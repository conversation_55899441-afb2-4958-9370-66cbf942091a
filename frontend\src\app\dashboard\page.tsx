'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  EnterpriseCard,
  EnterpriseNav,
  EnterpriseProgress,
  EnterpriseBadge,
  EnterpriseButton
} from '@/components/ui/enterprise-components'
import {
  StatusIndicator,
  LoadingSpinner,
  MetricCard,
  ThemeToggle
} from '@/components/providers/theme-provider'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    documents: { total: 0, processing: 0, failed: 0 },
    queries: { total: 0, today: 0, avg_response_time: 0 },
    analytics: { avg_trust_score: 0 },
    performance: { cpu: 0, memory: 0, storage: 0 }
  })
  const [loading, setLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    // Update time every second
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    
    // Load enterprise dashboard data
    const loadData = async () => {
      try {
        const response = await fetch('http://localhost:8000/api/v1/analytics/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          // Enterprise mock data
          setStats({
            documents: { total: 2847, processing: 12, failed: 3 },
            queries: { total: 18293, today: 156, avg_response_time: 1.2 },
            analytics: { avg_trust_score: 0.942 },
            performance: { cpu: 23, memory: 67, storage: 45 }
          })
        }
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
        setStats({
          documents: { total: 2847, processing: 12, failed: 3 },
          queries: { total: 18293, today: 156, avg_response_time: 1.2 },
          analytics: { avg_trust_score: 0.942 },
          performance: { cpu: 23, memory: 67, storage: 45 }
        })
      } finally {
        setLoading(false)
      }
    }

    setTimeout(loadData, 1000) // Realistic loading
    return () => clearInterval(timer)
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900 flex items-center justify-center">
        <EnterpriseCard className="text-center p-8 max-w-md mx-auto">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-foreground mb-2">Loading Enterprise Dashboard</h2>
          <p className="text-muted-foreground mb-4">Initializing real-time analytics and performance metrics</p>
          <div className="space-y-2">
            <EnterpriseProgress value={33} label="Loading system data" color="blue" />
            <EnterpriseProgress value={66} label="Connecting to services" color="purple" />
            <EnterpriseProgress value={90} label="Preparing dashboard" color="green" />
          </div>
        </EnterpriseCard>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Enterprise Navigation */}
      <EnterpriseNav
        brand={
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <Link href="/" className="text-2xl font-bold text-gradient">
                AIthentiqMind
              </Link>
            </div>
            <StatusIndicator status="online" label="Enterprise Dashboard" />
          </div>
        }
        links={[
          { href: '/demo', label: 'Live Demo' },
          { href: 'http://localhost:8000/docs', label: 'API Docs' },
          { href: '/dashboard', label: 'Dashboard', active: true }
        ]}
        actions={
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-4 text-xs text-muted-foreground">
              <StatusIndicator status="online" label="All Systems" />
              <StatusIndicator status="online" label="Performance" />
              <span className="text-muted-foreground">|</span>
              <span>{currentTime.toLocaleTimeString()}</span>
            </div>
            <ThemeToggle />
            <div className="flex items-center space-x-2">
              <span className="text-muted-foreground text-sm">Admin</span>
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">AD</span>
              </div>
            </div>
          </div>
        }
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enterprise KPI Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Documents"
            value={stats.documents?.total?.toLocaleString() || '0'}
            change="+12.5% this month"
            changeType="positive"
            icon={<span className="text-white text-xl">📄</span>}
            className="hover:shadow-glow-blue"
          />

          <MetricCard
            title="Queries Processed"
            value={stats.queries?.total?.toLocaleString() || '0'}
            change="+8.3% this week"
            changeType="positive"
            icon={<span className="text-white text-xl">💬</span>}
            className="hover:shadow-glow-green"
          />

          <MetricCard
            title="Trust Score"
            value={`${((stats.analytics?.avg_trust_score || 0) * 100).toFixed(1)}%`}
            change="+2.1% accuracy"
            changeType="positive"
            icon={<span className="text-white text-xl">🎯</span>}
            className="hover:shadow-glow-purple"
          />

          <MetricCard
            title="Response Time"
            value={`${(stats.queries?.avg_response_time || 0).toFixed(1)}s`}
            change="15% faster"
            changeType="positive"
            icon={<span className="text-white text-xl">⚡</span>}
            className="hover:shadow-glow-blue"
          />
        </div>

        {/* Enterprise Control Panel */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* System Performance */}
          <EnterpriseCard
            title="System Performance"
            subtitle="Real-time resource monitoring"
            className="lg:col-span-2"
            hover
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-medium text-muted-foreground">Resource Utilization</h4>
                <EnterpriseBadge variant="success" size="sm">
                  Optimal
                </EnterpriseBadge>
              </div>

              <EnterpriseProgress
                value={stats.performance?.cpu || 0}
                label="CPU Usage"
                color="blue"
                showValue
              />

              <EnterpriseProgress
                value={stats.performance?.memory || 0}
                label="Memory Usage"
                color="green"
                showValue
              />

              <EnterpriseProgress
                value={stats.performance?.storage || 0}
                label="Storage Usage"
                color="purple"
                showValue
              />

              <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border/50">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gradient">99.9%</div>
                  <div className="text-xs text-muted-foreground">Uptime</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gradient">1.2s</div>
                  <div className="text-xs text-muted-foreground">Avg Response</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gradient">0</div>
                  <div className="text-xs text-muted-foreground">Errors</div>
                </div>
              </div>
            </div>
          </EnterpriseCard>

          {/* Quick Actions */}
          <EnterpriseCard
            title="Quick Actions"
            subtitle="Enterprise controls"
            hover
          >
            <div className="space-y-4">
              <Link href="/demo">
                <EnterpriseButton variant="primary" className="w-full">
                  🚀 Launch Demo
                </EnterpriseButton>
              </Link>

              <EnterpriseButton
                variant="secondary"
                className="w-full"
                onClick={() => window.open('http://localhost:8000/docs', '_blank')}
              >
                📚 API Documentation
              </EnterpriseButton>

              <EnterpriseButton
                variant="outline"
                className="w-full"
                onClick={() => window.location.reload()}
              >
                🔄 Refresh Analytics
              </EnterpriseButton>

              <div className="pt-4 border-t border-border/50">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">System Status</span>
                  <StatusIndicator status="online" label="All Services" />
                </div>
                <div className="flex items-center justify-between text-sm mt-2">
                  <span className="text-muted-foreground">Last Updated</span>
                  <span className="text-foreground font-medium">{currentTime.toLocaleTimeString()}</span>
                </div>
              </div>
            </div>
          </EnterpriseCard>
        </div>

        {/* Enterprise Architecture Overview */}
        <div className="bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Enterprise Architecture</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg border border-blue-200/50">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🎨</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Frontend Layer</h4>
              <p className="text-sm text-gray-600 mb-3">Next.js 14 with TypeScript</p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• Real-time WebSocket connections</div>
                <div>• Progressive Web App (PWA)</div>
                <div>• Advanced UI components</div>
              </div>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg border border-green-200/50">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">⚙️</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Backend Services</h4>
              <p className="text-sm text-gray-600 mb-3">FastAPI with async/await</p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• PostgreSQL + Redis + Qdrant</div>
                <div>• OpenAI GPT-4 & Embeddings</div>
                <div>• Prometheus monitoring</div>
              </div>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg border border-purple-200/50">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">☁️</span>
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Infrastructure</h4>
              <p className="text-sm text-gray-600 mb-3">Kubernetes orchestration</p>
              <div className="space-y-1 text-xs text-gray-500">
                <div>• Docker containerization</div>
                <div>• GitHub Actions CI/CD</div>
                <div>• Auto-scaling & load balancing</div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
