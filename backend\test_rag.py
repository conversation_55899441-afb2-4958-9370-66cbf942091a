#!/usr/bin/env python3
"""
Simple test script for the RAG system
"""

import asyncio
import os
import tempfile
from pathlib import Path

# Add the app directory to the Python path
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.rag.engine import RAGEngine
from app.core.config import settings


async def test_rag_system():
    """Test the RAG system with a sample document"""
    print("🧪 Testing AIthentiqMind RAG System")
    print("=" * 50)
    
    try:
        # Initialize RAG engine
        print("1. Initializing RAG engine...")
        rag_engine = RAGEngine()
        
        # Health check
        print("2. Performing health check...")
        health = await rag_engine.health_check()
        print(f"   Status: {health['status']}")
        
        for component, status in health.get('components', {}).items():
            print(f"   - {component}: {status.get('status', 'unknown')}")
        
        if health['status'] != 'healthy':
            print("❌ RAG system is not healthy. Please check your configuration.")
            return False
        
        # Create a test document
        print("3. Creating test document...")
        test_content = """
        AIthentiqMind Platform Overview
        
        AIthentiqMind is an enterprise-ready RAG (Retrieval-Augmented Generation) SaaS platform 
        that combines n8n workflow orchestration with intelligent document-based question answering.
        
        Key Features:
        - Multi-tenant architecture with data isolation
        - Advanced document processing with LangChain
        - Vector search using Qdrant database
        - Trust scoring for response reliability
        - Workflow automation with n8n
        - Real-time chat interface
        - Enterprise security with Auth0
        
        The platform supports various document formats including PDF, DOCX, TXT, HTML, and Markdown.
        It uses OpenAI GPT-4 for response generation and text-embedding-ada-002 for vector embeddings.
        
        Trust scoring evaluates responses based on confidence, relevance, recency, source diversity,
        and answer quality to provide users with reliability indicators.
        """
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = f.name
        
        try:
            # Process the document
            print("4. Processing test document...")
            result = await rag_engine.process_document(
                file_path=temp_file_path,
                filename="test_document.txt",
                tenant_id="test-tenant",
                user_id="test-user"
            )
            
            if result['status'] == 'processed':
                print(f"   ✅ Document processed successfully!")
                print(f"   - Document ID: {result['document_id']}")
                print(f"   - Chunks created: {result['chunk_count']}")
                print(f"   - Processing time: {result['processing_time']:.2f}s")
            else:
                print(f"   ❌ Document processing failed: {result.get('error', 'Unknown error')}")
                return False
            
            # Test queries
            print("5. Testing RAG queries...")
            test_questions = [
                "What is AIthentiqMind?",
                "What document formats are supported?",
                "How does trust scoring work?",
                "What are the key features of the platform?"
            ]
            
            for i, question in enumerate(test_questions, 1):
                print(f"\n   Query {i}: {question}")
                
                query_result = await rag_engine.query_documents(
                    question=question,
                    tenant_id="test-tenant",
                    user_id="test-user",
                    top_k=3,
                    include_sources=True
                )
                
                print(f"   Answer: {query_result['answer'][:200]}...")
                print(f"   Trust Score: {query_result['trust_score']:.3f}")
                print(f"   Response Time: {query_result['response_time']:.2f}s")
                print(f"   Sources: {len(query_result.get('sources', []))}")
            
            print("\n6. Testing document cleanup...")
            cleanup_success = await rag_engine.delete_document("test-tenant", result['document_id'])
            if cleanup_success:
                print("   ✅ Document deleted successfully")
            else:
                print("   ⚠️ Document deletion failed")
            
            print("\n🎉 RAG system test completed successfully!")
            return True
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        
    except Exception as e:
        print(f"\n❌ RAG system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_individual_components():
    """Test individual RAG components"""
    print("\n🔧 Testing Individual Components")
    print("=" * 50)
    
    try:
        from app.services.vector.embeddings import create_embedding_service
        from app.services.vector.qdrant_store import create_qdrant_store
        from app.services.llm.providers import create_llm_service
        
        # Test embedding service
        print("1. Testing embedding service...")
        embedding_service = create_embedding_service()
        test_result = await embedding_service.test_connection()
        print(f"   Embedding service: {'✅ Working' if test_result else '❌ Failed'}")
        
        # Test vector store
        print("2. Testing vector store...")
        vector_store = create_qdrant_store()
        health = await vector_store.health_check()
        print(f"   Vector store: {'✅ Working' if health['status'] == 'healthy' else '❌ Failed'}")
        
        # Test LLM service
        print("3. Testing LLM service...")
        llm_service = create_llm_service()
        llm_test = await llm_service.test_connection()
        print(f"   LLM service: {'✅ Working' if llm_test else '❌ Failed'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Component testing failed: {e}")
        return False


def check_configuration():
    """Check if required configuration is present"""
    print("⚙️ Checking Configuration")
    print("=" * 50)
    
    required_configs = [
        ("OPENAI_API_KEY", settings.OPENAI_API_KEY),
        ("QDRANT_URL", settings.QDRANT_URL),
        ("DATABASE_URL", settings.DATABASE_URL),
        ("REDIS_URL", settings.REDIS_URL),
    ]
    
    all_good = True
    for name, value in required_configs:
        if value:
            print(f"✅ {name}: Configured")
        else:
            print(f"❌ {name}: Missing")
            all_good = False
    
    if not all_good:
        print("\n⚠️ Some required configuration is missing.")
        print("Please check your .env file and ensure all required variables are set.")
    
    return all_good


async def main():
    """Main test function"""
    print("🚀 AIthentiqMind RAG System Test Suite")
    print("=" * 60)
    
    # Check configuration
    if not check_configuration():
        return
    
    # Test individual components
    components_ok = await test_individual_components()
    
    if components_ok:
        # Test full RAG system
        await test_rag_system()
    else:
        print("\n❌ Component tests failed. Skipping full system test.")


if __name__ == "__main__":
    asyncio.run(main())
