# AIthentiqMind Production Deployment Guide

This guide provides step-by-step instructions for deploying AIthentiqMind to production using Kubernetes.

## 🚀 Quick Start

### Prerequisites
- Kubernetes cluster (1.24+)
- kubectl configured
- Helm 3.x installed
- Docker registry access
- Domain names configured

### 1. Initial Setup
```bash
# Clone repository
git clone <repository-url>
cd AIthentiqMind

# Make scripts executable (Linux/Mac)
chmod +x scripts/*.sh

# Run production setup
./scripts/setup-production.sh
```

### 2. Configure Secrets
Edit the generated secrets file:
```bash
# Update with your actual values
nano k8s/base/secrets-production.yaml
```

Required updates:
- `OPENAI_API_KEY`: Your OpenAI API key
- `AUTH0_DOMAIN`: Your Auth0 domain
- `AUTH0_CLIENT_ID`: Your Auth0 client ID
- `AUTH0_CLIENT_SECRET`: Your Auth0 client secret
- `AUTH0_AUDIENCE`: Your API domain

### 3. Configure Domains
Update domain names in:
```bash
# Update with your actual domains
nano k8s/base/configmap-production.yaml
nano k8s/base/ingress.yaml
```

### 4. Deploy Application
```bash
# Deploy everything
./scripts/deploy.sh

# Or deploy step by step
./scripts/deploy.sh infrastructure
./scripts/deploy.sh application
```

### 5. Verify Deployment
```bash
# Run health checks
./scripts/health-check.sh

# Check status
./scripts/deploy.sh status
```

## 📋 Detailed Deployment Steps

### Step 1: Infrastructure Setup

#### 1.1 Kubernetes Cluster
Ensure you have a Kubernetes cluster with:
- At least 4 CPU cores and 8GB RAM
- LoadBalancer support
- Persistent volume support
- Ingress controller capability

#### 1.2 Domain Configuration
Configure DNS records:
```
A    api.yourdomain.com     -> <LoadBalancer-IP>
A    app.yourdomain.com     -> <LoadBalancer-IP>
A    workflows.yourdomain.com -> <LoadBalancer-IP>
```

#### 1.3 SSL Certificates
The setup script configures Let's Encrypt automatically. Update the email in:
```yaml
# In cluster-issuer.yaml
email: <EMAIL>  # Your email
```

### Step 2: Application Configuration

#### 2.1 Auth0 Setup
1. Create Auth0 application (Single Page Application)
2. Configure callback URLs:
   - `https://app.yourdomain.com/api/auth/callback`
3. Configure logout URLs:
   - `https://app.yourdomain.com`
4. Create API with identifier: `https://api.yourdomain.com`

#### 2.2 OpenAI Configuration
1. Get API key from OpenAI
2. Set up billing and usage limits
3. Configure in secrets file

#### 2.3 Environment Variables
Key production settings in `configmap-production.yaml`:
```yaml
ENVIRONMENT: "production"
LOG_LEVEL: "INFO"
CORS_ORIGINS: "https://app.yourdomain.com"
ALLOWED_HOSTS: "api.yourdomain.com"
```

### Step 3: Deployment Process

#### 3.1 Infrastructure Components
```bash
# Deploy in order:
kubectl apply -f k8s/base/namespace.yaml
kubectl apply -f k8s/base/secrets-production.yaml
kubectl apply -f k8s/base/configmap-production.yaml
kubectl apply -f k8s/base/postgres.yaml
kubectl apply -f k8s/base/redis.yaml
kubectl apply -f k8s/base/qdrant.yaml
kubectl apply -f k8s/base/minio.yaml
kubectl apply -f k8s/base/n8n.yaml
```

#### 3.2 Application Components
```bash
# Deploy applications:
kubectl apply -f k8s/base/backend.yaml
kubectl apply -f k8s/base/frontend.yaml
kubectl apply -f k8s/base/ingress.yaml
```

#### 3.3 Database Migration
```bash
# Run migrations after backend is ready
kubectl exec -n aithentiqmind deployment/backend -- python -m alembic upgrade head
```

### Step 4: Monitoring Setup

#### 4.1 Prometheus & Grafana
```bash
# Install monitoring stack
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install monitoring prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace
```

#### 4.2 Application Metrics
Access Grafana dashboard:
- URL: `http://<grafana-service-ip>:3000`
- Username: `admin`
- Password: `admin123` (change after first login)

### Step 5: Backup Configuration

#### 5.1 Database Backups
Automated daily backups are configured via CronJob:
```bash
# Check backup status
kubectl get cronjobs -n backup
kubectl get jobs -n backup
```

#### 5.2 Volume Snapshots
Configure volume snapshots for persistent data:
```bash
# Create volume snapshot class
kubectl apply -f k8s/backup/volume-snapshot-class.yaml
```

## 🔧 Configuration Reference

### Resource Requirements

#### Minimum Production Setup
- **CPU**: 4 cores total
- **Memory**: 8GB RAM total
- **Storage**: 50GB persistent storage
- **Network**: LoadBalancer with SSL

#### Recommended Production Setup
- **CPU**: 8+ cores
- **Memory**: 16GB+ RAM
- **Storage**: 100GB+ SSD storage
- **Network**: CDN + LoadBalancer

### Service Ports
- **Frontend**: 3000 (internal), 80/443 (external)
- **Backend**: 8000 (internal), 80/443 (external via ingress)
- **PostgreSQL**: 5432 (internal only)
- **Redis**: 6379 (internal only)
- **Qdrant**: 6333 (internal only)
- **MinIO**: 9000 (internal only)
- **n8n**: 5678 (internal), 80/443 (external via ingress)

### Environment-Specific Settings

#### Production
```yaml
ENVIRONMENT: production
LOG_LEVEL: INFO
DEBUG: false
WORKERS: 4
```

#### Staging
```yaml
ENVIRONMENT: staging
LOG_LEVEL: DEBUG
DEBUG: true
WORKERS: 2
```

## 🔍 Health Monitoring

### Health Check Endpoints
- **Backend**: `https://api.yourdomain.com/health`
- **Frontend**: `https://app.yourdomain.com/api/health`

### Monitoring Commands
```bash
# Quick health check
./scripts/health-check.sh quick

# Full health check
./scripts/health-check.sh all

# Generate health report
./scripts/health-check.sh report
```

### Key Metrics to Monitor
- Response time < 2 seconds
- Trust score > 70%
- Error rate < 1%
- CPU usage < 80%
- Memory usage < 80%
- Disk usage < 80%

## 🚨 Troubleshooting

### Common Issues

#### 1. Pods Not Starting
```bash
# Check pod status
kubectl get pods -n aithentiqmind

# Check pod logs
kubectl logs -f deployment/backend -n aithentiqmind

# Describe pod for events
kubectl describe pod <pod-name> -n aithentiqmind
```

#### 2. Database Connection Issues
```bash
# Test database connectivity
kubectl exec -n aithentiqmind deployment/postgres -- pg_isready

# Check database logs
kubectl logs -f deployment/postgres -n aithentiqmind
```

#### 3. SSL Certificate Issues
```bash
# Check certificate status
kubectl get certificates -n aithentiqmind

# Check cert-manager logs
kubectl logs -f deployment/cert-manager -n cert-manager
```

#### 4. Ingress Issues
```bash
# Check ingress status
kubectl describe ingress aithentiqmind-ingress -n aithentiqmind

# Check ingress controller logs
kubectl logs -f deployment/ingress-nginx-controller -n ingress-nginx
```

### Recovery Procedures

#### Rollback Deployment
```bash
# Rollback to previous version
./scripts/deploy.sh rollback

# Or manually rollback specific deployment
kubectl rollout undo deployment/backend -n aithentiqmind
```

#### Restore from Backup
```bash
# List available backups
kubectl exec -n backup deployment/backup-manager -- ls -la /backup

# Restore specific backup
kubectl exec -n backup deployment/backup-manager -- ./restore.sh backup-20240101-020000.sql
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale backend
kubectl scale deployment backend --replicas=5 -n aithentiqmind

# Scale frontend
kubectl scale deployment frontend --replicas=3 -n aithentiqmind
```

### Auto-scaling
```bash
# Enable HPA
kubectl autoscale deployment backend --cpu-percent=70 --min=2 --max=10 -n aithentiqmind
```

### Database Scaling
For high-traffic scenarios:
1. Configure read replicas
2. Implement connection pooling
3. Add Redis caching layer

## 🔐 Security

### Network Security
- Network policies restrict inter-pod communication
- Ingress controller handles SSL termination
- Internal services not exposed externally

### Data Security
- Secrets encrypted at rest
- TLS for all external communication
- Regular security updates

### Access Control
- RBAC for Kubernetes access
- Auth0 for application access
- Audit logging enabled

## 📞 Support

### Getting Help
1. Check logs: `kubectl logs -f deployment/<service> -n aithentiqmind`
2. Run health checks: `./scripts/health-check.sh`
3. Review documentation
4. Contact support team

### Maintenance Windows
- **Preferred**: Sunday 2-4 AM UTC
- **Emergency**: Any time with 1-hour notice
- **Updates**: Monthly security patches

---

## 🎉 Congratulations!

Your AIthentiqMind platform is now deployed and ready for production use!

**Next Steps:**
1. Configure monitoring alerts
2. Set up backup verification
3. Train your team on the platform
4. Start onboarding users

**Access URLs:**
- **Application**: https://app.yourdomain.com
- **API**: https://api.yourdomain.com
- **Workflows**: https://workflows.yourdomain.com
- **Monitoring**: http://grafana-service-ip:3000
