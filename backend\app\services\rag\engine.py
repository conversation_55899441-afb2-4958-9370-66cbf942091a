"""
Main RAG engine that orchestrates document processing, embedding, and query answering
"""

import asyncio
import time
from typing import List, Dict, Any, Optional, AsyncGenerator, Tuple
import uuid

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import DocumentProcessing<PERSON>rror, VectorStoreError, LLMError
from app.services.document.processor import DocumentProcessor
from app.services.vector.embeddings import create_embedding_service
from app.services.vector.qdrant_store import create_qdrant_store
from app.services.llm.providers import create_llm_service
from app.services.rag.trust_scorer import TrustScorer
from app.services.vector_store import get_vector_store
from app.services.embeddings import get_embedding_service as get_new_embedding_service
from app.services.chunking import get_document_chunker

logger = get_logger(__name__)


class RAGEngine:
    """Main RAG engine for document processing and query answering"""
    
    def __init__(self):
        self.document_processor = DocumentProcessor()
        self.embedding_service = create_embedding_service()
        self.vector_store = create_qdrant_store()
        self.llm_service = create_llm_service()
        self.trust_scorer = TrustScorer()
        
        logger.info("RAG Engine initialized")
    
    async def process_document(
        self,
        file_path: str,
        filename: str,
        tenant_id: str,
        user_id: str,
        document_id: str = None
    ) -> Dict[str, Any]:
        """
        Process a document through the complete RAG pipeline
        
        Args:
            file_path: Path to the uploaded file
            filename: Original filename
            tenant_id: Tenant identifier
            user_id: User identifier
            document_id: Optional document ID
            
        Returns:
            Processing result with document metadata and status
        """
        start_time = time.time()
        
        try:
            logger.info(
                "Starting document processing",
                filename=filename,
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            # Step 1: Validate file
            is_valid, error_msg = await self.document_processor.validate_file(file_path, filename)
            if not is_valid:
                raise DocumentProcessingError(f"File validation failed: {error_msg}")
            
            # Step 2: Extract text and create chunks
            processing_result = await self.document_processor.process_file(
                file_path=file_path,
                filename=filename,
                tenant_id=tenant_id,
                user_id=user_id,
                document_id=document_id
            )
            
            # Step 3: Generate embeddings for chunks
            chunks_with_embeddings = await self.embedding_service.embed_chunks(
                processing_result["chunks"]
            )
            
            # Step 4: Ensure tenant collection exists
            embedding_dimension = self.embedding_service.provider.get_dimension()
            await self.vector_store.create_collection(tenant_id, embedding_dimension)
            
            # Step 5: Store chunks in vector database
            point_ids = await self.vector_store.add_documents(
                tenant_id=tenant_id,
                chunks=chunks_with_embeddings
            )
            
            processing_time = time.time() - start_time
            
            result = {
                "document_id": processing_result["document_id"],
                "filename": processing_result["filename"],
                "mime_type": processing_result["mime_type"],
                "file_size": processing_result["file_size"],
                "content_hash": processing_result["content_hash"],
                "chunk_count": processing_result["chunk_count"],
                "metadata": processing_result["metadata"],
                "point_ids": point_ids,
                "processing_time": processing_time,
                "status": "processed",
                "tenant_id": tenant_id,
                "user_id": user_id
            }
            
            logger.info(
                "Document processing completed",
                document_id=result["document_id"],
                chunk_count=result["chunk_count"],
                processing_time=processing_time
            )
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(
                "Document processing failed",
                filename=filename,
                error=str(e),
                processing_time=processing_time
            )
            
            return {
                "document_id": document_id or str(uuid.uuid4()),
                "filename": filename,
                "status": "failed",
                "error": str(e),
                "processing_time": processing_time,
                "tenant_id": tenant_id,
                "user_id": user_id
            }
    
    async def query_documents(
        self,
        question: str,
        tenant_id: str,
        user_id: str,
        document_ids: Optional[List[str]] = None,
        top_k: int = None,
        include_sources: bool = True,
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        Query documents using RAG
        
        Args:
            question: User question
            tenant_id: Tenant identifier
            user_id: User identifier
            document_ids: Optional filter by specific documents
            top_k: Number of context chunks to retrieve
            include_sources: Whether to include source information
            stream: Whether to stream the response
            
        Returns:
            Query result with answer, sources, and trust score
        """
        start_time = time.time()
        query_id = str(uuid.uuid4())
        
        try:
            logger.info(
                "Starting RAG query",
                query_id=query_id,
                question=question[:100],
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            # Step 1: Generate query embedding
            query_embedding = await self.embedding_service.embed_query(question)
            
            # Step 2: Retrieve similar chunks
            top_k = top_k or settings.RETRIEVAL_TOP_K
            similar_chunks = await self.vector_store.search_similar(
                tenant_id=tenant_id,
                query_embedding=query_embedding,
                limit=top_k,
                document_ids=document_ids
            )
            
            if not similar_chunks:
                return {
                    "query_id": query_id,
                    "question": question,
                    "answer": "I don't have any relevant information to answer your question.",
                    "sources": [],
                    "trust_score": 0.0,
                    "response_time": time.time() - start_time,
                    "tenant_id": tenant_id,
                    "user_id": user_id
                }
            
            # Step 3: Generate response
            if stream:
                # For streaming, we'll return a generator
                return await self._generate_streaming_response(
                    query_id=query_id,
                    question=question,
                    similar_chunks=similar_chunks,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    start_time=start_time,
                    include_sources=include_sources
                )
            else:
                answer = await self.llm_service.generate_rag_response(
                    question=question,
                    context_chunks=similar_chunks
                )
                
                # Step 4: Calculate trust score
                trust_score = await self.trust_scorer.calculate_trust_score(
                    question=question,
                    answer=answer,
                    context_chunks=similar_chunks
                )
                
                response_time = time.time() - start_time
                
                result = {
                    "query_id": query_id,
                    "question": question,
                    "answer": answer,
                    "trust_score": trust_score["overall_score"],
                    "trust_factors": trust_score,
                    "response_time": response_time,
                    "tenant_id": tenant_id,
                    "user_id": user_id
                }
                
                if include_sources:
                    result["sources"] = self._format_sources(similar_chunks)
                
                logger.info(
                    "RAG query completed",
                    query_id=query_id,
                    trust_score=trust_score["overall_score"],
                    response_time=response_time
                )
                
                return result
                
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(
                "RAG query failed",
                query_id=query_id,
                error=str(e),
                response_time=response_time
            )
            
            return {
                "query_id": query_id,
                "question": question,
                "answer": f"I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "trust_score": 0.0,
                "error": str(e),
                "response_time": response_time,
                "tenant_id": tenant_id,
                "user_id": user_id
            }
    
    async def _generate_streaming_response(
        self,
        query_id: str,
        question: str,
        similar_chunks: List[Dict[str, Any]],
        tenant_id: str,
        user_id: str,
        start_time: float,
        include_sources: bool
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response for RAG query"""
        try:
            # First yield metadata
            yield {
                "type": "metadata",
                "query_id": query_id,
                "question": question,
                "sources": self._format_sources(similar_chunks) if include_sources else [],
                "tenant_id": tenant_id,
                "user_id": user_id
            }
            
            # Stream the answer
            full_answer = ""
            async for chunk in self.llm_service.generate_streaming_rag_response(
                question=question,
                context_chunks=similar_chunks
            ):
                full_answer += chunk
                yield {
                    "type": "content",
                    "chunk": chunk
                }
            
            # Calculate trust score and yield final result
            trust_score = await self.trust_scorer.calculate_trust_score(
                question=question,
                answer=full_answer,
                context_chunks=similar_chunks
            )
            
            response_time = time.time() - start_time
            
            yield {
                "type": "complete",
                "trust_score": trust_score["overall_score"],
                "trust_factors": trust_score,
                "response_time": response_time
            }
            
        except Exception as e:
            yield {
                "type": "error",
                "error": str(e),
                "response_time": time.time() - start_time
            }
    
    def _format_sources(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format source information from chunks"""
        sources = []
        
        for chunk in chunks:
            source = {
                "document_id": chunk.get("document_id"),
                "chunk_id": chunk.get("chunk_id"),
                "content": chunk.get("content", "")[:500] + "..." if len(chunk.get("content", "")) > 500 else chunk.get("content", ""),
                "relevance_score": chunk.get("score", 0.0),
                "metadata": chunk.get("metadata", {})
            }
            sources.append(source)
        
        return sources
    
    async def delete_document(self, tenant_id: str, document_id: str) -> bool:
        """Delete a document and all its chunks from the vector store"""
        try:
            await self.vector_store.delete_document(tenant_id, document_id)
            logger.info(f"Deleted document {document_id} for tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            return False
    
    async def get_document_chunks(
        self, 
        tenant_id: str, 
        document_id: str
    ) -> List[Dict[str, Any]]:
        """Get all chunks for a specific document"""
        try:
            chunks = await self.vector_store.get_document_chunks(tenant_id, document_id)
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to get document chunks: {e}")
            raise VectorStoreError(f"Failed to retrieve document chunks: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on all RAG components"""
        health_status = {
            "status": "healthy",
            "components": {}
        }
        
        # Check embedding service
        try:
            embedding_test = await self.embedding_service.test_connection()
            health_status["components"]["embedding_service"] = {
                "status": "healthy" if embedding_test else "unhealthy",
                "info": self.embedding_service.get_embedding_info()
            }
        except Exception as e:
            health_status["components"]["embedding_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # Check vector store
        try:
            vector_health = await self.vector_store.health_check()
            health_status["components"]["vector_store"] = vector_health
            if vector_health["status"] != "healthy":
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["components"]["vector_store"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        # Check LLM service
        try:
            llm_test = await self.llm_service.test_connection()
            health_status["components"]["llm_service"] = {
                "status": "healthy" if llm_test else "unhealthy",
                "info": self.llm_service.get_provider_info()
            }
        except Exception as e:
            health_status["components"]["llm_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"
        
        return health_status


# Global RAG engine instance
_rag_engine = None

def get_rag_engine() -> RAGEngine:
    """Get or create RAG engine instance"""
    global _rag_engine
    if _rag_engine is None:
        _rag_engine = RAGEngine()
    return _rag_engine
