"""
Authentication and authorization system
"""

import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.db.database import get_db
from app.models.user import User
from app.models.tenant import Tenant

logger = get_logger(__name__)
security = HTTPBearer()


class AuthService:
    """Authentication service for JWT token validation and user management"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
    
    def create_access_token(self, data: Dict[str, Any]) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode.update({"exp": expire})
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.JWTError:
            raise AuthenticationError("Invalid token")
    
    async def get_user_from_token(self, token: str, db: AsyncSession) -> User:
        """Get user from JWT token"""
        try:
            payload = self.verify_token(token)
            user_id = payload.get("sub")
            
            if not user_id:
                raise AuthenticationError("Invalid token payload")
            
            # Get user from database
            result = await db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                raise AuthenticationError("User not found")
            
            if not user.is_active:
                raise AuthenticationError("User account is disabled")
            
            return user
            
        except Exception as e:
            logger.error(f"Token validation failed: {e}")
            raise AuthenticationError("Authentication failed")


# Global auth service instance
auth_service = AuthService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get current authenticated user"""
    try:
        token = credentials.credentials
        user = await auth_service.get_user_from_token(token, db)
        return user
    except AuthenticationError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Dependency to get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_current_tenant(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Tenant:
    """Dependency to get current user's tenant"""
    try:
        result = await db.execute(select(Tenant).where(Tenant.id == current_user.tenant_id))
        tenant = result.scalar_one_or_none()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        if not tenant.is_active:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Tenant account is disabled"
            )
        
        return tenant
        
    except Exception as e:
        logger.error(f"Failed to get tenant: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tenant information"
        )


def require_permissions(required_permissions: List[str]):
    """Decorator to require specific permissions"""
    def permission_checker(current_user: User = Depends(get_current_active_user)):
        user_permissions = current_user.get_permissions()
        
        for permission in required_permissions:
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission required: {permission}"
                )
        
        return current_user
    
    return permission_checker


def require_admin(current_user: User = Depends(get_current_active_user)) -> User:
    """Dependency to require admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


class TenantContext:
    """Context manager for tenant-specific operations"""
    
    def __init__(self, tenant: Tenant, user: User):
        self.tenant = tenant
        self.user = user
        self.tenant_id = tenant.id
        self.user_id = user.id
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission in tenant context"""
        return permission in self.user.get_permissions()
    
    def require_permission(self, permission: str):
        """Require specific permission in tenant context"""
        if not self.has_permission(permission):
            raise AuthorizationError(f"Permission required: {permission}")
    
    def get_context_info(self) -> Dict[str, Any]:
        """Get context information for logging"""
        return {
            "tenant_id": str(self.tenant_id),
            "tenant_name": self.tenant.name,
            "user_id": str(self.user_id),
            "user_email": self.user.email,
            "user_roles": self.user.get_roles()
        }


async def get_tenant_context(
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant)
) -> TenantContext:
    """Dependency to get tenant context"""
    return TenantContext(current_tenant, current_user)


# Auth0 Integration
class Auth0Service:
    """Auth0 integration service"""
    
    def __init__(self):
        self.domain = settings.AUTH0_DOMAIN
        self.client_id = settings.AUTH0_CLIENT_ID
        self.client_secret = settings.AUTH0_CLIENT_SECRET
        self.audience = settings.AUTH0_AUDIENCE
    
    async def verify_auth0_token(self, token: str) -> Dict[str, Any]:
        """Verify Auth0 JWT token"""
        try:
            import httpx
            
            # Get Auth0 public keys
            jwks_url = f"https://{self.domain}/.well-known/jwks.json"
            async with httpx.AsyncClient() as client:
                response = await client.get(jwks_url)
                jwks = response.json()
            
            # Verify token
            from jose import jwt as jose_jwt
            
            unverified_header = jose_jwt.get_unverified_header(token)
            rsa_key = {}
            
            for key in jwks["keys"]:
                if key["kid"] == unverified_header["kid"]:
                    rsa_key = {
                        "kty": key["kty"],
                        "kid": key["kid"],
                        "use": key["use"],
                        "n": key["n"],
                        "e": key["e"]
                    }
            
            if rsa_key:
                payload = jose_jwt.decode(
                    token,
                    rsa_key,
                    algorithms=["RS256"],
                    audience=self.audience,
                    issuer=f"https://{self.domain}/"
                )
                return payload
            else:
                raise AuthenticationError("Unable to find appropriate key")
                
        except Exception as e:
            logger.error(f"Auth0 token verification failed: {e}")
            raise AuthenticationError("Invalid Auth0 token")
    
    async def get_or_create_user(self, auth0_payload: Dict[str, Any], db: AsyncSession) -> User:
        """Get or create user from Auth0 payload"""
        try:
            email = auth0_payload.get("email")
            if not email:
                raise AuthenticationError("Email not found in Auth0 token")
            
            # Check if user exists
            result = await db.execute(select(User).where(User.email == email))
            user = result.scalar_one_or_none()
            
            if not user:
                # Create new user
                # TODO: Implement user creation logic
                # This would involve creating a default tenant or assigning to existing tenant
                pass
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to get/create user from Auth0: {e}")
            raise AuthenticationError("User creation failed")


# Global Auth0 service instance
auth0_service = Auth0Service() if settings.AUTH0_DOMAIN else None


async def get_auth0_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Dependency to get user from Auth0 token"""
    if not auth0_service:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Auth0 not configured"
        )
    
    try:
        token = credentials.credentials
        auth0_payload = await auth0_service.verify_auth0_token(token)
        user = await auth0_service.get_or_create_user(auth0_payload, db)
        return user
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
