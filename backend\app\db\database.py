"""
Database configuration and connection management
"""

from typing import As<PERSON><PERSON><PERSON>ator
import asyncio

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# Create async engine with optimized connection pooling
async_engine = create_async_engine(
    str(settings.DATABASE_URL),
    echo=settings.DEBUG,
    future=True,
    poolclass=NullPool if settings.TESTING else None,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,  # Number of connections to maintain
    max_overflow=30,  # Additional connections beyond pool_size
    pool_timeout=30,  # Timeout for getting connection from pool
    connect_args={
        "server_settings": {
            "application_name": "aithentiqmind_backend",
            "jit": "off",  # Disable JIT for better performance with short queries
        },
        "command_timeout": 60,
    } if not settings.TESTING else {}
)

# Create sync engine for migrations
sync_engine = create_engine(
    str(settings.DATABASE_URL).replace("+asyncpg", ""),
    echo=settings.DEBUG,
    pool_pre_ping=True,
    pool_recycle=300,
)

# Create session makers
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

SessionLocal = sessionmaker(
    sync_engine,
    autocommit=False,
    autoflush=False,
)

# Create base class for models
Base = declarative_base()

# Metadata for migrations
metadata = MetaData()


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


def get_sync_session():
    """Get sync database session"""
    session = SessionLocal()
    try:
        yield session
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()


async def create_tables():
    """Create all database tables"""
    try:
        async with async_engine.begin() as conn:
            # Import all models to ensure they're registered
            from app.models import (
                tenant, user, document, query, workflow, 
                audit, trust_score, data_source
            )
            
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


async def drop_tables():
    """Drop all database tables (for testing)"""
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


async def check_database_connection():
    """Check if database connection is working"""
    try:
        async with AsyncSessionLocal() as session:
            await session.execute("SELECT 1")
            logger.info("Database connection successful")
            return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


class DatabaseManager:
    """Database management utilities"""
    
    @staticmethod
    async def health_check() -> dict:
        """Perform database health check"""
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute("SELECT version()")
                version = result.scalar()
                
                # Check connection pool status
                pool = async_engine.pool
                pool_status = {
                    "size": pool.size(),
                    "checked_in": pool.checkedin(),
                    "checked_out": pool.checkedout(),
                }
                
                return {
                    "status": "healthy",
                    "version": version,
                    "pool": pool_status
                }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    @staticmethod
    async def get_stats() -> dict:
        """Get database statistics"""
        try:
            async with AsyncSessionLocal() as session:
                # Get table sizes
                result = await session.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables 
                    WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
                    ORDER BY size_bytes DESC;
                """)
                
                tables = []
                for row in result:
                    tables.append({
                        "schema": row[0],
                        "table": row[1],
                        "size": row[2],
                        "size_bytes": row[3]
                    })
                
                # Get database size
                db_result = await session.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as db_size,
                           pg_database_size(current_database()) as db_size_bytes;
                """)
                db_size_row = db_result.first()
                
                return {
                    "database_size": db_size_row[0],
                    "database_size_bytes": db_size_row[1],
                    "tables": tables
                }
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {"error": str(e)}


# Dependency for FastAPI
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency to get database session"""
    async for session in get_async_session():
        yield session


# Context manager for database transactions
class DatabaseTransaction:
    """Context manager for database transactions"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.session.rollback()
            logger.error(f"Transaction rolled back due to: {exc_val}")
        else:
            await self.session.commit()
            logger.debug("Transaction committed successfully")


# Utility functions
async def execute_raw_sql(sql: str, params: dict = None) -> list:
    """Execute raw SQL query"""
    async with AsyncSessionLocal() as session:
        result = await session.execute(sql, params or {})
        return result.fetchall()


async def get_tenant_schema(tenant_id: str) -> str:
    """Get schema name for tenant"""
    return f"tenant_{tenant_id.replace('-', '_')}"


async def create_tenant_schema(tenant_id: str):
    """Create schema for new tenant"""
    schema_name = await get_tenant_schema(tenant_id)
    
    async with AsyncSessionLocal() as session:
        await session.execute(f"CREATE SCHEMA IF NOT EXISTS {schema_name}")
        await session.commit()
        
        logger.info(f"Created schema for tenant: {tenant_id}")


async def drop_tenant_schema(tenant_id: str):
    """Drop schema for tenant"""
    schema_name = await get_tenant_schema(tenant_id)

    async with AsyncSessionLocal() as session:
        await session.execute(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE")
        await session.commit()

        logger.info(f"Dropped schema for tenant: {tenant_id}")


class TenantAwareSession:
    """Tenant-aware database session manager"""

    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.schema_name = f"tenant_{tenant_id.replace('-', '_')}"

    async def __aenter__(self) -> AsyncSession:
        """Enter async context and set tenant schema"""
        self.session = AsyncSessionLocal()

        # Set search path to tenant schema
        await self.session.execute(f"SET search_path TO {self.schema_name}, public")

        return self.session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context and cleanup"""
        if exc_type is not None:
            await self.session.rollback()
            logger.error(f"Tenant session rolled back for {self.tenant_id}: {exc_val}")
        else:
            await self.session.commit()

        await self.session.close()


async def get_tenant_session(tenant_id: str) -> TenantAwareSession:
    """Get tenant-aware database session"""
    return TenantAwareSession(tenant_id)


class ConnectionPoolMonitor:
    """Monitor database connection pool health"""

    @staticmethod
    async def get_pool_stats() -> dict:
        """Get detailed connection pool statistics"""
        pool = async_engine.pool

        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
            "total_connections": pool.size() + pool.overflow(),
            "utilization_percent": round(
                (pool.checkedout() / (pool.size() + pool.overflow())) * 100, 2
            ) if (pool.size() + pool.overflow()) > 0 else 0
        }

    @staticmethod
    async def check_pool_health() -> dict:
        """Check if connection pool is healthy"""
        try:
            stats = await ConnectionPoolMonitor.get_pool_stats()

            # Check for potential issues
            warnings = []
            if stats["utilization_percent"] > 80:
                warnings.append("High connection pool utilization")

            if stats["invalid"] > 0:
                warnings.append(f"{stats['invalid']} invalid connections in pool")

            return {
                "status": "healthy" if not warnings else "warning",
                "stats": stats,
                "warnings": warnings
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }


async def optimize_database_settings():
    """Apply database optimization settings"""
    optimizations = [
        "SET shared_preload_libraries = 'pg_stat_statements'",
        "SET log_statement = 'none'",  # Reduce logging overhead
        "SET log_min_duration_statement = 1000",  # Log slow queries only
        "SET effective_cache_size = '1GB'",
        "SET random_page_cost = 1.1",  # Assume SSD storage
    ]

    async with AsyncSessionLocal() as session:
        for sql in optimizations:
            try:
                await session.execute(sql)
                logger.debug(f"Applied optimization: {sql}")
            except Exception as e:
                logger.warning(f"Failed to apply optimization {sql}: {e}")

        await session.commit()


async def create_database_indexes():
    """Create performance indexes"""
    indexes = [
        # Tenant-based indexes
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_tenant_id ON users(tenant_id)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_queries_tenant_id ON queries(tenant_id)",

        # Performance indexes
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_status ON documents(status)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_created_at ON documents(created_at DESC)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_queries_created_at ON queries(created_at DESC)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_auth0_id ON users(auth0_user_id)",

        # Composite indexes for common queries
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_documents_tenant_status ON documents(tenant_id, status)",
        "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_queries_tenant_user ON queries(tenant_id, user_id)",
    ]

    async with AsyncSessionLocal() as session:
        for index_sql in indexes:
            try:
                await session.execute(index_sql)
                logger.info(f"Created index: {index_sql.split()[-1]}")
            except Exception as e:
                logger.warning(f"Failed to create index: {e}")

        await session.commit()
