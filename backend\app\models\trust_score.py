"""
Trust score model for tracking RAG response reliability
"""

from typing import Dict, Any, List
from sqlalchemy import Column, String, DateTime, Float, ForeignKey, JSON, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class TrustScore(Base):
    __tablename__ = "trust_scores"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_id = Column(UUID(as_uuid=True), ForeignKey("queries.id"), nullable=False, unique=True, index=True)

    # Overall trust score (0.0 to 1.0)
    overall_score = Column(Float, nullable=False, index=True)

    # Individual factor scores (0.0 to 1.0)
    confidence_score = Column(Float, nullable=True)  # Vector similarity confidence
    relevance_score = Column(Float, nullable=True)   # Semantic relevance
    recency_score = Column(Float, nullable=True)     # Document freshness
    source_diversity_score = Column(Float, nullable=True)  # Multiple source validation
    answer_quality_score = Column(Float, nullable=True)    # Response coherence

    # Detailed factors and metadata
    factors = Column(JSON, default=dict)  # Detailed scoring factors
    explanation = Column(Text, nullable=True)  # Human-readable explanation

    # Scoring metadata
    scoring_model_version = Column(String, default="1.0")
    computation_time_ms = Column(Float, nullable=True)

    # Timestamp
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    query = relationship("Query", back_populates="trust_score_record")

    def get_factors(self) -> Dict[str, Any]:
        """Get detailed scoring factors"""
        return self.factors or {}

    def set_factors(self, factors: Dict[str, Any]):
        """Set detailed scoring factors"""
        self.factors = factors

    def get_score_breakdown(self) -> Dict[str, Any]:
        """Get detailed score breakdown"""
        return {
            "overall_score": self.overall_score,
            "factor_scores": {
                "confidence": self.confidence_score,
                "relevance": self.relevance_score,
                "recency": self.recency_score,
                "source_diversity": self.source_diversity_score,
                "answer_quality": self.answer_quality_score
            },
            "factors": self.get_factors(),
            "explanation": self.explanation,
            "model_version": self.scoring_model_version
        }

    def get_trust_level(self) -> str:
        """Get human-readable trust level"""
        if self.overall_score >= 0.8:
            return "high"
        elif self.overall_score >= 0.6:
            return "medium"
        elif self.overall_score >= 0.4:
            return "low"
        else:
            return "very_low"

    def get_trust_indicators(self) -> List[str]:
        """Get list of trust indicators"""
        indicators = []

        if self.confidence_score and self.confidence_score >= 0.8:
            indicators.append("high_confidence")

        if self.relevance_score and self.relevance_score >= 0.8:
            indicators.append("highly_relevant")

        if self.source_diversity_score and self.source_diversity_score >= 0.7:
            indicators.append("multiple_sources")

        if self.recency_score and self.recency_score >= 0.7:
            indicators.append("recent_information")

        if self.answer_quality_score and self.answer_quality_score >= 0.8:
            indicators.append("high_quality_response")

        return indicators

    def to_dict(self) -> Dict[str, Any]:
        """Convert trust score to dictionary"""
        return {
            "id": str(self.id),
            "query_id": str(self.query_id),
            "overall_score": self.overall_score,
            "trust_level": self.get_trust_level(),
            "score_breakdown": self.get_score_breakdown(),
            "trust_indicators": self.get_trust_indicators(),
            "explanation": self.explanation,
            "computation_time_ms": self.computation_time_ms,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def create_score(
        cls,
        query_id: str,
        overall_score: float,
        confidence_score: float = None,
        relevance_score: float = None,
        recency_score: float = None,
        source_diversity_score: float = None,
        answer_quality_score: float = None,
        factors: Dict[str, Any] = None,
        explanation: str = None,
        computation_time_ms: float = None
    ) -> "TrustScore":
        """Factory method to create trust score"""
        return cls(
            query_id=query_id,
            overall_score=overall_score,
            confidence_score=confidence_score,
            relevance_score=relevance_score,
            recency_score=recency_score,
            source_diversity_score=source_diversity_score,
            answer_quality_score=answer_quality_score,
            factors=factors or {},
            explanation=explanation,
            computation_time_ms=computation_time_ms
        )
