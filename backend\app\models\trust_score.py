"""
Trust score model
"""

from sqlalchemy import Column, String, DateTime, Float, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from app.db.database import Base


class TrustScore(Base):
    __tablename__ = "trust_scores"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    query_id = Column(UUID(as_uuid=True), ForeignKey("queries.id"), nullable=False)
    overall_score = Column(Float, nullable=False)
    confidence_score = Column(Float)
    relevance_score = Column(Float)
    recency_score = Column(Float)
    source_diversity_score = Column(Float)
    factors = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
