"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/page",{

/***/ "(app-pages-browser)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DemoPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction DemoPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: \"user\",\n            content: \"What is artificial intelligence?\",\n            timestamp: \"10:30 AM\"\n        },\n        {\n            id: 2,\n            type: \"assistant\",\n            content: \"Artificial Intelligence (AI) is a broad field of computer science focused on creating systems that can perform tasks typically requiring human intelligence, such as learning, reasoning, problem-solving, and understanding natural language.\",\n            sources: [\n                {\n                    title: \"AI and Machine Learning Guide\",\n                    relevance: 0.95\n                }\n            ],\n            trustScore: 0.92,\n            timestamp: \"10:30 AM\"\n        }\n    ]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: messages.length + 1,\n            type: \"user\",\n            content: newMessage,\n            timestamp: new Date().toLocaleTimeString([], {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            })\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        // Simulate API call\n        try {\n            const response = await fetch(\"http://localhost:8000/api/v1/queries/ask\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    question: newMessage,\n                    include_sources: true,\n                    top_k: 5\n                })\n            });\n            const data = await response.json();\n            // Add assistant response\n            const assistantMessage = {\n                id: messages.length + 2,\n                type: \"assistant\",\n                content: data.answer,\n                sources: data.sources,\n                trustScore: data.trust_score,\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                })\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error:\", error);\n            // Add error message\n            const errorMessage = {\n                id: messages.length + 2,\n                type: \"assistant\",\n                content: \"Sorry, I encountered an error processing your request. Please try again.\",\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                })\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        }\n        setNewMessage(\"\");\n    };\n    const mockDocuments = [\n        {\n            id: 1,\n            title: \"AI and Machine Learning Guide\",\n            status: \"Processed\",\n            chunks: 15\n        },\n        {\n            id: 2,\n            title: \"Enterprise RAG Implementation\",\n            status: \"Processed\",\n            chunks: 22\n        },\n        {\n            id: 3,\n            title: \"Data Science Handbook\",\n            status: \"Processing\",\n            chunks: 0\n        }\n    ];\n    const mockAnalytics = {\n        totalQueries: 1247,\n        avgTrustScore: 0.85,\n        totalDocuments: 23,\n        activeUsers: 12\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: \"AIthentiqMind\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Demo User\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white text-sm font-medium\",\n                                            children: \"DU\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-64 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"chat\"),\n                                        className: \"w-full flex items-center px-4 py-2 text-sm font-medium rounded-md \".concat(activeTab === \"chat\" ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-lg\",\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Chat\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"documents\"),\n                                        className: \"w-full flex items-center px-4 py-2 text-sm font-medium rounded-md \".concat(activeTab === \"documents\" ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-lg\",\n                                                children: \"\\uD83D\\uDCC4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Documents\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"analytics\"),\n                                        className: \"w-full flex items-center px-4 py-2 text-sm font-medium rounded-md \".concat(activeTab === \"analytics\" ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-lg\",\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Analytics\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(\"upload\"),\n                                        className: \"w-full flex items-center px-4 py-2 text-sm font-medium rounded-md \".concat(activeTab === \"upload\" ? \"bg-blue-100 text-blue-700\" : \"text-gray-600 hover:bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-lg\",\n                                                children: \"⬆️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Upload\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                activeTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"AI Assistant\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Ask questions about your documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-96 overflow-y-auto p-6 space-y-4\",\n                                            children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.type === \"user\" ? \"justify-end\" : \"justify-start\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(message.type === \"user\" ? \"bg-blue-500 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.sources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Sources:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    message.sources.map((source, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-blue-600\",\n                                                                            children: [\n                                                                                source.title,\n                                                                                \" (Relevance: \",\n                                                                                (source.relevance * 100).toFixed(0),\n                                                                                \"%)\"\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 31\n                                                                        }, this)),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-600 mt-1\",\n                                                                        children: [\n                                                                            \"Trust Score: \",\n                                                                            (message.trustScore * 100).toFixed(0),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 192,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 opacity-70\",\n                                                                children: message.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-t\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newMessage,\n                                                        onChange: (e)=>setNewMessage(e.target.value),\n                                                        onKeyPress: (e)=>e.key === \"Enter\" && handleSendMessage(),\n                                                        placeholder: \"Ask a question...\",\n                                                        className: \"flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSendMessage,\n                                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"documents\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Manage your knowledge base\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: mockDocuments.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: doc.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Status: \",\n                                                                            doc.status,\n                                                                            \" • Chunks: \",\n                                                                            doc.chunks\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-red-600 hover:text-red-800 text-sm\",\n                                                                        children: \"Delete\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, doc.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Platform insights and metrics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-blue-600\",\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Total Queries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 264,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.totalQueries\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-green-600\",\n                                                                    children: \"\\uD83D\\uDCCA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Avg Trust Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: [\n                                                                                (mockAnalytics.avgTrustScore * 100).toFixed(0),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-purple-600\",\n                                                                    children: \"\\uD83D\\uDCC4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Documents\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.totalDocuments\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 283,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 279,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-orange-600\",\n                                                                    children: \"\\uD83D\\uDC65\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Active Users\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.activeUsers\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === \"upload\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Upload Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Add new documents to your knowledge base\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-12 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-6xl text-gray-400\",\n                                                        children: \"⬆️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-4 text-lg font-medium text-gray-900\",\n                                                        children: \"Upload Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm text-gray-500\",\n                                                        children: \"Drag and drop files here, or click to select files\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-400\",\n                                                        children: \"Supports PDF, DOCX, TXT, MD files up to 50MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"mt-4 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600\",\n                                                        children: \"Select Files\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(DemoPage, \"tdRThtiAlJB6brpGPKjdSz4lASQ=\");\n_c = DemoPage;\nvar _c;\n$RefreshReg$(_c, \"DemoPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/demo/page.tsx\n"));

/***/ })

});