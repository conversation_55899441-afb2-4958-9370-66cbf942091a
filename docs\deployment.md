# AIthentiqMind Production Deployment Guide

This guide covers deploying AIthentiqMind to production using Kubernetes.

## Prerequisites

- **Kubernetes cluster** (1.24+)
- **kubectl** configured for your cluster
- **Docker registry** access (GitHub Container Registry recommended)
- **Domain names** configured:
  - `app.yourdomain.com` (frontend)
  - `api.yourdomain.com` (backend)
- **SSL certificates** (Let's Encrypt recommended)

## Infrastructure Requirements

### Minimum Resources

- **CPU**: 4 cores total
- **Memory**: 8GB RAM total
- **Storage**: 50GB persistent storage
- **Network**: Load balancer with SSL termination

### Recommended Production Setup

- **CPU**: 8+ cores
- **Memory**: 16GB+ RAM
- **Storage**: 100GB+ SSD storage
- **Network**: CDN + Load balancer
- **Monitoring**: Prometheus + Grafana
- **Backup**: Automated database backups

## Quick Deployment

### 1. Clone Repository

```bash
git clone <repository-url>
cd AIthentiqMind
```

### 2. Configure Secrets

```bash
# Copy and edit secrets
cp k8s/base/secrets.yaml k8s/base/secrets-prod.yaml

# Edit secrets-prod.yaml with your production values:
# - DATABASE_PASSWORD
# - REDIS_PASSWORD
# - SECRET_KEY (generate with: openssl rand -hex 32)
# - OPENAI_API_KEY
# - AUTH0_* credentials
# - MINIO_* credentials
```

### 3. Deploy to Kubernetes

```bash
# Create namespace
kubectl apply -f k8s/base/namespace.yaml

# Apply secrets
kubectl apply -f k8s/base/secrets-prod.yaml

# Deploy infrastructure
kubectl apply -f k8s/base/configmap.yaml
kubectl apply -f k8s/base/postgres.yaml
kubectl apply -f k8s/base/redis.yaml
kubectl apply -f k8s/base/qdrant.yaml
kubectl apply -f k8s/base/minio.yaml
kubectl apply -f k8s/base/n8n.yaml

# Wait for infrastructure to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n aithentiqmind --timeout=300s

# Deploy applications
kubectl apply -f k8s/base/backend.yaml
kubectl apply -f k8s/base/frontend.yaml

# Configure ingress
kubectl apply -f k8s/base/ingress.yaml
```

### 4. Run Database Migrations

```bash
# Wait for backend to be ready
kubectl wait --for=condition=ready pod -l app=backend -n aithentiqmind --timeout=300s

# Run migrations
kubectl exec -n aithentiqmind deployment/backend -- python -m alembic upgrade head
```

### 5. Verify Deployment

```bash
# Check all pods are running
kubectl get pods -n aithentiqmind

# Check services
kubectl get services -n aithentiqmind

# Check ingress
kubectl get ingress -n aithentiqmind

# Test health endpoints
curl https://api.yourdomain.com/health
curl https://app.yourdomain.com/api/health
```

## Configuration

### Environment Variables

Key production environment variables to configure:

```yaml
# Security
SECRET_KEY: "your-very-long-secret-key"
JWT_ALGORITHM: "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES: "1440"

# Database
DATABASE_URL: "********************************/db"

# OpenAI
OPENAI_API_KEY: "your-openai-api-key"
OPENAI_MODEL: "gpt-4"
OPENAI_EMBEDDING_MODEL: "text-embedding-ada-002"

# Auth0
AUTH0_DOMAIN: "your-domain.auth0.com"
AUTH0_CLIENT_ID: "your-client-id"
AUTH0_CLIENT_SECRET: "your-client-secret"
AUTH0_AUDIENCE: "https://api.yourdomain.com"

# Application
ENVIRONMENT: "production"
LOG_LEVEL: "INFO"
```

### Auth0 Setup

1. **Create Auth0 Application**:
   - Type: Single Page Application
   - Allowed Callback URLs: `https://app.yourdomain.com/api/auth/callback`
   - Allowed Logout URLs: `https://app.yourdomain.com`
   - Allowed Web Origins: `https://app.yourdomain.com`

2. **Configure API**:
   - Identifier: `https://api.yourdomain.com`
   - Signing Algorithm: RS256

3. **Set up Rules/Actions** for user management and tenant assignment

### SSL/TLS Configuration

Using cert-manager with Let's Encrypt:

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

## Monitoring and Observability

### Prometheus + Grafana

```bash
# Install monitoring stack
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

helm install monitoring prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --create-namespace \
  --set grafana.adminPassword=admin123
```

### Application Metrics

The application exposes metrics at `/metrics` endpoint:

- Request duration and count
- Database connection pool stats
- RAG query performance
- Trust score distributions
- Error rates and types

### Logging

Structured JSON logging is configured for:

- Request/response logging
- Error tracking with stack traces
- Performance metrics
- Security events
- Audit trails

## Backup and Recovery

### Database Backups

```bash
# Create backup job
kubectl apply -f - <<EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: aithentiqmind
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/bash
            - -c
            - |
              pg_dump \$DATABASE_URL > /backup/backup-\$(date +%Y%m%d-%H%M%S).sql
              # Upload to S3 or your backup storage
            env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: aithentiqmind-secrets
                  key: DATABASE_URL
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
EOF
```

### Vector Database Backups

Qdrant snapshots can be created via API:

```bash
# Create snapshot
curl -X POST "https://qdrant.yourdomain.com/collections/{collection_name}/snapshots"

# Download snapshot
curl "https://qdrant.yourdomain.com/collections/{collection_name}/snapshots/{snapshot_name}"
```

## Scaling

### Horizontal Pod Autoscaling

```bash
# Enable HPA for backend
kubectl autoscale deployment backend --cpu-percent=70 --min=2 --max=10 -n aithentiqmind

# Enable HPA for frontend
kubectl autoscale deployment frontend --cpu-percent=70 --min=2 --max=5 -n aithentiqmind
```

### Database Scaling

For high-traffic scenarios:

1. **Read Replicas**: Configure PostgreSQL read replicas
2. **Connection Pooling**: Use PgBouncer for connection management
3. **Caching**: Redis for query result caching

### Vector Database Scaling

Qdrant supports:

1. **Clustering**: Multi-node setup for high availability
2. **Sharding**: Distribute collections across nodes
3. **Replication**: Data redundancy

## Security

### Network Policies

```bash
# Apply network policies
kubectl apply -f k8s/security/network-policies.yaml
```

### Pod Security Standards

```bash
# Apply pod security policies
kubectl apply -f k8s/security/pod-security.yaml
```

### Secrets Management

Consider using external secret management:

- **AWS Secrets Manager**
- **Azure Key Vault**
- **HashiCorp Vault**
- **Google Secret Manager**

## Troubleshooting

### Common Issues

1. **Pod Startup Failures**:
   ```bash
   kubectl describe pod <pod-name> -n aithentiqmind
   kubectl logs <pod-name> -n aithentiqmind
   ```

2. **Database Connection Issues**:
   ```bash
   kubectl exec -it deployment/backend -n aithentiqmind -- python -c "
   from app.db.database import engine
   print('Database connection test')
   "
   ```

3. **Ingress Issues**:
   ```bash
   kubectl describe ingress aithentiqmind-ingress -n aithentiqmind
   ```

### Performance Tuning

1. **Database**:
   - Tune PostgreSQL configuration
   - Add appropriate indexes
   - Monitor query performance

2. **Application**:
   - Adjust worker processes
   - Tune connection pools
   - Optimize vector search parameters

3. **Infrastructure**:
   - Right-size resource requests/limits
   - Use appropriate storage classes
   - Configure load balancer properly

## Maintenance

### Updates

1. **Application Updates**:
   ```bash
   # Update backend
   kubectl set image deployment/backend backend=ghcr.io/yourorg/backend:v1.2.0 -n aithentiqmind
   
   # Update frontend
   kubectl set image deployment/frontend frontend=ghcr.io/yourorg/frontend:v1.2.0 -n aithentiqmind
   ```

2. **Database Migrations**:
   ```bash
   kubectl exec -n aithentiqmind deployment/backend -- python -m alembic upgrade head
   ```

3. **Infrastructure Updates**:
   - Update Kubernetes cluster
   - Update base images
   - Update dependencies

### Health Checks

Regular health check endpoints:

- `/health` - Basic health check
- `/api/v1/health/detailed` - Detailed component health
- `/metrics` - Prometheus metrics

## Support

For production support:

1. **Monitoring**: Set up alerts for critical metrics
2. **Logging**: Centralized log aggregation
3. **Documentation**: Keep runbooks updated
4. **Incident Response**: Define escalation procedures
