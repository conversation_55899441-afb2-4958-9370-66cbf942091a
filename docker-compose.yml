version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: aithentiq_postgres
    environment:
      POSTGRES_DB: aithentiq_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - aithentiq_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Queue
  redis:
    image: redis:7-alpine
    container_name: aithentiq_redis
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - aithentiq_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: aithentiq_qdrant
    volumes:
      - qdrant_data:/qdrant/storage
    ports:
      - "6333:6333"
      - "6334:6334"
    networks:
      - aithentiq_network
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MinIO S3-compatible Storage
  minio:
    image: minio/minio:latest
    container_name: aithentiq_minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - aithentiq_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # n8n Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    container_name: aithentiq_n8n
    environment:
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n_db
      DB_POSTGRESDB_USER: postgres
      DB_POSTGRESDB_PASSWORD: postgres_password
      N8N_BASIC_AUTH_ACTIVE: true
      N8N_BASIC_AUTH_USER: admin
      N8N_BASIC_AUTH_PASSWORD: admin123
      WEBHOOK_URL: http://localhost:5678
      GENERIC_TIMEZONE: UTC
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n-workflows:/home/<USER>/.n8n/workflows
    ports:
      - "5678:5678"
    networks:
      - aithentiq_network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: aithentiq_backend
    environment:
      DATABASE_URL: *****************************************************/aithentiq_db
      REDIS_URL: redis://redis:6379/0
      QDRANT_URL: http://qdrant:6333
      MINIO_ENDPOINT: minio:9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      N8N_WEBHOOK_URL: http://n8n:5678/webhook
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      AUTH0_DOMAIN: ${AUTH0_DOMAIN}
      AUTH0_CLIENT_ID: ${AUTH0_CLIENT_ID}
      AUTH0_CLIENT_SECRET: ${AUTH0_CLIENT_SECRET}
    volumes:
      - ./backend:/app
      - backend_uploads:/app/uploads
    ports:
      - "8000:8000"
    networks:
      - aithentiq_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    develop:
      watch:
        - action: sync
          path: ./backend
          target: /app

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: aithentiq_frontend
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_AUTH0_DOMAIN: ${AUTH0_DOMAIN}
      NEXT_PUBLIC_AUTH0_CLIENT_ID: ${AUTH0_CLIENT_ID}
      NEXT_PUBLIC_AUTH0_REDIRECT_URI: http://localhost:3000/api/auth/callback
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    networks:
      - aithentiq_network
    depends_on:
      - backend
    develop:
      watch:
        - action: sync
          path: ./frontend
          target: /app

volumes:
  postgres_data:
  redis_data:
  qdrant_data:
  minio_data:
  n8n_data:
  backend_uploads:

networks:
  aithentiq_network:
    driver: bridge
