"""
RagFlow client integration for document processing and RAG operations
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import json

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class RagFlowClient:
    """Client for RagFlow API integration"""
    
    def __init__(self):
        self.base_url = settings.RAGFLOW_BASE_URL
        self.api_key = settings.RAGFLOW_API_KEY
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession(
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            )
        return self.session
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def create_knowledge_base(self, tenant_id: str, name: str, description: str = "") -> Dict[str, Any]:
        """Create a knowledge base for tenant"""
        session = await self._get_session()
        
        payload = {
            "name": f"{tenant_id}_{name}",
            "description": description,
            "language": "English",
            "embedding_model": "BAAI/bge-large-en-v1.5",
            "chunk_method": "intelligent",
            "parser_config": {
                "chunk_token_count": 1024,
                "layout_recognize": True,
                "task_page_size": 12
            }
        }
        
        async with session.post(f"{self.base_url}/api/v1/kb", json=payload) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"Created knowledge base: {data.get('data', {}).get('id')}")
                return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to create knowledge base: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def upload_document(
        self, 
        kb_id: str, 
        file_content: bytes, 
        filename: str,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Upload document to knowledge base"""
        session = await self._get_session()
        
        # Create form data
        data = aiohttp.FormData()
        data.add_field('file', file_content, filename=filename)
        data.add_field('kb_id', kb_id)
        
        if metadata:
            data.add_field('metadata', json.dumps(metadata))
        
        # Remove content-type header for multipart upload
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        async with session.post(
            f"{self.base_url}/api/v1/document", 
            data=data,
            headers=headers
        ) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"Uploaded document: {data.get('data', {}).get('id')}")
                return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to upload document: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def get_document_status(self, doc_id: str) -> Dict[str, Any]:
        """Get document processing status"""
        session = await self._get_session()
        
        async with session.get(f"{self.base_url}/api/v1/document/{doc_id}") as response:
            if response.status == 200:
                data = await response.json()
                return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to get document status: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def start_document_parsing(self, doc_id: str) -> bool:
        """Start document parsing process"""
        session = await self._get_session()
        
        async with session.post(f"{self.base_url}/api/v1/document/{doc_id}/run") as response:
            if response.status == 200:
                logger.info(f"Started parsing for document: {doc_id}")
                return True
            else:
                error = await response.text()
                logger.error(f"Failed to start parsing: {error}")
                return False
    
    async def query_knowledge_base(
        self, 
        kb_id: str, 
        question: str,
        top_k: int = 5,
        similarity_threshold: float = 0.1,
        vector_similarity_weight: float = 0.3
    ) -> Dict[str, Any]:
        """Query knowledge base for relevant chunks"""
        session = await self._get_session()
        
        payload = {
            "question": question,
            "kb_ids": [kb_id],
            "top_k": top_k,
            "similarity_threshold": similarity_threshold,
            "vector_similarity_weight": vector_similarity_weight
        }
        
        async with session.post(f"{self.base_url}/api/v1/retrieval", json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to query knowledge base: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def chat_with_knowledge_base(
        self,
        kb_id: str,
        question: str,
        conversation_id: Optional[str] = None,
        stream: bool = False
    ) -> Dict[str, Any]:
        """Chat with knowledge base using RagFlow's chat API"""
        session = await self._get_session()
        
        payload = {
            "question": question,
            "kb_ids": [kb_id],
            "stream": stream
        }
        
        if conversation_id:
            payload["conversation_id"] = conversation_id
        
        async with session.post(f"{self.base_url}/api/v1/chat", json=payload) as response:
            if response.status == 200:
                if stream:
                    return response  # Return response for streaming
                else:
                    data = await response.json()
                    return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to chat with knowledge base: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def stream_chat_response(
        self,
        kb_id: str,
        question: str,
        conversation_id: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream chat response from RagFlow"""
        session = await self._get_session()
        
        payload = {
            "question": question,
            "kb_ids": [kb_id],
            "stream": True
        }
        
        if conversation_id:
            payload["conversation_id"] = conversation_id
        
        async with session.post(f"{self.base_url}/api/v1/chat", json=payload) as response:
            if response.status == 200:
                async for line in response.content:
                    if line:
                        try:
                            # Parse SSE format
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]  # Remove 'data: ' prefix
                                if data_str and data_str != '[DONE]':
                                    data = json.loads(data_str)
                                    yield data
                        except json.JSONDecodeError:
                            continue
            else:
                error = await response.text()
                logger.error(f"Failed to stream chat: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def delete_document(self, doc_id: str) -> bool:
        """Delete document from knowledge base"""
        session = await self._get_session()
        
        async with session.delete(f"{self.base_url}/api/v1/document/{doc_id}") as response:
            if response.status == 200:
                logger.info(f"Deleted document: {doc_id}")
                return True
            else:
                error = await response.text()
                logger.error(f"Failed to delete document: {error}")
                return False
    
    async def list_knowledge_bases(self) -> List[Dict[str, Any]]:
        """List all knowledge bases"""
        session = await self._get_session()
        
        async with session.get(f"{self.base_url}/api/v1/kb") as response:
            if response.status == 200:
                data = await response.json()
                return data.get("data", [])
            else:
                error = await response.text()
                logger.error(f"Failed to list knowledge bases: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def get_knowledge_base_stats(self, kb_id: str) -> Dict[str, Any]:
        """Get knowledge base statistics"""
        session = await self._get_session()
        
        async with session.get(f"{self.base_url}/api/v1/kb/{kb_id}/stats") as response:
            if response.status == 200:
                data = await response.json()
                return data.get("data", {})
            else:
                error = await response.text()
                logger.error(f"Failed to get KB stats: {error}")
                raise Exception(f"RagFlow API error: {error}")
    
    async def health_check(self) -> bool:
        """Check RagFlow service health"""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/api/v1/health") as response:
                return response.status == 200
        except Exception as e:
            logger.error(f"RagFlow health check failed: {e}")
            return False


# Global client instance
ragflow_client = RagFlowClient()


async def get_ragflow_client() -> RagFlowClient:
    """Get RagFlow client instance"""
    return ragflow_client
