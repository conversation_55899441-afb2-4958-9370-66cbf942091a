import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { UserProvider } from '@auth0/nextjs-auth0/client'

import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'
import '@/styles/globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

export const metadata: Metadata = {
  title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
  description: 'Intelligent document-based answers with workflow orchestration',
  keywords: ['RAG', 'AI', 'Enterprise', 'SaaS', 'Document Intelligence', 'Workflow Automation'],
  authors: [{ name: 'AIthentiqMind Team' }],
  creator: 'AIthentiqMind',
  publisher: 'AIthentiqMind',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://aithentiqmind.com',
    title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
    description: 'Intelligent document-based answers with workflow orchestration',
    siteName: 'AIthentiqMind',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
    description: 'Intelligent document-based answers with workflow orchestration',
    creator: '@aithentiqmind',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="min-h-screen bg-background font-sans antialiased">
        <UserProvider>
          <Providers>
            {children}
            <Toaster />
          </Providers>
        </UserProvider>
      </body>
    </html>
  )
}
