import type { Metadata } from 'next'
import '../styles/globals.css'

export const metadata: Metadata = {
  title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
  description: 'Intelligent document-based answers with workflow orchestration',
  keywords: ['RAG', 'AI', 'Enterprise', 'SaaS', 'Document Intelligence', 'Workflow Automation'],
  authors: [{ name: 'AIthentiqMind Team' }],
  creator: 'AIthentiqMind',
  publisher: 'AIthentiqMind',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://aithentiqmind.com',
    title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
    description: 'Intelligent document-based answers with workflow orchestration',
    siteName: 'AIthentiqMind',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AIthentiqMind - Enterprise RAG SaaS Platform',
    description: 'Intelligent document-based answers with workflow orchestration',
    creator: '@aithentiqmind',
  },
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
  },
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gray-50 font-sans antialiased">
        {children}
      </body>
    </html>
  )
}
