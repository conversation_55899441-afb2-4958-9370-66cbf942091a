#!/bin/bash

# AIthentiqMind Production Deployment Script
# This script deploys the application to Kubernetes

set -e

# Configuration
NAMESPACE="aithentiqmind"
ENVIRONMENT=${1:-production}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-"ghcr.io"}
IMAGE_TAG=${IMAGE_TAG:-"latest"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if kubectl can connect to cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
        exit 1
    fi
    
    # Check if namespace exists
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log_warning "Namespace $NAMESPACE does not exist. Creating it..."
        kubectl create namespace $NAMESPACE
    fi
    
    log_success "Prerequisites check passed"
}

# Deploy infrastructure components
deploy_infrastructure() {
    log_info "Deploying infrastructure components..."
    
    # Apply namespace
    kubectl apply -f k8s/base/namespace.yaml
    
    # Apply secrets (make sure to update with real values)
    log_warning "Make sure to update secrets with real production values!"
    kubectl apply -f k8s/base/secrets.yaml
    
    # Apply configmap
    kubectl apply -f k8s/base/configmap.yaml
    
    # Deploy PostgreSQL
    log_info "Deploying PostgreSQL..."
    kubectl apply -f k8s/base/postgres.yaml
    
    # Wait for PostgreSQL to be ready
    log_info "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    # Deploy Redis
    log_info "Deploying Redis..."
    kubectl apply -f k8s/base/redis.yaml
    
    # Wait for Redis to be ready
    log_info "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    # Deploy Qdrant
    log_info "Deploying Qdrant..."
    kubectl apply -f k8s/base/qdrant.yaml
    
    # Wait for Qdrant to be ready
    log_info "Waiting for Qdrant to be ready..."
    kubectl wait --for=condition=ready pod -l app=qdrant -n $NAMESPACE --timeout=300s
    
    # Deploy MinIO
    log_info "Deploying MinIO..."
    kubectl apply -f k8s/base/minio.yaml
    
    # Wait for MinIO to be ready
    log_info "Waiting for MinIO to be ready..."
    kubectl wait --for=condition=ready pod -l app=minio -n $NAMESPACE --timeout=300s
    
    # Deploy n8n
    log_info "Deploying n8n..."
    kubectl apply -f k8s/base/n8n.yaml
    
    # Wait for n8n to be ready
    log_info "Waiting for n8n to be ready..."
    kubectl wait --for=condition=ready pod -l app=n8n -n $NAMESPACE --timeout=300s
    
    log_success "Infrastructure components deployed successfully"
}

# Deploy application
deploy_application() {
    log_info "Deploying application components..."
    
    # Update image tags in deployment files
    if [ "$IMAGE_TAG" != "latest" ]; then
        log_info "Updating image tags to $IMAGE_TAG..."
        sed -i.bak "s|:latest|:$IMAGE_TAG|g" k8s/base/backend.yaml
        sed -i.bak "s|:latest|:$IMAGE_TAG|g" k8s/base/frontend.yaml
    fi
    
    # Deploy backend
    log_info "Deploying backend..."
    kubectl apply -f k8s/base/backend.yaml
    
    # Wait for backend to be ready
    log_info "Waiting for backend to be ready..."
    kubectl wait --for=condition=ready pod -l app=backend -n $NAMESPACE --timeout=300s
    
    # Run database migrations
    log_info "Running database migrations..."
    kubectl exec -n $NAMESPACE deployment/backend -- python -m alembic upgrade head
    
    # Deploy frontend
    log_info "Deploying frontend..."
    kubectl apply -f k8s/base/frontend.yaml
    
    # Wait for frontend to be ready
    log_info "Waiting for frontend to be ready..."
    kubectl wait --for=condition=ready pod -l app=frontend -n $NAMESPACE --timeout=300s
    
    # Deploy ingress
    log_info "Deploying ingress..."
    kubectl apply -f k8s/base/ingress.yaml
    
    # Restore original deployment files
    if [ "$IMAGE_TAG" != "latest" ]; then
        mv k8s/base/backend.yaml.bak k8s/base/backend.yaml
        mv k8s/base/frontend.yaml.bak k8s/base/frontend.yaml
    fi
    
    log_success "Application deployed successfully"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check all pods are running
    log_info "Checking pod status..."
    kubectl get pods -n $NAMESPACE
    
    # Check services
    log_info "Checking services..."
    kubectl get services -n $NAMESPACE
    
    # Check ingress
    log_info "Checking ingress..."
    kubectl get ingress -n $NAMESPACE
    
    # Test health endpoints
    log_info "Testing health endpoints..."
    
    # Get backend service IP
    BACKEND_IP=$(kubectl get service backend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    
    # Test backend health (from within cluster)
    if kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$BACKEND_IP:8000/health; then
        log_success "Backend health check passed"
    else
        log_error "Backend health check failed"
    fi
    
    # Get frontend service IP
    FRONTEND_IP=$(kubectl get service frontend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}')
    
    # Test frontend health (from within cluster)
    if kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl -- curl -f http://$FRONTEND_IP:3000/api/health; then
        log_success "Frontend health check passed"
    else
        log_error "Frontend health check failed"
    fi
    
    log_success "Deployment verification completed"
}

# Rollback deployment
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    # Rollback backend
    kubectl rollout undo deployment/backend -n $NAMESPACE
    
    # Rollback frontend
    kubectl rollout undo deployment/frontend -n $NAMESPACE
    
    # Wait for rollback to complete
    kubectl rollout status deployment/backend -n $NAMESPACE
    kubectl rollout status deployment/frontend -n $NAMESPACE
    
    log_success "Rollback completed"
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo
    
    log_info "Pods:"
    kubectl get pods -n $NAMESPACE -o wide
    echo
    
    log_info "Services:"
    kubectl get services -n $NAMESPACE
    echo
    
    log_info "Ingress:"
    kubectl get ingress -n $NAMESPACE
    echo
    
    log_info "Deployments:"
    kubectl get deployments -n $NAMESPACE
    echo
    
    # Show resource usage
    log_info "Resource Usage:"
    kubectl top pods -n $NAMESPACE 2>/dev/null || log_warning "Metrics server not available"
}

# Main deployment function
main() {
    log_info "Starting AIthentiqMind deployment to $ENVIRONMENT environment"
    log_info "Using namespace: $NAMESPACE"
    log_info "Using image tag: $IMAGE_TAG"
    echo
    
    case "${1:-deploy}" in
        "deploy")
            check_prerequisites
            deploy_infrastructure
            deploy_application
            verify_deployment
            show_status
            log_success "Deployment completed successfully!"
            ;;
        "infrastructure")
            check_prerequisites
            deploy_infrastructure
            log_success "Infrastructure deployment completed!"
            ;;
        "application")
            check_prerequisites
            deploy_application
            verify_deployment
            log_success "Application deployment completed!"
            ;;
        "verify")
            verify_deployment
            ;;
        "status")
            show_status
            ;;
        "rollback")
            rollback_deployment
            ;;
        *)
            echo "Usage: $0 [deploy|infrastructure|application|verify|status|rollback]"
            echo
            echo "Commands:"
            echo "  deploy         - Full deployment (default)"
            echo "  infrastructure - Deploy only infrastructure components"
            echo "  application    - Deploy only application components"
            echo "  verify         - Verify current deployment"
            echo "  status         - Show deployment status"
            echo "  rollback       - Rollback to previous version"
            echo
            echo "Environment variables:"
            echo "  DOCKER_REGISTRY - Docker registry URL (default: ghcr.io)"
            echo "  IMAGE_TAG       - Image tag to deploy (default: latest)"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
