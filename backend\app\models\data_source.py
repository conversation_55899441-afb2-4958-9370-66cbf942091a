"""
Data source model for external integrations
"""

from typing import Dict, Any
from sqlalchemy import Column, String, DateTime, Boolean, Text, ForeignKey, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class DataSource(Base):
    __tablename__ = "data_sources"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Data source details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    source_type = Column(String, nullable=False, index=True)  # dropbox, sharepoint, github, etc.

    # Connection configuration
    connection_config = Column(JSON, default=dict)  # Encrypted connection details

    # Status and sync information
    is_active = Column(Boolean, default=True)
    sync_enabled = Column(Boolean, default=False)
    sync_frequency = Column(String, default="manual")  # manual, hourly, daily, weekly

    # Sync tracking
    last_sync = Column(DateTime(timezone=True), nullable=True)
    next_sync = Column(DateTime(timezone=True), nullable=True)
    sync_status = Column(String, default="pending", index=True)  # pending, running, success, failed
    sync_error = Column(Text, nullable=True)

    # Statistics
    total_documents = Column(Integer, default=0)
    synced_documents = Column(Integer, default=0)
    failed_documents = Column(Integer, default=0)

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant", back_populates="data_sources")

    def get_connection_config(self) -> Dict[str, Any]:
        """Get connection configuration (should be decrypted)"""
        return self.connection_config or {}

    def set_connection_config(self, config: Dict[str, Any]):
        """Set connection configuration (should be encrypted)"""
        # TODO: Implement encryption for sensitive data
        self.connection_config = config

    def update_sync_status(self, status: str, error: str = None):
        """Update sync status"""
        self.sync_status = status
        self.sync_error = error
        if status in ["success", "failed"]:
            self.last_sync = func.now()

    def increment_synced_documents(self, count: int = 1):
        """Increment synced document counter"""
        self.synced_documents = (self.synced_documents or 0) + count

    def increment_failed_documents(self, count: int = 1):
        """Increment failed document counter"""
        self.failed_documents = (self.failed_documents or 0) + count

    def get_sync_progress(self) -> Dict[str, Any]:
        """Get sync progress information"""
        total = self.total_documents or 0
        synced = self.synced_documents or 0
        failed = self.failed_documents or 0

        return {
            "total_documents": total,
            "synced_documents": synced,
            "failed_documents": failed,
            "pending_documents": max(0, total - synced - failed),
            "success_rate": round((synced / total) * 100, 2) if total > 0 else 0,
            "completion_rate": round(((synced + failed) / total) * 100, 2) if total > 0 else 0
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert data source to dictionary"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "name": self.name,
            "description": self.description,
            "source_type": self.source_type,
            "is_active": self.is_active,
            "sync_enabled": self.sync_enabled,
            "sync_frequency": self.sync_frequency,
            "sync_status": self.sync_status,
            "sync_error": self.sync_error,
            "sync_progress": self.get_sync_progress(),
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "next_sync": self.next_sync.isoformat() if self.next_sync else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
