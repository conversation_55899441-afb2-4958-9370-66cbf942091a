apiVersion: v1
kind: ConfigMap
metadata:
  name: aithentiqmind-config
  namespace: aithentiqmind
data:
  # Database
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "aithentiqmind"
  
  # Redis
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # Qdrant
  QDRANT_URL: "http://qdrant-service:6333"
  
  # MinIO
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_BUCKET: "aithentiqmind-documents"
  
  # n8n
  N8N_BASE_URL: "http://n8n-service:5678"

  # RagFlow
  RAGFLOW_BASE_URL: "http://ragflow-service:9380"

  # Application
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # RAG Configuration
  CHUNK_SIZE: "1000"
  CHUNK_OVERLAP: "200"
  RETRIEVAL_TOP_K: "5"
  MAX_CHUNKS_PER_DOCUMENT: "1000"
  MAX_FILE_SIZE: "52428800"  # 50MB
  
  # Trust Scoring
  CONFIDENCE_WEIGHT: "0.3"
  RELEVANCE_WEIGHT: "0.3"
  RECENCY_WEIGHT: "0.2"
  MIN_SOURCE_DIVERSITY: "2"
  
  # OpenAI
  OPENAI_MODEL: "gpt-4"
  OPENAI_EMBEDDING_MODEL: "text-embedding-ada-002"
  OPENAI_MAX_TOKENS: "4000"
  OPENAI_TEMPERATURE: "0.1"
  
  # File Types
  ALLOWED_FILE_TYPES: "pdf,docx,txt,md,html"
  
  # Qdrant
  QDRANT_COLLECTION_PREFIX: "aithentiqmind"
  
  # JWT
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "1440"  # 24 hours
