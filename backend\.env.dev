# Development Environment Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# Database (using SQLite for development)
DATABASE_URL=sqlite+aiosqlite:///./aithentiqmind_dev.db

# Redis (optional for development)
REDIS_URL=redis://localhost:6379/0

# Security
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=dev-jwt-secret-key

# CORS
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=AIthentiqMind

# Development settings
WORKERS=1
RELOAD=true

# Mock external services for development
OPENAI_API_KEY=mock-openai-key
AUTH0_DOMAIN=dev.auth0.com
AUTH0_CLIENT_ID=mock-client-id
AUTH0_CLIENT_SECRET=mock-client-secret
AUTH0_AUDIENCE=https://api.dev.aithentiqmind.com

# RagFlow (mock for development)
RAGFLOW_BASE_URL=http://localhost:9380
RAGFLOW_API_KEY=mock-ragflow-key

# n8n (mock for development)
N8N_BASE_URL=http://localhost:5678
N8N_API_KEY=mock-n8n-key
