"""
Tests for query API endpoints
"""

import pytest
import pytest_asyncio
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from app.models.tenant import Tenant
from app.models.user import User


class TestQueryAPI:
    """Test query API endpoints"""
    
    @pytest.mark.asyncio
    async def test_ask_question_success(
        self,
        client: AsyncClient,
        test_tenant: Tenant,
        test_user: User,
        auth_headers: dict
    ):
        """Test successful question asking"""
        
        # Mock RAG engine response
        mock_response = {
            "query_id": "test_query_123",
            "answer": "Machine learning is a subset of artificial intelligence.",
            "sources": [
                {
                    "documentId": "doc_1",
                    "title": "AI Guide",
                    "content": "ML is part of AI...",
                    "relevanceScore": 0.95,
                    "chunkId": "chunk_1",
                    "metadata": {"filename": "ai_guide.pdf"}
                }
            ],
            "trust_score": 0.85,
            "response_time": 1.5,
            "retrieval_time": 0.5,
            "generation_time": 1.0
        }
        
        with patch('app.services.rag.engine.RAGEngine.process_query', return_value=mock_response):
            response = await client.post(
                "/api/v1/queries/ask",
                json={
                    "question": "What is machine learning?",
                    "include_sources": True,
                    "top_k": 5
                },
                headers=auth_headers
            )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["query_id"] == "test_query_123"
        assert data["answer"] == "Machine learning is a subset of artificial intelligence."
        assert data["trust_score"] == 0.85
        assert len(data["sources"]) == 1
        assert data["sources"][0]["relevanceScore"] == 0.95
    
    @pytest.mark.asyncio
    async def test_ask_question_invalid_input(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test asking question with invalid input"""
        
        # Empty question
        response = await client.post(
            "/api/v1/queries/ask",
            json={"question": ""},
            headers=auth_headers
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_ask_question_unauthorized(
        self,
        client: AsyncClient
    ):
        """Test asking question without authentication"""
        
        response = await client.post(
            "/api/v1/queries/ask",
            json={"question": "What is AI?"}
        )
        
        assert response.status_code == 401
    
    @pytest.mark.asyncio
    async def test_ask_question_with_filters(
        self,
        client: AsyncClient,
        test_tenant: Tenant,
        test_user: User,
        auth_headers: dict
    ):
        """Test asking question with document filters"""
        
        mock_response = {
            "query_id": "test_query_456",
            "answer": "Filtered response about AI.",
            "sources": [],
            "trust_score": 0.75,
            "response_time": 1.2
        }
        
        with patch('app.services.rag.engine.RAGEngine.process_query', return_value=mock_response) as mock_rag:
            response = await client.post(
                "/api/v1/queries/ask",
                json={
                    "question": "What is AI?",
                    "document_filters": {
                        "tags": ["ai", "ml"],
                        "document_ids": ["doc_1", "doc_2"]
                    },
                    "top_k": 3
                },
                headers=auth_headers
            )
        
        assert response.status_code == 200
        
        # Verify filters were passed to RAG engine
        mock_rag.assert_called_once()
        call_args = mock_rag.call_args
        assert call_args.kwargs["document_filters"]["tags"] == ["ai", "ml"]
        assert call_args.kwargs["document_filters"]["document_ids"] == ["doc_1", "doc_2"]
        assert call_args.kwargs["top_k"] == 3
    
    @pytest.mark.asyncio
    async def test_streaming_query(
        self,
        client: AsyncClient,
        test_tenant: Tenant,
        test_user: User,
        auth_headers: dict
    ):
        """Test streaming query response"""
        
        # Mock streaming response
        async def mock_stream():
            yield {"type": "content", "content": "AI is "}
            yield {"type": "content", "content": "artificial intelligence."}
            yield {"type": "sources", "sources": []}
            yield {"type": "trust_score", "trust_score": 0.8}
            yield {"type": "complete", "query_id": "stream_123"}
        
        with patch('app.services.rag.engine.RAGEngine.process_query_stream', return_value=mock_stream()):
            response = await client.post(
                "/api/v1/queries/ask-stream",
                json={"question": "What is AI?"},
                headers=auth_headers
            )
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_get_query_history(
        self,
        client: AsyncClient,
        test_tenant: Tenant,
        test_user: User,
        test_query,
        auth_headers: dict
    ):
        """Test getting query history"""
        
        response = await client.get(
            "/api/v1/queries/history",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "queries" in data
        assert "total" in data
        assert len(data["queries"]) >= 1
        
        # Check query structure
        query = data["queries"][0]
        assert "id" in query
        assert "question" in query
        assert "answer" in query
        assert "trust_score" in query
        assert "created_at" in query
    
    @pytest.mark.asyncio
    async def test_get_query_history_with_pagination(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test query history with pagination"""
        
        response = await client.get(
            "/api/v1/queries/history?limit=5&offset=0",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["queries"]) <= 5
    
    @pytest.mark.asyncio
    async def test_get_specific_query(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test getting specific query by ID"""
        
        response = await client.get(
            f"/api/v1/queries/{test_query.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == str(test_query.id)
        assert data["question"] == test_query.question
        assert data["answer"] == test_query.answer
        assert data["trust_score"] == test_query.trust_score
    
    @pytest.mark.asyncio
    async def test_get_nonexistent_query(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test getting non-existent query"""
        
        response = await client.get(
            "/api/v1/queries/nonexistent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_delete_query(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test deleting a query"""
        
        response = await client.delete(
            f"/api/v1/queries/{test_query.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        
        # Verify query is deleted
        get_response = await client.get(
            f"/api/v1/queries/{test_query.id}",
            headers=auth_headers
        )
        
        assert get_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_query_feedback(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test providing feedback on a query"""
        
        response = await client.post(
            f"/api/v1/queries/{test_query.id}/feedback",
            json={
                "rating": 4,
                "feedback": "Good answer but could be more detailed",
                "helpful": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["message"] == "Feedback recorded successfully"
    
    @pytest.mark.asyncio
    async def test_query_feedback_invalid_rating(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test providing invalid feedback rating"""
        
        response = await client.post(
            f"/api/v1/queries/{test_query.id}/feedback",
            json={
                "rating": 6,  # Invalid rating (should be 1-5)
                "feedback": "Test feedback"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 422
    
    @pytest.mark.asyncio
    async def test_query_export(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test exporting query history"""
        
        response = await client.get(
            "/api/v1/queries/export?format=json",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "queries" in data
        assert "export_date" in data
        assert "total_queries" in data
    
    @pytest.mark.asyncio
    async def test_query_analytics(
        self,
        client: AsyncClient,
        test_query,
        auth_headers: dict
    ):
        """Test query analytics endpoint"""
        
        response = await client.get(
            "/api/v1/queries/analytics?days=30",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "total_queries" in data
        assert "avg_trust_score" in data
        assert "avg_response_time" in data
        assert "query_trends" in data
    
    @pytest.mark.asyncio
    async def test_rate_limiting(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test rate limiting on query endpoints"""
        
        # This would require implementing rate limiting middleware
        # For now, just test that multiple requests work
        
        for i in range(5):
            response = await client.post(
                "/api/v1/queries/ask",
                json={"question": f"Test question {i}"},
                headers=auth_headers
            )
            
            # Should succeed for reasonable number of requests
            assert response.status_code in [200, 429]  # 429 if rate limited
    
    @pytest.mark.asyncio
    async def test_query_validation(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test query input validation"""
        
        # Test very long question
        long_question = "What is AI? " * 1000  # Very long question
        
        response = await client.post(
            "/api/v1/queries/ask",
            json={"question": long_question},
            headers=auth_headers
        )
        
        # Should either succeed or return validation error
        assert response.status_code in [200, 422]
        
        # Test question with special characters
        special_question = "What is AI? <script>alert('test')</script>"
        
        response = await client.post(
            "/api/v1/queries/ask",
            json={"question": special_question},
            headers=auth_headers
        )
        
        assert response.status_code in [200, 422]
    
    @pytest.mark.asyncio
    async def test_concurrent_queries(
        self,
        client: AsyncClient,
        auth_headers: dict
    ):
        """Test handling concurrent queries"""
        
        import asyncio
        
        async def make_query(question: str):
            return await client.post(
                "/api/v1/queries/ask",
                json={"question": question},
                headers=auth_headers
            )
        
        # Make multiple concurrent requests
        tasks = [
            make_query(f"What is question {i}?")
            for i in range(3)
        ]
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed or fail gracefully
        for response in responses:
            if not isinstance(response, Exception):
                assert response.status_code in [200, 429, 500]
