"""
Query/RAG endpoints
"""

import asyncio
from typing import List, Optional, AsyncGenerator
import json

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import get_logger
from app.db.database import get_db
from app.services.rag.engine import get_rag_engine
from app.core.exceptions import LLMError, VectorStoreError

logger = get_logger(__name__)
router = APIRouter()


class QueryRequest(BaseModel):
    """Request model for RAG queries"""
    question: str = Field(..., min_length=1, max_length=1000, description="The question to ask")
    document_ids: Optional[List[str]] = Field(None, description="Optional list of document IDs to search within")
    top_k: Optional[int] = Field(5, ge=1, le=20, description="Number of context chunks to retrieve")
    include_sources: bool = Field(True, description="Whether to include source information in response")
    stream: bool = Field(False, description="Whether to stream the response")


class QueryResponse(BaseModel):
    """Response model for RAG queries"""
    query_id: str
    question: str
    answer: str
    trust_score: float
    trust_factors: dict
    sources: List[dict]
    response_time: float
    tenant_id: str
    user_id: str


@router.post("/ask", response_model=QueryResponse)
async def ask_question(
    request: QueryRequest,
    db: AsyncSession = Depends(get_db)
):
    """Ask a question using RAG"""
    try:
        # TODO: Add authentication and get tenant_id, user_id from token
        tenant_id = "default-tenant"  # Placeholder
        user_id = "default-user"      # Placeholder

        # Get RAG engine
        rag_engine = get_rag_engine()

        # Handle streaming vs non-streaming
        if request.stream:
            # Return streaming response
            return StreamingResponse(
                stream_rag_response(
                    rag_engine,
                    request,
                    tenant_id,
                    user_id
                ),
                media_type="text/plain"
            )
        else:
            # Process query
            result = await rag_engine.query_documents(
                question=request.question,
                tenant_id=tenant_id,
                user_id=user_id,
                document_ids=request.document_ids,
                top_k=request.top_k,
                include_sources=request.include_sources,
                stream=False
            )

            return result

    except LLMError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"LLM service error: {str(e)}"
        )
    except VectorStoreError as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Vector store error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Query processing failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Query processing failed: {str(e)}"
        )


async def stream_rag_response(
    rag_engine,
    request: QueryRequest,
    tenant_id: str,
    user_id: str
) -> AsyncGenerator[str, None]:
    """Stream RAG response"""
    try:
        # Process query with streaming
        async for chunk in rag_engine.query_documents(
            question=request.question,
            tenant_id=tenant_id,
            user_id=user_id,
            document_ids=request.document_ids,
            top_k=request.top_k,
            include_sources=request.include_sources,
            stream=True
        ):
            # Send each chunk as JSON
            yield f"data: {json.dumps(chunk)}\n\n"

    except Exception as e:
        error_chunk = {
            "type": "error",
            "error": str(e)
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"


@router.get("/")
async def list_queries(
    page: int = 1,
    size: int = 20,
    db: AsyncSession = Depends(get_db)
):
    """List query history with pagination"""
    try:
        # TODO: Add authentication and get tenant_id, user_id from token
        tenant_id = "default-tenant"  # Placeholder
        user_id = "default-user"      # Placeholder

        # TODO: Implement database query to fetch query history
        # This would query the queries table with pagination

        # Placeholder response
        return {
            "items": [],
            "total": 0,
            "page": page,
            "size": size,
            "pages": 0
        }

    except Exception as e:
        logger.error(f"Failed to list queries: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve query history"
        )


@router.get("/{query_id}")
async def get_query(
    query_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get query details"""
    try:
        # TODO: Add authentication and get tenant_id, user_id from token
        tenant_id = "default-tenant"  # Placeholder
        user_id = "default-user"      # Placeholder

        # TODO: Implement database query to fetch specific query
        # This would query the queries table by ID, tenant, and user

        # Placeholder response
        return {
            "query_id": query_id,
            "message": "Query details endpoint not fully implemented"
        }

    except Exception as e:
        logger.error(f"Failed to get query {query_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve query details"
        )


@router.post("/test")
async def test_rag_system():
    """Test RAG system health"""
    try:
        # Get RAG engine
        rag_engine = get_rag_engine()

        # Perform health check
        health_status = await rag_engine.health_check()

        if health_status["status"] != "healthy":
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="RAG system is not healthy"
            )

        return {
            "status": "healthy",
            "message": "RAG system is working correctly",
            "components": health_status["components"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"RAG system test failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"RAG system test failed: {str(e)}"
        )
