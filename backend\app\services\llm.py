"""
LLM service for generating responses
"""

import asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import json

from openai import AsyncOpenAI

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class LLMService:
    """Service for generating responses using LLM"""
    
    def __init__(self):
        self.logger = get_logger("llm")
        self.client: Optional[AsyncOpenAI] = None
        self.model = getattr(settings, 'OPENAI_MODEL', 'gpt-4')
        self.max_tokens = getattr(settings, 'OPENAI_MAX_TOKENS', 4000)
        self.temperature = getattr(settings, 'OPENAI_TEMPERATURE', 0.1)
        
        # Initialize OpenAI client
        self._init_client()
    
    def _init_client(self):
        """Initialize OpenAI client"""
        try:
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY and settings.OPENAI_API_KEY != "mock-openai-key":
                self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
                self.logger.info("OpenAI LLM client initialized")
            else:
                self.logger.warning("OpenAI API key not configured, using mock responses")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI LLM client: {e}")
            self.client = None
    
    async def generate_response(
        self,
        query: str,
        context_chunks: List[Dict[str, Any]],
        system_prompt: Optional[str] = None,
        include_sources: bool = True
    ) -> Dict[str, Any]:
        """Generate response using LLM with context"""
        try:
            # Build context from chunks
            context = self._build_context(context_chunks)
            
            # Build prompt
            prompt = self._build_prompt(query, context, system_prompt)
            
            if self.client:
                # Generate response using OpenAI
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt or self._get_default_system_prompt()},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=self.max_tokens,
                    temperature=self.temperature
                )
                
                answer = response.choices[0].message.content
                
                # Calculate confidence based on response characteristics
                confidence = self._calculate_confidence(answer, context_chunks)
                
            else:
                # Generate mock response
                answer, confidence = self._generate_mock_response(query, context_chunks)
            
            # Prepare sources
            sources = []
            if include_sources:
                sources = self._prepare_sources(context_chunks)
            
            return {
                "answer": answer,
                "confidence": confidence,
                "sources": sources,
                "context_used": len(context_chunks),
                "model": self.model,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to generate response: {e}")
            raise
    
    async def generate_response_stream(
        self,
        query: str,
        context_chunks: List[Dict[str, Any]],
        system_prompt: Optional[str] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate streaming response"""
        try:
            # Build context and prompt
            context = self._build_context(context_chunks)
            prompt = self._build_prompt(query, context, system_prompt)
            
            if self.client:
                # Stream response from OpenAI
                stream = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt or self._get_default_system_prompt()},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=self.max_tokens,
                    temperature=self.temperature,
                    stream=True
                )
                
                full_response = ""
                async for chunk in stream:
                    if chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        full_response += content
                        
                        yield {
                            "type": "content",
                            "content": content,
                            "timestamp": datetime.utcnow().isoformat()
                        }
                
                # Send final metadata
                confidence = self._calculate_confidence(full_response, context_chunks)
                sources = self._prepare_sources(context_chunks)
                
                yield {
                    "type": "metadata",
                    "confidence": confidence,
                    "sources": sources,
                    "context_used": len(context_chunks),
                    "model": self.model,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
            else:
                # Mock streaming response
                mock_response, confidence = self._generate_mock_response(query, context_chunks)
                
                # Simulate streaming by sending words
                words = mock_response.split()
                for word in words:
                    yield {
                        "type": "content",
                        "content": word + " ",
                        "timestamp": datetime.utcnow().isoformat()
                    }
                    await asyncio.sleep(0.05)  # Simulate typing delay
                
                # Send final metadata
                sources = self._prepare_sources(context_chunks)
                yield {
                    "type": "metadata",
                    "confidence": confidence,
                    "sources": sources,
                    "context_used": len(context_chunks),
                    "model": self.model,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Failed to generate streaming response: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def _build_context(self, context_chunks: List[Dict[str, Any]]) -> str:
        """Build context string from chunks"""
        if not context_chunks:
            return "No relevant context found."
        
        context_parts = []
        for i, chunk in enumerate(context_chunks, 1):
            content = chunk.get("content", "")
            metadata = chunk.get("metadata", {})
            
            # Add source information
            source_info = ""
            if "document_title" in metadata:
                source_info = f"[Source: {metadata['document_title']}]"
            elif "filename" in metadata:
                source_info = f"[Source: {metadata['filename']}]"
            
            context_parts.append(f"Context {i}: {source_info}\n{content}")
        
        return "\n\n".join(context_parts)
    
    def _build_prompt(self, query: str, context: str, system_prompt: Optional[str] = None) -> str:
        """Build prompt for LLM"""
        prompt = f"""Based on the following context, please answer the question. If the context doesn't contain enough information to answer the question, please say so clearly.

Context:
{context}

Question: {query}

Please provide a comprehensive answer based on the context provided. Include specific details and cite relevant information from the context when possible."""
        
        return prompt
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt"""
        return """You are an AI assistant that helps answer questions based on provided context. 

Guidelines:
- Always base your answers on the provided context
- If the context doesn't contain enough information, clearly state this
- Be accurate and specific in your responses
- Cite relevant parts of the context when appropriate
- If you're uncertain about something, express that uncertainty
- Provide helpful and comprehensive answers when possible"""
    
    def _calculate_confidence(self, answer: str, context_chunks: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the response"""
        # Simple confidence calculation based on various factors
        confidence = 0.5  # Base confidence
        
        # Increase confidence if answer is longer (more detailed)
        if len(answer) > 100:
            confidence += 0.1
        
        # Increase confidence if we have multiple context chunks
        if len(context_chunks) > 1:
            confidence += 0.1
        
        # Increase confidence if context chunks have high relevance scores
        avg_score = sum(chunk.get("score", 0.5) for chunk in context_chunks) / max(len(context_chunks), 1)
        confidence += avg_score * 0.3
        
        # Decrease confidence if answer contains uncertainty phrases
        uncertainty_phrases = ["i don't know", "not sure", "unclear", "insufficient information"]
        if any(phrase in answer.lower() for phrase in uncertainty_phrases):
            confidence -= 0.2
        
        return max(0.0, min(1.0, confidence))
    
    def _prepare_sources(self, context_chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare source information from context chunks"""
        sources = []
        
        for chunk in context_chunks:
            metadata = chunk.get("metadata", {})
            
            source = {
                "chunk_id": chunk.get("id", "unknown"),
                "relevance_score": chunk.get("score", 0.0),
                "content_preview": chunk.get("content", "")[:200] + "..." if len(chunk.get("content", "")) > 200 else chunk.get("content", "")
            }
            
            # Add document information if available
            if "document_id" in metadata:
                source["document_id"] = metadata["document_id"]
            if "document_title" in metadata:
                source["document_title"] = metadata["document_title"]
            if "filename" in metadata:
                source["filename"] = metadata["filename"]
            if "page_number" in metadata:
                source["page_number"] = metadata["page_number"]
            
            sources.append(source)
        
        return sources
    
    def _generate_mock_response(self, query: str, context_chunks: List[Dict[str, Any]]) -> tuple[str, float]:
        """Generate mock response for development"""
        # Simple mock response based on query keywords
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["ai", "artificial intelligence", "machine learning"]):
            answer = "Artificial Intelligence (AI) is a broad field of computer science focused on creating systems that can perform tasks that typically require human intelligence. This includes machine learning, natural language processing, computer vision, and robotics."
            confidence = 0.85
        elif any(word in query_lower for word in ["rag", "retrieval", "augmented", "generation"]):
            answer = "Retrieval-Augmented Generation (RAG) is a powerful technique that combines information retrieval with language generation to provide more accurate and contextual responses. It retrieves relevant documents and uses them as context for generating answers."
            confidence = 0.88
        elif any(word in query_lower for word in ["document", "processing", "upload"]):
            answer = "Document processing involves extracting, analyzing, and organizing information from various document formats. This typically includes text extraction, chunking, embedding generation, and indexing for efficient retrieval."
            confidence = 0.82
        else:
            answer = "I don't have specific information about that topic in my knowledge base. Please try asking about AI, machine learning, or RAG implementation."
            confidence = 0.3
        
        return answer, confidence
    
    async def health_check(self) -> bool:
        """Check if LLM service is healthy"""
        try:
            if self.client:
                # Test with a simple completion
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=10
                )
                return bool(response.choices[0].message.content)
            else:
                # Mock mode is always healthy
                return True
                
        except Exception as e:
            self.logger.error(f"LLM service health check failed: {e}")
            return False


# Global LLM service instance
llm_service = LLMService()


def get_llm_service() -> LLMService:
    """Get LLM service instance"""
    return llm_service
