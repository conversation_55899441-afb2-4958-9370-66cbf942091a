"""
Base connector interface for data source integrations
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from app.core.logging import get_logger

logger = get_logger(__name__)


class ConnectorStatus(Enum):
    """Connector status enumeration"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    SYNCING = "syncing"


@dataclass
class ConnectorDocument:
    """Document from external data source"""
    id: str
    title: str
    content: str
    file_type: str
    file_size: int
    modified_at: datetime
    created_at: datetime
    metadata: Dict[str, Any]
    source_path: str
    checksum: Optional[str] = None


@dataclass
class SyncResult:
    """Result of a sync operation"""
    success: bool
    documents_processed: int
    documents_added: int
    documents_updated: int
    documents_failed: int
    errors: List[str]
    duration_seconds: float
    last_sync_token: Optional[str] = None


class BaseConnector(ABC):
    """Base class for all data source connectors"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.status = ConnectorStatus.DISCONNECTED
        self.last_error: Optional[str] = None
        self.logger = get_logger(f"connector.{self.__class__.__name__.lower()}")
    
    @abstractmethod
    async def connect(self) -> bool:
        """Establish connection to the data source"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """Disconnect from the data source"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> bool:
        """Test if connection is working"""
        pass
    
    @abstractmethod
    async def list_documents(
        self, 
        path: str = "/",
        recursive: bool = True,
        modified_since: Optional[datetime] = None
    ) -> AsyncGenerator[ConnectorDocument, None]:
        """List documents from the data source"""
        pass
    
    @abstractmethod
    async def get_document_content(self, document_id: str) -> bytes:
        """Get document content by ID"""
        pass
    
    @abstractmethod
    async def sync_incremental(
        self, 
        last_sync_token: Optional[str] = None
    ) -> SyncResult:
        """Perform incremental sync"""
        pass
    
    @abstractmethod
    async def sync_full(self) -> SyncResult:
        """Perform full sync"""
        pass
    
    async def get_status(self) -> Dict[str, Any]:
        """Get connector status information"""
        return {
            "status": self.status.value,
            "last_error": self.last_error,
            "config_valid": await self.validate_config(),
            "connected": self.status == ConnectorStatus.CONNECTED
        }
    
    async def validate_config(self) -> bool:
        """Validate connector configuration"""
        required_fields = self.get_required_config_fields()
        
        for field in required_fields:
            if field not in self.config or not self.config[field]:
                self.last_error = f"Missing required configuration field: {field}"
                return False
        
        return True
    
    @abstractmethod
    def get_required_config_fields(self) -> List[str]:
        """Get list of required configuration fields"""
        pass
    
    @abstractmethod
    def get_config_schema(self) -> Dict[str, Any]:
        """Get configuration schema for UI"""
        pass
    
    def set_status(self, status: ConnectorStatus, error: Optional[str] = None):
        """Set connector status"""
        self.status = status
        if error:
            self.last_error = error
            self.logger.error(f"Connector error: {error}")
        elif status == ConnectorStatus.CONNECTED:
            self.last_error = None
            self.logger.info("Connector connected successfully")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        try:
            if self.status == ConnectorStatus.DISCONNECTED:
                await self.connect()
            
            is_healthy = await self.test_connection()
            
            return {
                "healthy": is_healthy,
                "status": self.status.value,
                "last_error": self.last_error,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.set_status(ConnectorStatus.ERROR, str(e))
            return {
                "healthy": False,
                "status": self.status.value,
                "last_error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }


class ConnectorFactory:
    """Factory for creating connector instances"""
    
    _connectors: Dict[str, type] = {}
    
    @classmethod
    def register(cls, connector_type: str, connector_class: type):
        """Register a connector class"""
        cls._connectors[connector_type] = connector_class
    
    @classmethod
    def create(cls, connector_type: str, config: Dict[str, Any]) -> BaseConnector:
        """Create a connector instance"""
        if connector_type not in cls._connectors:
            raise ValueError(f"Unknown connector type: {connector_type}")
        
        connector_class = cls._connectors[connector_type]
        return connector_class(config)
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """Get list of available connector types"""
        return list(cls._connectors.keys())
    
    @classmethod
    def get_connector_info(cls, connector_type: str) -> Dict[str, Any]:
        """Get information about a connector type"""
        if connector_type not in cls._connectors:
            raise ValueError(f"Unknown connector type: {connector_type}")
        
        connector_class = cls._connectors[connector_type]
        instance = connector_class({})  # Temporary instance for schema
        
        return {
            "type": connector_type,
            "name": getattr(connector_class, "DISPLAY_NAME", connector_type),
            "description": getattr(connector_class, "DESCRIPTION", ""),
            "config_schema": instance.get_config_schema(),
            "required_fields": instance.get_required_config_fields()
        }


# Utility functions
def calculate_checksum(content: bytes) -> str:
    """Calculate checksum for content"""
    import hashlib
    return hashlib.md5(content).hexdigest()


def extract_file_metadata(file_path: str, content: bytes) -> Dict[str, Any]:
    """Extract metadata from file"""
    import os
    
    metadata = {
        "file_extension": os.path.splitext(file_path)[1].lower(),
        "file_size": len(content),
        "checksum": calculate_checksum(content)
    }
    
    # Add MIME type detection
    try:
        import magic
        metadata["mime_type"] = magic.from_buffer(content, mime=True)
    except ImportError:
        # Fallback MIME type detection
        extension_map = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".doc": "application/msword",
            ".txt": "text/plain",
            ".md": "text/markdown",
            ".html": "text/html",
            ".htm": "text/html"
        }
        metadata["mime_type"] = extension_map.get(
            metadata["file_extension"], 
            "application/octet-stream"
        )
    
    return metadata


def is_supported_file_type(file_path: str) -> bool:
    """Check if file type is supported"""
    supported_extensions = {
        ".pdf", ".docx", ".doc", ".txt", ".md", ".html", ".htm"
    }
    
    import os
    extension = os.path.splitext(file_path)[1].lower()
    return extension in supported_extensions
