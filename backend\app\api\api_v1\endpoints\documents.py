"""
Document management endpoints
"""

import os
import tempfile
import uuid
from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import get_logger
from app.db.database import get_db
from app.services.rag.engine import get_rag_engine
from app.services.ragflow.manager import get_ragflow_manager
from app.services.document_processor import get_document_processor
from app.core.exceptions import DocumentProcessingError, VectorStoreError

logger = get_logger(__name__)
router = APIRouter()


@router.post("/upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db)
):
    """Upload and process a document"""
    try:
        # TODO: Add authentication and get tenant_id, user_id from token
        tenant_id = "default-tenant"  # Placeholder
        user_id = "default-user"      # Placeholder

        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )

        # Read file content
        file_content = await file.read()

        # Use enhanced document processor
        try:
            document_processor = get_document_processor()

            document = await document_processor.process_document(
                file_content=file_content,
                filename=file.filename,
                tenant_id=tenant_id,
                user_id=user_id,
                title=title,
                db=db
            )

            return {
                "message": "Document uploaded successfully",
                "document": {
                    "id": str(document.id),
                    "title": document.title,
                    "filename": document.filename,
                    "status": document.status,
                    "file_type": document.file_type,
                    "file_size": document.file_size,
                    "created_at": document.created_at.isoformat()
                }
            }

        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e)
            )



    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Document upload failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Upload failed: {str(e)}"
        )


async def process_document_background(
    rag_engine,
    file_path: str,
    filename: str,
    tenant_id: str,
    user_id: str,
    document_id: str,
    title: Optional[str]
):
    """Background task for document processing"""
    try:
        logger.info(f"Starting background processing for document {document_id}")

        # Process document
        result = await rag_engine.process_document(
            file_path=file_path,
            filename=filename,
            tenant_id=tenant_id,
            user_id=user_id,
            document_id=document_id
        )

        # TODO: Save document metadata to database
        # This would include storing the result in the documents table

        logger.info(f"Document processing completed: {document_id}")

    except Exception as e:
        logger.error(f"Background document processing failed: {e}")
        # TODO: Update document status to 'failed' in database

    finally:
        # Clean up temporary file
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
            temp_dir = os.path.dirname(file_path)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
        except Exception as e:
            logger.warning(f"Failed to clean up temp files: {e}")


@router.get("/")
async def list_documents(
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None,
    search: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """List documents with pagination and filtering"""
    try:
        # TODO: Add authentication and get tenant_id from token
        tenant_id = "default-tenant"  # Placeholder

        # TODO: Implement database query to fetch documents
        # This would query the documents table with filters

        # Placeholder response
        return {
            "items": [],
            "total": 0,
            "page": page,
            "size": size,
            "pages": 0
        }

    except Exception as e:
        logger.error(f"Failed to list documents: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve documents"
        )


@router.get("/{document_id}")
async def get_document(
    document_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get document details"""
    try:
        # TODO: Add authentication and get tenant_id from token
        tenant_id = "default-tenant"  # Placeholder

        # TODO: Implement database query to fetch document
        # This would query the documents table by ID and tenant

        # Placeholder response
        return {
            "document_id": document_id,
            "message": "Document endpoint not fully implemented"
        }

    except Exception as e:
        logger.error(f"Failed to get document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document"
        )


@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Delete a document"""
    try:
        # TODO: Add authentication and get tenant_id from token
        tenant_id = "default-tenant"  # Placeholder

        # Get RAG engine
        rag_engine = get_rag_engine()

        # Delete from vector store
        success = await rag_engine.delete_document(tenant_id, document_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        # TODO: Delete from database
        # This would delete the document record from the documents table

        return {"message": "Document deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.get("/{document_id}/chunks")
async def get_document_chunks(
    document_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get all chunks for a document"""
    try:
        # TODO: Add authentication and get tenant_id from token
        tenant_id = "default-tenant"  # Placeholder

        # Get RAG engine
        rag_engine = get_rag_engine()

        # Get chunks from vector store
        chunks = await rag_engine.get_document_chunks(tenant_id, document_id)

        return {
            "document_id": document_id,
            "chunks": chunks,
            "chunk_count": len(chunks)
        }

    except VectorStoreError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to get chunks for document {document_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document chunks"
        )
