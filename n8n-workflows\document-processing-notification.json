{"name": "Document Processing Notification", "nodes": [{"parameters": {"httpMethod": "POST", "path": "document-processed", "responseMode": "onReceived"}, "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [250, 300], "webhookId": "aithentiq-doc-processed"}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"status\"]}}", "operation": "equal", "value2": "processed"}]}}, "name": "Check Status", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpBasicAuth", "url": "={{$json[\"notification_url\"]}}", "options": {}, "bodyParametersUi": {"parameter": [{"name": "document_id", "value": "={{$json[\"document_id\"]}}"}, {"name": "status", "value": "={{$json[\"status\"]}}"}, {"name": "message", "value": "Document processing completed successfully"}, {"name": "document_title", "value": "={{$json[\"document_title\"]}}"}, {"name": "chunk_count", "value": "={{$json[\"chunk_count\"]}}"}, {"name": "processing_time", "value": "={{$json[\"processing_time\"]}}"}]}}, "name": "Send Success Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [650, 200]}, {"parameters": {"authentication": "generic", "genericAuthType": "httpBasicAuth", "url": "={{$json[\"notification_url\"]}}", "options": {}, "bodyParametersUi": {"parameter": [{"name": "document_id", "value": "={{$json[\"document_id\"]}}"}, {"name": "status", "value": "={{$json[\"status\"]}}"}, {"name": "error", "value": "={{$json[\"error\"]}}"}, {"name": "message", "value": "Document processing failed"}]}}, "name": "Send Error Notification", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [650, 400]}, {"parameters": {"channel": "={{$json[\"slack_channel\"] || '#general'}}", "text": "📄 Document Processing Update\n\n*Document:* {{$json[\"document_title\"]}}\n*Status:* {{$json[\"status\"]}}\n*Tenant:* {{$json[\"tenant_name\"]}}\n\n{{$json[\"status\"] === 'processed' ? '✅ Successfully processed with ' + $json[\"chunk_count\"] + ' chunks in ' + $json[\"processing_time\"] + 's' : '❌ Processing failed: ' + $json[\"error\"]}}", "username": "AIthentiq<PERSON><PERSON>", "iconEmoji": ":robot_face:"}, "name": "Send Slack Notification", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [850, 300]}], "connections": {"Webhook": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Send Success Notification", "type": "main", "index": 0}, {"node": "Send Slack Notification", "type": "main", "index": 0}], [{"node": "Send Error Notification", "type": "main", "index": 0}, {"node": "Send Slack Notification", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC"}, "id": "1", "tags": [{"name": "aithentiqmind", "id": "1"}, {"name": "notifications", "id": "2"}]}