"""
LLM providers for generating responses in RAG pipeline
"""

import asyncio
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, AsyncGenerator
import json

import openai
import httpx
from langchain.schema import BaseMessage, HumanMessage, SystemMessage

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import LLMError

logger = get_logger(__name__)


class BaseLLMProvider(ABC):
    """Base class for LLM providers"""
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None,
        stream: bool = False
    ) -> str:
        """Generate response from messages"""
        pass
    
    @abstractmethod
    async def generate_streaming_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        pass


class OpenAILLMProvider(BaseLLMProvider):
    """OpenAI LLM provider"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.model = model or settings.OPENAI_MODEL
        
        if not self.api_key:
            raise LLMError("OpenAI API key not provided")
        
        self.client = openai.AsyncOpenAI(api_key=self.api_key)
    
    def _convert_messages(self, messages: List[BaseMessage]) -> List[Dict[str, str]]:
        """Convert LangChain messages to OpenAI format"""
        openai_messages = []
        
        for message in messages:
            if isinstance(message, SystemMessage):
                role = "system"
            elif isinstance(message, HumanMessage):
                role = "user"
            else:
                role = "assistant"
            
            openai_messages.append({
                "role": role,
                "content": message.content
            })
        
        return openai_messages
    
    async def generate_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None,
        stream: bool = False
    ) -> str:
        """Generate response using OpenAI"""
        try:
            openai_messages = self._convert_messages(messages)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                temperature=temperature,
                max_tokens=max_tokens or settings.OPENAI_MAX_TOKENS,
                stream=stream
            )
            
            if stream:
                # Handle streaming response
                content = ""
                async for chunk in response:
                    if chunk.choices[0].delta.content:
                        content += chunk.choices[0].delta.content
                return content
            else:
                return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI response generation failed: {e}")
            raise LLMError(f"OpenAI API error: {str(e)}")
    
    async def generate_streaming_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        try:
            openai_messages = self._convert_messages(messages)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=openai_messages,
                temperature=temperature,
                max_tokens=max_tokens or settings.OPENAI_MAX_TOKENS,
                stream=True
            )
            
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"OpenAI streaming failed: {e}")
            raise LLMError(f"OpenAI streaming error: {str(e)}")


class OllamaLLMProvider(BaseLLMProvider):
    """Ollama LLM provider for local models"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or settings.OLLAMA_BASE_URL
        self.model = model or settings.OLLAMA_MODEL
        self.client = httpx.AsyncClient(timeout=120.0)
    
    def _convert_messages(self, messages: List[BaseMessage]) -> str:
        """Convert messages to Ollama prompt format"""
        prompt_parts = []
        
        for message in messages:
            if isinstance(message, SystemMessage):
                prompt_parts.append(f"System: {message.content}")
            elif isinstance(message, HumanMessage):
                prompt_parts.append(f"Human: {message.content}")
            else:
                prompt_parts.append(f"Assistant: {message.content}")
        
        return "\n\n".join(prompt_parts) + "\n\nAssistant:"
    
    async def generate_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None,
        stream: bool = False
    ) -> str:
        """Generate response using Ollama"""
        try:
            prompt = self._convert_messages(messages)
            
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens or 2000
                }
            }
            
            if stream:
                content = ""
                async with self.client.stream(
                    "POST",
                    f"{self.base_url}/api/generate",
                    json=payload
                ) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line:
                            chunk_data = json.loads(line)
                            if "response" in chunk_data:
                                content += chunk_data["response"]
                return content
            else:
                response = await self.client.post(
                    f"{self.base_url}/api/generate",
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result["response"]
                
        except Exception as e:
            logger.error(f"Ollama response generation failed: {e}")
            raise LLMError(f"Ollama API error: {str(e)}")
    
    async def generate_streaming_response(
        self,
        messages: List[BaseMessage],
        temperature: float = 0.1,
        max_tokens: int = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming response"""
        try:
            prompt = self._convert_messages(messages)
            
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens or 2000
                }
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/generate",
                json=payload
            ) as response:
                response.raise_for_status()
                async for line in response.aiter_lines():
                    if line:
                        chunk_data = json.loads(line)
                        if "response" in chunk_data:
                            yield chunk_data["response"]
                            
        except Exception as e:
            logger.error(f"Ollama streaming failed: {e}")
            raise LLMError(f"Ollama streaming error: {str(e)}")


class LLMService:
    """Main LLM service that manages different providers"""
    
    def __init__(self, provider_type: str = "openai"):
        self.provider_type = provider_type
        self.provider = self._create_provider(provider_type)
    
    def _create_provider(self, provider_type: str) -> BaseLLMProvider:
        """Create LLM provider based on type"""
        if provider_type == "openai":
            return OpenAILLMProvider()
        elif provider_type == "ollama":
            return OllamaLLMProvider()
        else:
            raise LLMError(f"Unsupported LLM provider: {provider_type}")
    
    async def generate_rag_response(
        self,
        question: str,
        context_chunks: List[Dict[str, Any]],
        system_prompt: str = None,
        temperature: float = None,
        stream: bool = False
    ) -> str:
        """
        Generate RAG response with context
        
        Args:
            question: User question
            context_chunks: Retrieved context chunks
            system_prompt: Optional system prompt
            temperature: Response randomness
            stream: Whether to stream response
            
        Returns:
            Generated response
        """
        try:
            # Build context from chunks
            context_text = self._build_context(context_chunks)
            
            # Create system prompt
            if not system_prompt:
                system_prompt = self._get_default_system_prompt()
            
            # Create user prompt with context
            user_prompt = self._build_user_prompt(question, context_text)
            
            # Create messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Generate response
            response = await self.provider.generate_response(
                messages=messages,
                temperature=temperature or settings.OPENAI_TEMPERATURE,
                stream=stream
            )
            
            logger.info(f"Generated RAG response for question: {question[:50]}...")
            return response
            
        except Exception as e:
            logger.error(f"RAG response generation failed: {e}")
            raise LLMError(f"Response generation failed: {str(e)}")
    
    async def generate_streaming_rag_response(
        self,
        question: str,
        context_chunks: List[Dict[str, Any]],
        system_prompt: str = None,
        temperature: float = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming RAG response"""
        try:
            # Build context from chunks
            context_text = self._build_context(context_chunks)
            
            # Create system prompt
            if not system_prompt:
                system_prompt = self._get_default_system_prompt()
            
            # Create user prompt with context
            user_prompt = self._build_user_prompt(question, context_text)
            
            # Create messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Generate streaming response
            async for chunk in self.provider.generate_streaming_response(
                messages=messages,
                temperature=temperature or settings.OPENAI_TEMPERATURE
            ):
                yield chunk
                
        except Exception as e:
            logger.error(f"Streaming RAG response failed: {e}")
            raise LLMError(f"Streaming response failed: {str(e)}")
    
    def _build_context(self, context_chunks: List[Dict[str, Any]]) -> str:
        """Build context text from chunks"""
        context_parts = []
        
        for i, chunk in enumerate(context_chunks, 1):
            content = chunk.get("content", "")
            metadata = chunk.get("metadata", {})
            
            # Add source information
            source_info = f"Source {i}"
            if metadata.get("filename"):
                source_info += f" ({metadata['filename']})"
            
            context_parts.append(f"{source_info}:\n{content}")
        
        return "\n\n".join(context_parts)
    
    def _build_user_prompt(self, question: str, context: str) -> str:
        """Build user prompt with question and context"""
        return f"""Based on the following context, please answer the question. If the answer cannot be found in the context, please say so.

Context:
{context}

Question: {question}

Answer:"""
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt for RAG"""
        return """You are a helpful AI assistant that answers questions based on provided context. 

Guidelines:
1. Answer questions accurately based only on the provided context
2. If information is not in the context, clearly state that you don't have enough information
3. Cite specific sources when possible
4. Be concise but comprehensive
5. If the context contains conflicting information, acknowledge this
6. Maintain a professional and helpful tone"""
    
    async def test_connection(self) -> bool:
        """Test if the LLM service is working"""
        try:
            test_messages = [
                SystemMessage(content="You are a helpful assistant."),
                HumanMessage(content="Say 'Hello, I am working correctly!'")
            ]
            
            response = await self.provider.generate_response(test_messages)
            
            if not response:
                return False
            
            logger.info(f"LLM service test successful: {response[:50]}...")
            return True
            
        except Exception as e:
            logger.error(f"LLM service test failed: {e}")
            return False
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the LLM provider"""
        return {
            "provider": self.provider_type,
            "model": getattr(self.provider, 'model', 'unknown'),
            "base_url": getattr(self.provider, 'base_url', None)
        }


# Factory function
def create_llm_service(provider_type: str = None) -> LLMService:
    """Create LLM service with specified provider"""
    if provider_type is None:
        # Auto-detect based on available configuration
        if settings.OPENAI_API_KEY:
            provider_type = "openai"
        elif settings.OLLAMA_BASE_URL:
            provider_type = "ollama"
        else:
            raise LLMError("No LLM provider configured")
    
    return LLMService(provider_type)
