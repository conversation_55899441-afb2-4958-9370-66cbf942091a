"""
RagFlow integration manager for AIthentiqMind
"""

import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import get_logger
from app.models.tenant import Tenant
from app.models.document import Document
from app.models.query import Query
from app.services.ragflow.client import get_ragflow_client
from app.services.audit import get_audit_service

logger = get_logger(__name__)


class RagFlowManager:
    """Manager for RagFlow operations integrated with AIthentiqMind"""
    
    def __init__(self):
        self.logger = get_logger("ragflow_manager")
    
    async def setup_tenant_knowledge_base(
        self, 
        tenant_id: str, 
        tenant_name: str,
        db: AsyncSession
    ) -> str:
        """Set up knowledge base for tenant"""
        try:
            client = await get_ragflow_client()
            
            # Create knowledge base
            kb_data = await client.create_knowledge_base(
                tenant_id=tenant_id,
                name=tenant_name.replace(" ", "_").lower(),
                description=f"Knowledge base for {tenant_name}"
            )
            
            kb_id = kb_data.get("id")
            
            # Update tenant with RagFlow KB ID
            await db.execute(
                update(Tenant)
                .where(Tenant.id == tenant_id)
                .values(ragflow_kb_id=kb_id)
            )
            await db.commit()
            
            self.logger.info(f"Created RagFlow KB {kb_id} for tenant {tenant_id}")
            return kb_id
            
        except Exception as e:
            self.logger.error(f"Failed to setup KB for tenant {tenant_id}: {e}")
            raise
    
    async def upload_document_to_ragflow(
        self,
        document_id: str,
        tenant_id: str,
        file_content: bytes,
        filename: str,
        db: AsyncSession
    ) -> str:
        """Upload document to RagFlow and update document record"""
        try:
            client = await get_ragflow_client()
            
            # Get tenant's knowledge base ID
            tenant_result = await db.execute(
                select(Tenant.ragflow_kb_id).where(Tenant.id == tenant_id)
            )
            kb_id = tenant_result.scalar_one_or_none()
            
            if not kb_id:
                # Create KB if it doesn't exist
                tenant_result = await db.execute(
                    select(Tenant.name).where(Tenant.id == tenant_id)
                )
                tenant_name = tenant_result.scalar_one()
                kb_id = await self.setup_tenant_knowledge_base(tenant_id, tenant_name, db)
            
            # Upload document to RagFlow
            doc_data = await client.upload_document(
                kb_id=kb_id,
                file_content=file_content,
                filename=filename,
                metadata={
                    "aithentiq_document_id": document_id,
                    "tenant_id": tenant_id
                }
            )
            
            ragflow_doc_id = doc_data.get("id")
            
            # Update document with RagFlow ID
            await db.execute(
                update(Document)
                .where(Document.id == document_id)
                .values(
                    ragflow_document_id=ragflow_doc_id,
                    ragflow_kb_id=kb_id,
                    status="uploaded_to_ragflow"
                )
            )
            await db.commit()
            
            # Start parsing
            await client.start_document_parsing(ragflow_doc_id)
            
            self.logger.info(f"Uploaded document {document_id} to RagFlow as {ragflow_doc_id}")
            return ragflow_doc_id
            
        except Exception as e:
            self.logger.error(f"Failed to upload document {document_id}: {e}")
            # Update document status to failed
            await db.execute(
                update(Document)
                .where(Document.id == document_id)
                .values(status="ragflow_upload_failed")
            )
            await db.commit()
            raise
    
    async def check_document_processing_status(
        self,
        document_id: str,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Check document processing status in RagFlow"""
        try:
            client = await get_ragflow_client()
            
            # Get document's RagFlow ID
            doc_result = await db.execute(
                select(Document.ragflow_document_id)
                .where(Document.id == document_id)
            )
            ragflow_doc_id = doc_result.scalar_one_or_none()
            
            if not ragflow_doc_id:
                return {"status": "not_uploaded", "error": "Document not uploaded to RagFlow"}
            
            # Get status from RagFlow
            status_data = await client.get_document_status(ragflow_doc_id)
            
            # Update document status based on RagFlow status
            ragflow_status = status_data.get("status", "unknown")
            
            if ragflow_status == "1":  # Processed successfully
                await db.execute(
                    update(Document)
                    .where(Document.id == document_id)
                    .values(
                        status="processed",
                        chunk_count=status_data.get("chunk_num", 0)
                    )
                )
                await db.commit()
            elif ragflow_status == "0":  # Processing
                await db.execute(
                    update(Document)
                    .where(Document.id == document_id)
                    .values(status="processing")
                )
                await db.commit()
            elif ragflow_status == "-1":  # Failed
                await db.execute(
                    update(Document)
                    .where(Document.id == document_id)
                    .values(status="processing_failed")
                )
                await db.commit()
            
            return {
                "status": ragflow_status,
                "chunk_count": status_data.get("chunk_num", 0),
                "progress": status_data.get("progress", 0),
                "ragflow_data": status_data
            }
            
        except Exception as e:
            self.logger.error(f"Failed to check document status {document_id}: {e}")
            return {"status": "error", "error": str(e)}
    
    async def query_with_ragflow(
        self,
        question: str,
        tenant_id: str,
        user_id: str,
        top_k: int = 5,
        document_filters: Optional[Dict[str, Any]] = None,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Process query using RagFlow"""
        try:
            client = await get_ragflow_client()
            audit_service = get_audit_service()
            
            # Get tenant's knowledge base ID
            tenant_result = await db.execute(
                select(Tenant.ragflow_kb_id).where(Tenant.id == tenant_id)
            )
            kb_id = tenant_result.scalar_one_or_none()
            
            if not kb_id:
                return {
                    "error": "No knowledge base found for tenant",
                    "answer": "I don't have access to any documents yet. Please upload some documents first.",
                    "sources": [],
                    "trust_score": 0.0
                }
            
            # Query RagFlow
            chat_response = await client.chat_with_knowledge_base(
                kb_id=kb_id,
                question=question
            )
            
            # Extract response data
            answer = chat_response.get("answer", "")
            reference = chat_response.get("reference", {})
            chunks = reference.get("chunks", [])
            
            # Format sources
            sources = []
            for chunk in chunks:
                sources.append({
                    "documentId": chunk.get("document_id"),
                    "title": chunk.get("document_name", "Unknown"),
                    "content": chunk.get("content", ""),
                    "relevanceScore": chunk.get("similarity", 0.0),
                    "chunkId": chunk.get("chunk_id"),
                    "metadata": {
                        "page_number": chunk.get("page_number"),
                        "positions": chunk.get("positions", [])
                    }
                })
            
            # Calculate basic trust score based on sources
            trust_score = min(1.0, len(sources) * 0.2) if sources else 0.1
            
            # Create query record
            query = Query(
                tenant_id=tenant_id,
                user_id=user_id,
                question=question,
                answer=answer,
                context_chunks=[
                    {
                        "content": chunk.get("content", ""),
                        "document_id": chunk.get("document_id"),
                        "chunk_id": chunk.get("chunk_id"),
                        "relevance_score": chunk.get("similarity", 0.0)
                    }
                    for chunk in chunks
                ],
                sources=[
                    {
                        "document_id": chunk.get("document_id"),
                        "title": chunk.get("document_name", "Unknown"),
                        "relevance_score": chunk.get("similarity", 0.0)
                    }
                    for chunk in chunks
                ],
                document_ids=[chunk.get("document_id") for chunk in chunks if chunk.get("document_id")],
                trust_score=trust_score,
                confidence_score=trust_score,
                relevance_score=sum(chunk.get("similarity", 0.0) for chunk in chunks) / len(chunks) if chunks else 0.0,
                top_k=top_k,
                model_used="ragflow",
                status="completed",
                ragflow_conversation_id=chat_response.get("conversation_id")
            )
            
            db.add(query)
            await db.commit()
            await db.refresh(query)
            
            # Log audit event
            await audit_service.log_action(
                tenant_id=tenant_id,
                user_id=user_id,
                action="query.ask",
                resource="query",
                resource_id=str(query.id),
                details={
                    "question_length": len(question),
                    "answer_length": len(answer),
                    "sources_count": len(sources),
                    "trust_score": trust_score
                }
            )
            
            return {
                "query_id": str(query.id),
                "answer": answer,
                "sources": sources,
                "trust_score": trust_score,
                "conversation_id": chat_response.get("conversation_id"),
                "ragflow_response": chat_response
            }
            
        except Exception as e:
            self.logger.error(f"Failed to query with RagFlow: {e}")
            raise
    
    async def stream_query_with_ragflow(
        self,
        question: str,
        tenant_id: str,
        user_id: str,
        conversation_id: Optional[str] = None,
        db: AsyncSession = None
    ):
        """Stream query response using RagFlow"""
        try:
            client = await get_ragflow_client()
            
            # Get tenant's knowledge base ID
            tenant_result = await db.execute(
                select(Tenant.ragflow_kb_id).where(Tenant.id == tenant_id)
            )
            kb_id = tenant_result.scalar_one_or_none()
            
            if not kb_id:
                yield {
                    "type": "error",
                    "error": "No knowledge base found for tenant"
                }
                return
            
            # Stream response from RagFlow
            async for chunk in client.stream_chat_response(
                kb_id=kb_id,
                question=question,
                conversation_id=conversation_id
            ):
                yield {
                    "type": "content",
                    "content": chunk.get("answer", ""),
                    "conversation_id": chunk.get("conversation_id")
                }
            
        except Exception as e:
            self.logger.error(f"Failed to stream query with RagFlow: {e}")
            yield {
                "type": "error",
                "error": str(e)
            }
    
    async def delete_document_from_ragflow(
        self,
        document_id: str,
        db: AsyncSession
    ) -> bool:
        """Delete document from RagFlow"""
        try:
            client = await get_ragflow_client()
            
            # Get document's RagFlow ID
            doc_result = await db.execute(
                select(Document.ragflow_document_id)
                .where(Document.id == document_id)
            )
            ragflow_doc_id = doc_result.scalar_one_or_none()
            
            if ragflow_doc_id:
                success = await client.delete_document(ragflow_doc_id)
                
                if success:
                    # Clear RagFlow IDs from document
                    await db.execute(
                        update(Document)
                        .where(Document.id == document_id)
                        .values(
                            ragflow_document_id=None,
                            ragflow_kb_id=None
                        )
                    )
                    await db.commit()
                
                return success
            
            return True  # Document wasn't in RagFlow anyway
            
        except Exception as e:
            self.logger.error(f"Failed to delete document from RagFlow: {e}")
            return False
    
    async def get_tenant_stats(self, tenant_id: str, db: AsyncSession) -> Dict[str, Any]:
        """Get tenant statistics from RagFlow"""
        try:
            client = await get_ragflow_client()
            
            # Get tenant's knowledge base ID
            tenant_result = await db.execute(
                select(Tenant.ragflow_kb_id).where(Tenant.id == tenant_id)
            )
            kb_id = tenant_result.scalar_one_or_none()
            
            if not kb_id:
                return {"error": "No knowledge base found"}
            
            stats = await client.get_knowledge_base_stats(kb_id)
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get tenant stats: {e}")
            return {"error": str(e)}


# Global manager instance
ragflow_manager = RagFlowManager()


def get_ragflow_manager() -> RagFlowManager:
    """Get RagFlow manager instance"""
    return ragflow_manager
