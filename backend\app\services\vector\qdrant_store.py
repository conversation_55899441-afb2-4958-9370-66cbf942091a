"""
Qdrant vector store service for storing and retrieving document embeddings
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
import uuid

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import (
    Distance, VectorParams, CreateCollection, PointStruct,
    Filter, FieldCondition, MatchValue, SearchRequest
)

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import VectorStoreError

logger = get_logger(__name__)


class QdrantVectorStore:
    """Qdrant vector store for document embeddings"""
    
    def __init__(self, url: str = None, api_key: str = None):
        self.url = url or settings.QDRANT_URL
        self.api_key = api_key or settings.QDRANT_API_KEY
        
        # Initialize Qdrant client
        self.client = QdrantClient(
            url=self.url,
            api_key=self.api_key,
            timeout=30
        )
        
        logger.info(f"Initialized Qdrant client: {self.url}")
    
    def _get_collection_name(self, tenant_id: str) -> str:
        """Get collection name for tenant"""
        return f"{settings.QDRANT_COLLECTION_PREFIX}_{tenant_id.replace('-', '_')}"
    
    async def create_collection(
        self, 
        tenant_id: str, 
        vector_dimension: int,
        distance_metric: Distance = Distance.COSINE
    ) -> bool:
        """Create a new collection for tenant"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Check if collection already exists
            collections = self.client.get_collections()
            existing_names = [col.name for col in collections.collections]
            
            if collection_name in existing_names:
                logger.info(f"Collection {collection_name} already exists")
                return True
            
            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_dimension,
                    distance=distance_metric
                )
            )
            
            logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection creation failed: {str(e)}")
    
    async def delete_collection(self, tenant_id: str) -> bool:
        """Delete collection for tenant"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection deletion failed: {str(e)}")
    
    async def add_documents(
        self, 
        tenant_id: str, 
        chunks: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Add document chunks to vector store
        
        Args:
            tenant_id: Tenant identifier
            chunks: List of chunks with embeddings
            
        Returns:
            List of point IDs
        """
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Prepare points for insertion
            points = []
            point_ids = []
            
            for chunk in chunks:
                point_id = str(uuid.uuid4())
                point_ids.append(point_id)
                
                # Prepare payload (metadata)
                payload = {
                    "document_id": chunk["document_id"],
                    "chunk_id": chunk["chunk_id"],
                    "chunk_index": chunk["chunk_index"],
                    "content": chunk["content"],
                    "tenant_id": tenant_id,
                    **chunk.get("metadata", {})
                }
                
                # Create point
                point = PointStruct(
                    id=point_id,
                    vector=chunk["embedding"],
                    payload=payload
                )
                points.append(point)
            
            # Insert points in batches
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                
                self.client.upsert(
                    collection_name=collection_name,
                    points=batch
                )
            
            logger.info(f"Added {len(points)} chunks to collection {collection_name}")
            return point_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents to vector store: {e}")
            raise VectorStoreError(f"Document insertion failed: {str(e)}")
    
    async def search_similar(
        self,
        tenant_id: str,
        query_embedding: List[float],
        limit: int = 5,
        score_threshold: float = 0.0,
        document_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents
        
        Args:
            tenant_id: Tenant identifier
            query_embedding: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            document_ids: Optional filter by document IDs
            
        Returns:
            List of similar chunks with scores
        """
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Prepare filter
            filter_conditions = [
                FieldCondition(
                    key="tenant_id",
                    match=MatchValue(value=tenant_id)
                )
            ]
            
            if document_ids:
                filter_conditions.append(
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_ids)
                    )
                )
            
            search_filter = Filter(must=filter_conditions) if filter_conditions else None
            
            # Perform search
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )
            
            # Format results
            results = []
            for hit in search_result:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "document_id": hit.payload.get("document_id"),
                    "chunk_id": hit.payload.get("chunk_id"),
                    "chunk_index": hit.payload.get("chunk_index"),
                    "content": hit.payload.get("content"),
                    "metadata": {
                        k: v for k, v in hit.payload.items() 
                        if k not in ["document_id", "chunk_id", "chunk_index", "content", "tenant_id"]
                    }
                }
                results.append(result)
            
            logger.info(f"Found {len(results)} similar chunks for tenant {tenant_id}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar documents: {e}")
            raise VectorStoreError(f"Similarity search failed: {str(e)}")
    
    async def delete_document(self, tenant_id: str, document_id: str) -> bool:
        """Delete all chunks for a document"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Delete points with matching document_id
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id)
                            ),
                            FieldCondition(
                                key="tenant_id",
                                match=MatchValue(value=tenant_id)
                            )
                        ]
                    )
                )
            )
            
            logger.info(f"Deleted document {document_id} from collection {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            raise VectorStoreError(f"Document deletion failed: {str(e)}")
    
    async def get_collection_info(self, tenant_id: str) -> Dict[str, Any]:
        """Get information about tenant's collection"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Get collection info
            collection_info = self.client.get_collection(collection_name)
            
            return {
                "name": collection_name,
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count,
                "points_count": collection_info.points_count,
                "segments_count": collection_info.segments_count,
                "status": collection_info.status,
                "optimizer_status": collection_info.optimizer_status,
                "disk_data_size": collection_info.disk_data_size,
                "ram_data_size": collection_info.ram_data_size,
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection info retrieval failed: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Qdrant health"""
        try:
            # Get cluster info
            cluster_info = self.client.get_cluster_info()
            
            # Get collections
            collections = self.client.get_collections()
            
            return {
                "status": "healthy",
                "cluster_status": cluster_info.status,
                "peer_count": len(cluster_info.peers),
                "collections_count": len(collections.collections),
                "url": self.url
            }
            
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "url": self.url
            }
    
    async def get_document_chunks(
        self, 
        tenant_id: str, 
        document_id: str
    ) -> List[Dict[str, Any]]:
        """Get all chunks for a specific document"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Search for all chunks of the document
            search_result = self.client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        ),
                        FieldCondition(
                            key="tenant_id",
                            match=MatchValue(value=tenant_id)
                        )
                    ]
                ),
                with_payload=True,
                with_vectors=False
            )
            
            chunks = []
            for point in search_result[0]:  # scroll returns (points, next_page_offset)
                chunk = {
                    "id": point.id,
                    "document_id": point.payload.get("document_id"),
                    "chunk_id": point.payload.get("chunk_id"),
                    "chunk_index": point.payload.get("chunk_index"),
                    "content": point.payload.get("content"),
                    "metadata": {
                        k: v for k, v in point.payload.items() 
                        if k not in ["document_id", "chunk_id", "chunk_index", "content", "tenant_id"]
                    }
                }
                chunks.append(chunk)
            
            # Sort by chunk index
            chunks.sort(key=lambda x: x.get("chunk_index", 0))
            
            logger.info(f"Retrieved {len(chunks)} chunks for document {document_id}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to get document chunks: {e}")
            raise VectorStoreError(f"Document chunks retrieval failed: {str(e)}")


# Factory function
def create_qdrant_store() -> QdrantVectorStore:
    """Create Qdrant vector store instance"""
    return QdrantVectorStore()
