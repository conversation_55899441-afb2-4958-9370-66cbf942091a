"""
Qdrant vector store service for storing and retrieving document embeddings
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
import uuid

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import (
    Distance, VectorParams, CreateCollection, PointStruct,
    Filter, FieldCondition, MatchValue, SearchRequest
)

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import VectorStoreError

logger = get_logger(__name__)


class QdrantVectorStore:
    """Qdrant vector store for document embeddings"""
    
    def __init__(self, url: str = None, api_key: str = None):
        self.url = url or settings.QDRANT_URL
        self.api_key = api_key or settings.QDRANT_API_KEY
        
        # Initialize Qdrant client
        self.client = QdrantClient(
            url=self.url,
            api_key=self.api_key,
            timeout=30
        )
        
        logger.info(f"Initialized Qdrant client: {self.url}")
    
    def _get_collection_name(self, tenant_id: str) -> str:
        """Get collection name for tenant"""
        return f"{settings.QDRANT_COLLECTION_PREFIX}_{tenant_id.replace('-', '_')}"
    
    async def create_collection(
        self, 
        tenant_id: str, 
        vector_dimension: int,
        distance_metric: Distance = Distance.COSINE
    ) -> bool:
        """Create a new collection for tenant"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Check if collection already exists
            collections = self.client.get_collections()
            existing_names = [col.name for col in collections.collections]
            
            if collection_name in existing_names:
                logger.info(f"Collection {collection_name} already exists")
                return True
            
            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=vector_dimension,
                    distance=distance_metric
                )
            )
            
            logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection creation failed: {str(e)}")
    
    async def delete_collection(self, tenant_id: str) -> bool:
        """Delete collection for tenant"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection deletion failed: {str(e)}")
    
    async def add_documents(
        self, 
        tenant_id: str, 
        chunks: List[Dict[str, Any]]
    ) -> List[str]:
        """
        Add document chunks to vector store
        
        Args:
            tenant_id: Tenant identifier
            chunks: List of chunks with embeddings
            
        Returns:
            List of point IDs
        """
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Prepare points for insertion
            points = []
            point_ids = []
            
            for chunk in chunks:
                point_id = str(uuid.uuid4())
                point_ids.append(point_id)
                
                # Prepare payload (metadata)
                payload = {
                    "document_id": chunk["document_id"],
                    "chunk_id": chunk["chunk_id"],
                    "chunk_index": chunk["chunk_index"],
                    "content": chunk["content"],
                    "tenant_id": tenant_id,
                    **chunk.get("metadata", {})
                }
                
                # Create point
                point = PointStruct(
                    id=point_id,
                    vector=chunk["embedding"],
                    payload=payload
                )
                points.append(point)
            
            # Insert points in batches
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch = points[i:i + batch_size]
                
                self.client.upsert(
                    collection_name=collection_name,
                    points=batch
                )
            
            logger.info(f"Added {len(points)} chunks to collection {collection_name}")
            return point_ids
            
        except Exception as e:
            logger.error(f"Failed to add documents to vector store: {e}")
            raise VectorStoreError(f"Document insertion failed: {str(e)}")
    
    async def search_similar(
        self,
        tenant_id: str,
        query_embedding: List[float],
        limit: int = 5,
        score_threshold: float = 0.0,
        document_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search for similar documents
        
        Args:
            tenant_id: Tenant identifier
            query_embedding: Query vector
            limit: Maximum number of results
            score_threshold: Minimum similarity score
            document_ids: Optional filter by document IDs
            
        Returns:
            List of similar chunks with scores
        """
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Prepare filter
            filter_conditions = [
                FieldCondition(
                    key="tenant_id",
                    match=MatchValue(value=tenant_id)
                )
            ]
            
            if document_ids:
                filter_conditions.append(
                    FieldCondition(
                        key="document_id",
                        match=MatchValue(value=document_ids)
                    )
                )
            
            search_filter = Filter(must=filter_conditions) if filter_conditions else None
            
            # Perform search
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )
            
            # Format results
            results = []
            for hit in search_result:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "document_id": hit.payload.get("document_id"),
                    "chunk_id": hit.payload.get("chunk_id"),
                    "chunk_index": hit.payload.get("chunk_index"),
                    "content": hit.payload.get("content"),
                    "metadata": {
                        k: v for k, v in hit.payload.items() 
                        if k not in ["document_id", "chunk_id", "chunk_index", "content", "tenant_id"]
                    }
                }
                results.append(result)
            
            logger.info(f"Found {len(results)} similar chunks for tenant {tenant_id}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar documents: {e}")
            raise VectorStoreError(f"Similarity search failed: {str(e)}")
    
    async def delete_document(self, tenant_id: str, document_id: str) -> bool:
        """Delete all chunks for a document"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Delete points with matching document_id
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id)
                            ),
                            FieldCondition(
                                key="tenant_id",
                                match=MatchValue(value=tenant_id)
                            )
                        ]
                    )
                )
            )
            
            logger.info(f"Deleted document {document_id} from collection {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            raise VectorStoreError(f"Document deletion failed: {str(e)}")
    
    async def get_collection_info(self, tenant_id: str) -> Dict[str, Any]:
        """Get information about tenant's collection"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Get collection info
            collection_info = self.client.get_collection(collection_name)
            
            return {
                "name": collection_name,
                "vectors_count": collection_info.vectors_count,
                "indexed_vectors_count": collection_info.indexed_vectors_count,
                "points_count": collection_info.points_count,
                "segments_count": collection_info.segments_count,
                "status": collection_info.status,
                "optimizer_status": collection_info.optimizer_status,
                "disk_data_size": collection_info.disk_data_size,
                "ram_data_size": collection_info.ram_data_size,
            }
            
        except Exception as e:
            logger.error(f"Failed to get collection info for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection info retrieval failed: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Qdrant health"""
        try:
            # Get cluster info
            cluster_info = self.client.get_cluster_info()
            
            # Get collections
            collections = self.client.get_collections()
            
            return {
                "status": "healthy",
                "cluster_status": cluster_info.status,
                "peer_count": len(cluster_info.peers),
                "collections_count": len(collections.collections),
                "url": self.url
            }
            
        except Exception as e:
            logger.error(f"Qdrant health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "url": self.url
            }
    
    async def get_document_chunks(
        self, 
        tenant_id: str, 
        document_id: str
    ) -> List[Dict[str, Any]]:
        """Get all chunks for a specific document"""
        try:
            collection_name = self._get_collection_name(tenant_id)
            
            # Search for all chunks of the document
            search_result = self.client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        ),
                        FieldCondition(
                            key="tenant_id",
                            match=MatchValue(value=tenant_id)
                        )
                    ]
                ),
                with_payload=True,
                with_vectors=False
            )
            
            chunks = []
            for point in search_result[0]:  # scroll returns (points, next_page_offset)
                chunk = {
                    "id": point.id,
                    "document_id": point.payload.get("document_id"),
                    "chunk_id": point.payload.get("chunk_id"),
                    "chunk_index": point.payload.get("chunk_index"),
                    "content": point.payload.get("content"),
                    "metadata": {
                        k: v for k, v in point.payload.items() 
                        if k not in ["document_id", "chunk_id", "chunk_index", "content", "tenant_id"]
                    }
                }
                chunks.append(chunk)
            
            # Sort by chunk index
            chunks.sort(key=lambda x: x.get("chunk_index", 0))
            
            logger.info(f"Retrieved {len(chunks)} chunks for document {document_id}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to get document chunks: {e}")
            raise VectorStoreError(f"Document chunks retrieval failed: {str(e)}")


    async def get_tenant_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get comprehensive statistics for tenant's collection"""
        try:
            collection_name = self._get_collection_name(tenant_id)

            # Get collection info
            collection_info = await self.get_collection_info(tenant_id)

            # Get document count
            doc_count_result = self.client.count(
                collection_name=collection_name,
                count_filter=Filter(
                    must=[
                        FieldCondition(
                            key="tenant_id",
                            match=MatchValue(value=tenant_id)
                        )
                    ]
                ),
                exact=True
            )

            # Get unique document count
            scroll_result = self.client.scroll(
                collection_name=collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="tenant_id",
                            match=MatchValue(value=tenant_id)
                        )
                    ]
                ),
                with_payload=["document_id"],
                with_vectors=False,
                limit=10000  # Adjust based on expected document count
            )

            unique_documents = set()
            for point in scroll_result[0]:
                unique_documents.add(point.payload.get("document_id"))

            return {
                **collection_info,
                "total_chunks": doc_count_result.count,
                "unique_documents": len(unique_documents),
                "avg_chunks_per_document": round(doc_count_result.count / len(unique_documents), 2) if unique_documents else 0
            }

        except Exception as e:
            logger.error(f"Failed to get tenant stats for {tenant_id}: {e}")
            raise VectorStoreError(f"Tenant stats retrieval failed: {str(e)}")

    async def backup_collection(self, tenant_id: str, backup_path: str) -> bool:
        """Create backup snapshot of tenant's collection"""
        try:
            collection_name = self._get_collection_name(tenant_id)

            # Create snapshot
            snapshot_result = self.client.create_snapshot(collection_name)

            logger.info(f"Created backup snapshot for collection {collection_name}: {snapshot_result.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to backup collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection backup failed: {str(e)}")

    async def optimize_collection(self, tenant_id: str) -> bool:
        """Optimize tenant's collection for better performance"""
        try:
            collection_name = self._get_collection_name(tenant_id)

            # Update collection configuration for optimization
            self.client.update_collection(
                collection_name=collection_name,
                optimizer_config=models.OptimizersConfigDiff(
                    indexing_threshold=20000,
                    memmap_threshold=1000000,
                    max_segment_size=None,
                    max_optimization_threads=None,
                    deleted_threshold=0.2,
                    vacuum_min_vector_number=1000,
                    default_segment_number=0
                )
            )

            logger.info(f"Optimized collection {collection_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to optimize collection for tenant {tenant_id}: {e}")
            raise VectorStoreError(f"Collection optimization failed: {str(e)}")

    async def search_with_filters(
        self,
        tenant_id: str,
        query_embedding: List[float],
        filters: Dict[str, Any],
        limit: int = 5,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """Advanced search with custom filters"""
        try:
            collection_name = self._get_collection_name(tenant_id)

            # Build filter conditions
            filter_conditions = [
                FieldCondition(
                    key="tenant_id",
                    match=MatchValue(value=tenant_id)
                )
            ]

            # Add custom filters
            for key, value in filters.items():
                if isinstance(value, list):
                    filter_conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )
                else:
                    filter_conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )

            search_filter = Filter(must=filter_conditions)

            # Perform search
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold,
                with_payload=True,
                with_vectors=False
            )

            # Format results
            results = []
            for hit in search_result:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "document_id": hit.payload.get("document_id"),
                    "chunk_id": hit.payload.get("chunk_id"),
                    "chunk_index": hit.payload.get("chunk_index"),
                    "content": hit.payload.get("content"),
                    "metadata": {
                        k: v for k, v in hit.payload.items()
                        if k not in ["document_id", "chunk_id", "chunk_index", "content", "tenant_id"]
                    }
                }
                results.append(result)

            logger.info(f"Found {len(results)} filtered results for tenant {tenant_id}")
            return results

        except Exception as e:
            logger.error(f"Failed to search with filters: {e}")
            raise VectorStoreError(f"Filtered search failed: {str(e)}")


class QdrantManager:
    """High-level Qdrant management service"""

    def __init__(self):
        self.store = QdrantVectorStore()
        self._tenant_collections = {}  # Cache for tenant collections

    async def ensure_tenant_collection(
        self,
        tenant_id: str,
        vector_dimension: int = 1536
    ) -> bool:
        """Ensure tenant has a collection, create if not exists"""
        try:
            if tenant_id in self._tenant_collections:
                return True

            # Check if collection exists
            collection_name = self.store._get_collection_name(tenant_id)
            collections = self.store.client.get_collections()
            existing_names = [col.name for col in collections.collections]

            if collection_name not in existing_names:
                await self.store.create_collection(tenant_id, vector_dimension)

            self._tenant_collections[tenant_id] = True
            return True

        except Exception as e:
            logger.error(f"Failed to ensure collection for tenant {tenant_id}: {e}")
            return False

    async def cleanup_tenant_data(self, tenant_id: str) -> bool:
        """Clean up all data for a tenant"""
        try:
            # Delete collection
            await self.store.delete_collection(tenant_id)

            # Remove from cache
            self._tenant_collections.pop(tenant_id, None)

            logger.info(f"Cleaned up all data for tenant {tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to cleanup tenant data for {tenant_id}: {e}")
            return False

    async def migrate_tenant_data(
        self,
        old_tenant_id: str,
        new_tenant_id: str
    ) -> bool:
        """Migrate data from one tenant to another"""
        try:
            # Get all data from old collection
            old_collection = self.store._get_collection_name(old_tenant_id)

            # Scroll through all points
            scroll_result = self.store.client.scroll(
                collection_name=old_collection,
                with_payload=True,
                with_vectors=True,
                limit=1000
            )

            # Ensure new collection exists
            await self.ensure_tenant_collection(new_tenant_id)

            # Migrate points
            new_collection = self.store._get_collection_name(new_tenant_id)
            points_to_migrate = []

            for point in scroll_result[0]:
                # Update tenant_id in payload
                new_payload = point.payload.copy()
                new_payload["tenant_id"] = new_tenant_id

                # Create new point
                new_point = PointStruct(
                    id=point.id,
                    vector=point.vector,
                    payload=new_payload
                )
                points_to_migrate.append(new_point)

            # Insert migrated points
            if points_to_migrate:
                self.store.client.upsert(
                    collection_name=new_collection,
                    points=points_to_migrate
                )

            logger.info(f"Migrated {len(points_to_migrate)} points from {old_tenant_id} to {new_tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to migrate tenant data: {e}")
            return False


# Global instances
qdrant_store = QdrantVectorStore()
qdrant_manager = QdrantManager()


# Factory functions
def create_qdrant_store() -> QdrantVectorStore:
    """Create Qdrant vector store instance"""
    return QdrantVectorStore()


def get_qdrant_store() -> QdrantVectorStore:
    """Get global Qdrant store instance"""
    return qdrant_store


def get_qdrant_manager() -> QdrantManager:
    """Get global Qdrant manager instance"""
    return qdrant_manager
