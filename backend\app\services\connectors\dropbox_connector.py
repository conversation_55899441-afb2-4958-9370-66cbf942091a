"""
Dropbox connector for syncing files from Dropbox
"""

import asyncio
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
import aiohttp

from .base import BaseConnector, ConnectorDocument, SyncR<PERSON>ult, ConnectorStatus, ConnectorFactory
from .base import is_supported_file_type, extract_file_metadata


class DropboxConnector(BaseConnector):
    """Connector for Dropbox integration"""
    
    DISPLAY_NAME = "Dropbox"
    DESCRIPTION = "Sync documents from Dropbox folders"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.access_token = config.get("access_token")
        self.folder_path = config.get("folder_path", "")
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def connect(self) -> bool:
        """Establish connection to Dropbox"""
        try:
            self.set_status(ConnectorStatus.CONNECTING)
            
            if not await self.validate_config():
                self.set_status(ConnectorStatus.ERROR, "Invalid configuration")
                return False
            
            self.session = aiohttp.ClientSession(
                headers={
                    "Authorization": f"Bearer {self.access_token}",
                    "Content-Type": "application/json"
                }
            )
            
            # Test connection
            if await self.test_connection():
                self.set_status(ConnectorStatus.CONNECTED)
                return True
            else:
                self.set_status(ConnectorStatus.ERROR, "Connection test failed")
                return False
                
        except Exception as e:
            self.set_status(ConnectorStatus.ERROR, str(e))
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from Dropbox"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            self.set_status(ConnectorStatus.DISCONNECTED)
            return True
            
        except Exception as e:
            self.logger.error(f"Error disconnecting: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Dropbox connection"""
        try:
            if not self.session:
                return False
            
            async with self.session.post(
                "https://api.dropboxapi.com/2/users/get_current_account"
            ) as response:
                return response.status == 200
                
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
    
    async def list_documents(
        self, 
        path: str = None,
        recursive: bool = True,
        modified_since: Optional[datetime] = None
    ) -> AsyncGenerator[ConnectorDocument, None]:
        """List documents from Dropbox"""
        try:
            if not self.session:
                await self.connect()
            
            folder_path = path or self.folder_path
            
            # List folder contents
            payload = {
                "path": folder_path,
                "recursive": recursive,
                "include_media_info": False,
                "include_deleted": False,
                "include_has_explicit_shared_members": False
            }
            
            async with self.session.post(
                "https://api.dropboxapi.com/2/files/list_folder",
                json=payload
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Dropbox API error: {error_text}")
                
                data = await response.json()
                
                for entry in data.get("entries", []):
                    if entry.get(".tag") == "file":
                        # Check if file type is supported
                        if not is_supported_file_type(entry["name"]):
                            continue
                        
                        # Check modification date filter
                        modified_at = datetime.fromisoformat(
                            entry["client_modified"].replace("Z", "+00:00")
                        )
                        
                        if modified_since and modified_at <= modified_since:
                            continue
                        
                        # Create document object
                        document = ConnectorDocument(
                            id=entry["id"],
                            title=entry["name"],
                            content="",  # Content loaded separately
                            file_type=entry["name"].split(".")[-1].lower(),
                            file_size=entry["size"],
                            modified_at=modified_at,
                            created_at=modified_at,  # Dropbox doesn't provide creation date
                            metadata={
                                "dropbox_path": entry["path_lower"],
                                "dropbox_id": entry["id"],
                                "content_hash": entry.get("content_hash")
                            },
                            source_path=entry["path_display"]
                        )
                        
                        yield document
                
                # Handle pagination
                while data.get("has_more", False):
                    cursor_payload = {"cursor": data["cursor"]}
                    
                    async with self.session.post(
                        "https://api.dropboxapi.com/2/files/list_folder/continue",
                        json=cursor_payload
                    ) as response:
                        
                        if response.status != 200:
                            break
                        
                        data = await response.json()
                        
                        for entry in data.get("entries", []):
                            if entry.get(".tag") == "file":
                                if not is_supported_file_type(entry["name"]):
                                    continue
                                
                                modified_at = datetime.fromisoformat(
                                    entry["client_modified"].replace("Z", "+00:00")
                                )
                                
                                if modified_since and modified_at <= modified_since:
                                    continue
                                
                                document = ConnectorDocument(
                                    id=entry["id"],
                                    title=entry["name"],
                                    content="",
                                    file_type=entry["name"].split(".")[-1].lower(),
                                    file_size=entry["size"],
                                    modified_at=modified_at,
                                    created_at=modified_at,
                                    metadata={
                                        "dropbox_path": entry["path_lower"],
                                        "dropbox_id": entry["id"],
                                        "content_hash": entry.get("content_hash")
                                    },
                                    source_path=entry["path_display"]
                                )
                                
                                yield document
                                
        except Exception as e:
            self.logger.error(f"Error listing documents: {e}")
            raise
    
    async def get_document_content(self, document_id: str) -> bytes:
        """Get document content from Dropbox"""
        try:
            if not self.session:
                await self.connect()
            
            # Get file metadata first to get the path
            metadata_payload = {"file": document_id}
            
            async with self.session.post(
                "https://api.dropboxapi.com/2/files/get_metadata",
                json=metadata_payload
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to get file metadata: {error_text}")
                
                metadata = await response.json()
                file_path = metadata["path_lower"]
            
            # Download file content
            download_payload = {"path": file_path}
            
            async with self.session.post(
                "https://content.dropboxapi.com/2/files/download",
                headers={
                    "Authorization": f"Bearer {self.access_token}",
                    "Dropbox-API-Arg": str(download_payload).replace("'", '"')
                }
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Failed to download file: {error_text}")
                
                return await response.read()
                
        except Exception as e:
            self.logger.error(f"Error getting document content: {e}")
            raise
    
    async def sync_incremental(
        self, 
        last_sync_token: Optional[str] = None
    ) -> SyncResult:
        """Perform incremental sync"""
        start_time = datetime.utcnow()
        documents_processed = 0
        documents_added = 0
        documents_updated = 0
        documents_failed = 0
        errors = []
        
        try:
            self.set_status(ConnectorStatus.SYNCING)
            
            # For Dropbox, we'll use modification time for incremental sync
            # In a real implementation, you'd store the last sync time
            last_sync_time = None
            if last_sync_token:
                try:
                    last_sync_time = datetime.fromisoformat(last_sync_token)
                except:
                    pass
            
            async for document in self.list_documents(modified_since=last_sync_time):
                try:
                    documents_processed += 1
                    
                    # Get document content
                    content = await self.get_document_content(document.id)
                    document.content = content.decode('utf-8', errors='ignore')
                    
                    # Add metadata
                    file_metadata = extract_file_metadata(document.source_path, content)
                    document.metadata.update(file_metadata)
                    document.checksum = file_metadata["checksum"]
                    
                    # Here you would save the document to your system
                    # For now, we'll just count it as added
                    documents_added += 1
                    
                except Exception as e:
                    documents_failed += 1
                    errors.append(f"Failed to process {document.title}: {str(e)}")
                    self.logger.error(f"Failed to process document {document.id}: {e}")
            
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            self.set_status(ConnectorStatus.CONNECTED)
            
            return SyncResult(
                success=documents_failed == 0,
                documents_processed=documents_processed,
                documents_added=documents_added,
                documents_updated=documents_updated,
                documents_failed=documents_failed,
                errors=errors,
                duration_seconds=duration,
                last_sync_token=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            self.set_status(ConnectorStatus.ERROR, str(e))
            duration = (datetime.utcnow() - start_time).total_seconds()
            
            return SyncResult(
                success=False,
                documents_processed=documents_processed,
                documents_added=documents_added,
                documents_updated=documents_updated,
                documents_failed=documents_failed + 1,
                errors=errors + [str(e)],
                duration_seconds=duration
            )
    
    async def sync_full(self) -> SyncResult:
        """Perform full sync"""
        return await self.sync_incremental(last_sync_token=None)
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields"""
        return ["access_token"]
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Get configuration schema"""
        return {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string",
                    "title": "Access Token",
                    "description": "Dropbox API access token",
                    "format": "password"
                },
                "folder_path": {
                    "type": "string",
                    "title": "Folder Path",
                    "description": "Dropbox folder path to sync (leave empty for root)",
                    "default": ""
                }
            },
            "required": ["access_token"]
        }


# Register the connector
ConnectorFactory.register("dropbox", DropboxConnector)
