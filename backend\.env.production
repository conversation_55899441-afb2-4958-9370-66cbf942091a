# Production Environment Configuration
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# Database
DATABASE_URL=postgresql+asyncpg://postgres:${DATABASE_PASSWORD}@postgres-service:5432/aithentiqmind

# Redis
REDIS_URL=redis://:${REDIS_PASSWORD}@redis-service:6379/0

# Security
SECRET_KEY=${SECRET_KEY}
JWT_SECRET_KEY=${SECRET_KEY}

# CORS
CORS_ORIGINS=["https://app.yourdomain.com", "https://yourdomain.com"]
ALLOWED_HOSTS=["api.yourdomain.com", "yourdomain.com"]

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=AIthentiqMind
VERSION=1.0.0

# Performance
WORKERS=4
MAX_CONNECTIONS=100
POOL_SIZE=20
MAX_OVERFLOW=30

# External Services
OPENAI_API_KEY=${OPENAI_API_KEY}
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# Auth0
AUTH0_DOMAIN=${AUTH0_DOMAIN}
AUTH0_CLIENT_ID=${AUTH0_CLIENT_ID}
AUTH0_CLIENT_SECRET=${AUTH0_CLIENT_SECRET}
AUTH0_AUDIENCE=${AUTH0_AUDIENCE}

# Vector Database
QDRANT_URL=http://qdrant-service:6333
QDRANT_API_KEY=${QDRANT_API_KEY}

# Object Storage
MINIO_ENDPOINT=minio-service:9000
MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
MINIO_BUCKET=aithentiqmind-documents

# RagFlow
RAGFLOW_BASE_URL=http://ragflow-service:9380
RAGFLOW_API_KEY=${RAGFLOW_API_KEY}

# n8n
N8N_BASE_URL=http://n8n-service:5678
N8N_API_KEY=${N8N_API_KEY}

# RAG Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
RETRIEVAL_TOP_K=5
MAX_CHUNKS_PER_DOCUMENT=1000
MAX_FILE_SIZE=52428800

# Trust Scoring
CONFIDENCE_WEIGHT=0.3
RELEVANCE_WEIGHT=0.3
RECENCY_WEIGHT=0.2
MIN_SOURCE_DIVERSITY=2

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# File Processing
ALLOWED_FILE_TYPES=pdf,docx,txt,md,html
