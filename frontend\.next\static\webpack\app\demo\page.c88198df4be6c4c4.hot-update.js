"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/page",{

/***/ "(app-pages-browser)/./src/app/demo/page.tsx":
/*!*******************************!*\
  !*** ./src/app/demo/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DemoPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_enterprise_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/enterprise-components */ \"(app-pages-browser)/./src/components/ui/enterprise-components.tsx\");\n/* harmony import */ var _components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/theme-provider */ \"(app-pages-browser)/./src/components/providers/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DemoPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('chat');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            type: 'user',\n            content: 'What is artificial intelligence?',\n            timestamp: '10:30 AM'\n        },\n        {\n            id: 2,\n            type: 'assistant',\n            content: 'Artificial Intelligence (AI) is a broad field of computer science focused on creating systems that can perform tasks typically requiring human intelligence, such as learning, reasoning, problem-solving, and understanding natural language.',\n            sources: [\n                {\n                    title: 'AI and Machine Learning Guide',\n                    relevance: 0.95\n                }\n            ],\n            trustScore: 0.92,\n            timestamp: '10:30 AM'\n        }\n    ]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim()) return;\n        // Add user message\n        const userMessage = {\n            id: messages.length + 1,\n            type: 'user',\n            content: newMessage,\n            timestamp: new Date().toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n            })\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        // Simulate API call\n        try {\n            const response = await fetch('http://localhost:8000/api/v1/queries/ask', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    question: newMessage,\n                    include_sources: true,\n                    top_k: 5\n                })\n            });\n            const data = await response.json();\n            // Add assistant response\n            const assistantMessage = {\n                id: messages.length + 2,\n                type: 'assistant',\n                content: data.answer,\n                sources: data.sources,\n                trustScore: data.trust_score,\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                })\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error('Error:', error);\n            // Add error message\n            const errorMessage = {\n                id: messages.length + 2,\n                type: 'assistant',\n                content: 'Sorry, I encountered an error processing your request. Please try again.',\n                timestamp: new Date().toLocaleTimeString([], {\n                    hour: '2-digit',\n                    minute: '2-digit'\n                })\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        }\n        setNewMessage('');\n    };\n    const mockDocuments = [\n        {\n            id: 1,\n            title: 'AI and Machine Learning Guide',\n            status: 'Processed',\n            chunks: 15\n        },\n        {\n            id: 2,\n            title: 'Enterprise RAG Implementation',\n            status: 'Processed',\n            chunks: 22\n        },\n        {\n            id: 3,\n            title: 'Data Science Handbook',\n            status: 'Processing',\n            chunks: 0\n        }\n    ];\n    const mockAnalytics = {\n        totalQueries: 1247,\n        avgTrustScore: 0.85,\n        totalDocuments: 23,\n        activeUsers: 12\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enterprise_components__WEBPACK_IMPORTED_MODULE_3__.EnterpriseNav, {\n                brand: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-sm\",\n                                        children: \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-gradient\",\n                                    children: \"AIthentiqMind\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.StatusIndicator, {\n                            status: \"online\",\n                            label: \"Enterprise Demo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, void 0),\n                links: [\n                    {\n                        href: '/',\n                        label: 'Home'\n                    },\n                    {\n                        href: '/demo',\n                        label: 'Live Demo',\n                        active: true\n                    },\n                    {\n                        href: '/dashboard',\n                        label: 'Dashboard'\n                    }\n                ],\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4 text-xs text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.StatusIndicator, {\n                                    status: \"online\",\n                                    label: \"Backend\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.StatusIndicator, {\n                                    status: \"online\",\n                                    label: \"RAG Engine\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_enterprise_components__WEBPACK_IMPORTED_MODULE_3__.EnterpriseBadge, {\n                                    variant: \"success\",\n                                    size: \"sm\",\n                                    children: \"94.2% Trust\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_theme_provider__WEBPACK_IMPORTED_MODULE_4__.ThemeToggle, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-muted-foreground text-sm\",\n                                    children: \"Enterprise User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white text-sm font-medium\",\n                                        children: \"EU\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-72 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"Enterprise Console\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Advanced RAG capabilities at your fingertips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('chat'),\n                                                className: \"w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'chat' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-lg\",\n                                                        children: \"\\uD83D\\uDCAC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Intelligent Chat\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs opacity-75\",\n                                                                children: \"RAG-powered Q&A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('documents'),\n                                                className: \"w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'documents' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-lg\",\n                                                        children: \"\\uD83D\\uDCC4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Document Library\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs opacity-75\",\n                                                                children: \"Manage knowledge base\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('analytics'),\n                                                className: \"w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'analytics' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-lg\",\n                                                        children: \"\\uD83D\\uDCCA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Analytics Dashboard\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs opacity-75\",\n                                                                children: \"Performance insights\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab('upload'),\n                                                className: \"w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(activeTab === 'upload' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg' : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-lg\",\n                                                        children: \"⬆️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: \"Document Upload\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs opacity-75\",\n                                                                children: \"Add new content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-gray-900 mb-3\",\n                                                children: \"System Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-xs\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Documents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: \"2,847\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Queries Today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: \"156\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Trust Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-green-600\",\n                                                                children: \"94.2%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600\",\n                                                                children: \"Response Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-blue-600\",\n                                                                children: \"1.2s\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                activeTab === 'chat' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200/50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-semibold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                                children: \"Enterprise AI Assistant\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 mt-1\",\n                                                                children: \"Advanced RAG-powered Q&A with enterprise-grade trust scoring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"AI Online\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"Vector DB Ready\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-96 overflow-y-auto p-6 space-y-4\",\n                                            children: messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.type === 'user' ? 'justify-end' : 'justify-start'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"max-w-xs lg:max-w-md px-4 py-2 rounded-lg \".concat(message.type === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: message.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            message.sources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 pt-2 border-t border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: \"Sources:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    message.sources.map((source, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-blue-600\",\n                                                                            children: [\n                                                                                source.title,\n                                                                                \" (Relevance: \",\n                                                                                (source.relevance * 100).toFixed(0),\n                                                                                \"%)\"\n                                                                            ]\n                                                                        }, idx, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 31\n                                                                        }, this)),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-green-600 mt-1\",\n                                                                        children: [\n                                                                            \"Trust Score: \",\n                                                                            (message.trustScore * 100).toFixed(0),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 opacity-70\",\n                                                                children: message.timestamp\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, message.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-t\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: newMessage,\n                                                        onChange: (e)=>setNewMessage(e.target.value),\n                                                        onKeyPress: (e)=>e.key === 'Enter' && handleSendMessage(),\n                                                        placeholder: \"Ask a question...\",\n                                                        className: \"flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSendMessage,\n                                                        className: \"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                        children: \"Send\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'documents' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Manage your knowledge base\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: mockDocuments.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: doc.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            \"Status: \",\n                                                                            doc.status,\n                                                                            \" • Chunks: \",\n                                                                            doc.chunks\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                                                                        children: \"View\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        className: \"text-red-600 hover:text-red-800 text-sm\",\n                                                                        children: \"Delete\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, doc.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'analytics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Analytics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Platform insights and metrics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 lg:grid-cols-4 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-blue-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-blue-600\",\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Total Queries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 358,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.totalQueries\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-green-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-green-600\",\n                                                                    children: \"\\uD83D\\uDCCA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Avg Trust Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: [\n                                                                                (mockAnalytics.avgTrustScore * 100).toFixed(0),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 366,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-purple-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-purple-600\",\n                                                                    children: \"\\uD83D\\uDCC4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 374,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Documents\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.totalDocuments\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-orange-50 p-4 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-3xl text-orange-600\",\n                                                                    children: \"\\uD83D\\uDC65\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ml-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-600\",\n                                                                            children: \"Active Users\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                                            children: mockAnalytics.activeUsers\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === 'upload' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-medium text-gray-900\",\n                                                    children: \"Upload Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"Add new documents to your knowledge base\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-gray-300 rounded-lg p-12 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-6xl text-gray-400\",\n                                                        children: \"⬆️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"mt-4 text-lg font-medium text-gray-900\",\n                                                        children: \"Upload Documents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm text-gray-500\",\n                                                        children: \"Drag and drop files here, or click to select files\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-xs text-gray-400\",\n                                                        children: \"Supports PDF, DOCX, TXT, MD files up to 50MB\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"mt-4 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600\",\n                                                        children: \"Select Files\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\app\\\\demo\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(DemoPage, \"tdRThtiAlJB6brpGPKjdSz4lASQ=\");\n_c = DemoPage;\nvar _c;\n$RefreshReg$(_c, \"DemoPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/demo/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/theme-provider.tsx":
/*!*****************************************************!*\
  !*** ./src/components/providers/theme-provider.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   MetricCard: () => (/* binding */ MetricCard),\n/* harmony export */   StatusIndicator: () => (/* binding */ StatusIndicator),\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme,ThemeToggle,StatusIndicator,LoadingSpinner,MetricCard auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nconst initialState = {\n    theme: 'system',\n    setTheme: ()=>null,\n    systemTheme: 'light',\n    resolvedTheme: 'light'\n};\nconst ThemeProviderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(initialState);\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = 'system', storageKey = 'aithentiqmind-theme', attribute = 'class', enableSystem = true, disableTransitionOnChange = false, ...props } = param;\n    _s();\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ThemeProvider.useState\": ()=>{\n            if (true) {\n                return localStorage.getItem(storageKey) || defaultTheme;\n            }\n            return defaultTheme;\n        }\n    }[\"ThemeProvider.useState\"]);\n    const [systemTheme, setSystemTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Get system theme\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            setSystemTheme(mediaQuery.matches ? 'dark' : 'light');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": (e)=>{\n                    setSystemTheme(e.matches ? 'dark' : 'light');\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    const resolvedTheme = theme === 'system' ? systemTheme : theme;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const root = window.document.documentElement;\n            root.classList.remove('light', 'dark');\n            if (theme === 'system') {\n                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.add(theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            localStorage.setItem(storageKey, theme);\n            setTheme(theme);\n        },\n        systemTheme,\n        resolvedTheme\n    };\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeProvider, \"+5hYRkLCl8rK8xDYLr4A3HUKIxw=\");\n_c = ThemeProvider;\nconst useTheme = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeProviderContext);\n    if (context === undefined) throw new Error('useTheme must be used within a ThemeProvider');\n    return context;\n};\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Theme Toggle Component\nfunction ThemeToggle() {\n    _s2();\n    const { theme, setTheme, resolvedTheme } = useTheme();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setTheme(resolvedTheme === 'dark' ? 'light' : 'dark'),\n        className: \"relative inline-flex h-10 w-10 items-center justify-center rounded-lg border border-border bg-background text-foreground transition-colors hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n        \"aria-label\": \"Toggle theme\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"h-5 w-5 transition-all \".concat(resolvedTheme === 'dark' ? 'rotate-0 scale-100' : 'rotate-90 scale-0'),\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"absolute h-5 w-5 transition-all \".concat(resolvedTheme === 'dark' ? 'rotate-90 scale-0' : 'rotate-0 scale-100'),\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_s2(ThemeToggle, \"5puWP7kDS4+x9mvfqm7YZ5g0Rcw=\", false, function() {\n    return [\n        useTheme\n    ];\n});\n_c1 = ThemeToggle;\n// Enterprise Status Indicator Component\nfunction StatusIndicator(param) {\n    let { status, label, size = 'sm' } = param;\n    const sizeClasses = {\n        sm: 'w-2 h-2',\n        md: 'w-3 h-3',\n        lg: 'w-4 h-4'\n    };\n    const statusClasses = {\n        online: 'bg-green-500 animate-pulse',\n        offline: 'bg-gray-500',\n        warning: 'bg-yellow-500 animate-pulse',\n        error: 'bg-red-500 animate-pulse'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(sizeClasses[size], \" \").concat(statusClasses[status], \" rounded-full\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm text-muted-foreground font-medium\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                lineNumber: 181,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_c2 = StatusIndicator;\n// Enterprise Loading Spinner Component\nfunction LoadingSpinner(param) {\n    let { size = 'md', className = '' } = param;\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-6 h-6',\n        lg: 'w-8 h-8'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(sizeClasses[size], \" border-2 border-primary border-t-transparent rounded-full animate-spin \").concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_c3 = LoadingSpinner;\n// Enterprise Metric Card Component\nfunction MetricCard(param) {\n    let { title, value, change, changeType = 'positive', icon, className = '' } = param;\n    const changeClasses = {\n        positive: 'text-green-600',\n        negative: 'text-red-600',\n        neutral: 'text-gray-600'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"metric-card \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"metric-label\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"metric-value\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this),\n                        change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"metric-change \".concat(changeClasses[changeType]),\n                            children: [\n                                changeType === 'positive' ? '↗' : changeType === 'negative' ? '↘' : '→',\n                                \" \",\n                                change\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this),\n                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\providers\\\\theme-provider.tsx\",\n        lineNumber: 229,\n        columnNumber: 5\n    }, this);\n}\n_c4 = MetricCard;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"ThemeProvider\");\n$RefreshReg$(_c1, \"ThemeToggle\");\n$RefreshReg$(_c2, \"StatusIndicator\");\n$RefreshReg$(_c3, \"LoadingSpinner\");\n$RefreshReg$(_c4, \"MetricCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/enterprise-components.tsx":
/*!*****************************************************!*\
  !*** ./src/components/ui/enterprise-components.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnterpriseAlert: () => (/* binding */ EnterpriseAlert),\n/* harmony export */   EnterpriseBadge: () => (/* binding */ EnterpriseBadge),\n/* harmony export */   EnterpriseButton: () => (/* binding */ EnterpriseButton),\n/* harmony export */   EnterpriseCard: () => (/* binding */ EnterpriseCard),\n/* harmony export */   EnterpriseInput: () => (/* binding */ EnterpriseInput),\n/* harmony export */   EnterpriseNav: () => (/* binding */ EnterpriseNav),\n/* harmony export */   EnterpriseProgress: () => (/* binding */ EnterpriseProgress),\n/* harmony export */   EnterpriseSidebar: () => (/* binding */ EnterpriseSidebar),\n/* harmony export */   EnterpriseTooltip: () => (/* binding */ EnterpriseTooltip)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../providers/theme-provider */ \"(app-pages-browser)/./src/components/providers/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ EnterpriseButton,EnterpriseInput,EnterpriseCard,EnterpriseNav,EnterpriseSidebar,EnterpriseProgress,EnterpriseBadge,EnterpriseAlert,EnterpriseTooltip auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Enterprise Button Component\nfunction EnterpriseButton(param) {\n    let { children, variant = 'primary', size = 'md', loading = false, disabled = false, onClick, className = '', ...props } = param;\n    const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\n    const variantClasses = {\n        primary: 'btn-primary focus:ring-blue-500',\n        secondary: 'btn-secondary focus:ring-gray-500',\n        ghost: 'btn-ghost focus:ring-gray-500',\n        outline: 'border border-border bg-transparent hover:bg-muted focus:ring-blue-500'\n    };\n    const sizeClasses = {\n        sm: 'px-4 py-2 text-sm rounded-md',\n        md: 'px-6 py-3 text-base rounded-lg',\n        lg: 'px-8 py-4 text-lg rounded-xl'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"\".concat(baseClasses, \" \").concat(variantClasses[variant], \" \").concat(sizeClasses[size], \" \").concat(className),\n        disabled: disabled || loading,\n        onClick: onClick,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers_theme_provider__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                size: \"sm\",\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 48,\n                columnNumber: 19\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_c = EnterpriseButton;\n// Enterprise Input Component\nfunction EnterpriseInput(param) {\n    let { label, error, helper, icon, className = '', ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 \".concat(className),\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                className: \"block text-sm font-medium text-foreground\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                        children: icon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        className: \"input-enterprise w-full \".concat(icon ? 'pl-10' : '', \" \").concat(error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''),\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this),\n            helper && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-muted-foreground\",\n                children: helper\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c1 = EnterpriseInput;\n// Enterprise Card Component\nfunction EnterpriseCard(param) {\n    let { children, title, subtitle, action, hover = false, className = '', ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(hover ? 'card-enterprise-hover' : 'card-enterprise', \" \").concat(className),\n        ...props,\n        children: [\n            (title || subtitle || action) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-6 border-b border-border/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 23\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground mt-1\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 26\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: action\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 22\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: title || subtitle || action ? 'p-6' : '',\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_c2 = EnterpriseCard;\n// Enterprise Navigation Component\nfunction EnterpriseNav(param) {\n    let { brand, links, actions, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"nav-enterprise \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-8\",\n                        children: [\n                            brand,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex space-x-6\",\n                                children: links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: link.href,\n                                        className: link.active ? 'nav-link-active' : 'nav-link',\n                                        children: link.label\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    actions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: actions\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 23\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_c3 = EnterpriseNav;\n// Enterprise Sidebar Component\nfunction EnterpriseSidebar(param) {\n    let { children, title, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sidebar-enterprise \".concat(className),\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 border-b border-border/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-lg font-semibold text-foreground\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_c4 = EnterpriseSidebar;\n// Enterprise Progress Bar Component\nfunction EnterpriseProgress(param) {\n    let { value, max = 100, label, showValue = true, color = 'blue', className = '' } = param;\n    const percentage = Math.min(value / max * 100, 100);\n    const colorClasses = {\n        blue: 'bg-gradient-to-r from-blue-500 to-blue-600',\n        green: 'bg-gradient-to-r from-green-500 to-green-600',\n        purple: 'bg-gradient-to-r from-purple-500 to-purple-600',\n        orange: 'bg-gradient-to-r from-orange-500 to-orange-600',\n        red: 'bg-gradient-to-r from-red-500 to-red-600'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2 \".concat(className),\n        children: [\n            (label || showValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between text-sm\",\n                children: [\n                    label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-muted-foreground\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 21\n                    }, this),\n                    showValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: [\n                            value,\n                            max === 100 ? '%' : \"/\".concat(max)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 224,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-muted rounded-full h-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-2 rounded-full transition-all duration-500 \".concat(colorClasses[color]),\n                    style: {\n                        width: \"\".concat(percentage, \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_c5 = EnterpriseProgress;\n// Enterprise Badge Component\nfunction EnterpriseBadge(param) {\n    let { children, variant = 'default', size = 'md', className = '' } = param;\n    const baseClasses = 'inline-flex items-center font-medium rounded-full';\n    const variantClasses = {\n        default: 'bg-muted text-muted-foreground',\n        success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n        error: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n        info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    };\n    const sizeClasses = {\n        sm: 'px-2 py-1 text-xs',\n        md: 'px-3 py-1 text-sm',\n        lg: 'px-4 py-2 text-base'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: \"\".concat(baseClasses, \" \").concat(variantClasses[variant], \" \").concat(sizeClasses[size], \" \").concat(className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\n_c6 = EnterpriseBadge;\n// Enterprise Alert Component\nfunction EnterpriseAlert(param) {\n    let { children, title, variant = 'info', dismissible = false, onDismiss, className = '' } = param;\n    _s();\n    const [dismissed, setDismissed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleDismiss = ()=>{\n        setDismissed(true);\n        onDismiss === null || onDismiss === void 0 ? void 0 : onDismiss();\n    };\n    if (dismissed) return null;\n    const variantClasses = {\n        info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200',\n        success: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200',\n        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',\n        error: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border rounded-lg p-4 \".concat(variantClasses[variant], \" \").concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-medium mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this),\n                dismissible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleDismiss,\n                    className: \"ml-4 text-current hover:opacity-70 transition-opacity\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, this);\n}\n_s(EnterpriseAlert, \"QAOOD081Jhe76HGRMdQoGotM9i0=\");\n_c7 = EnterpriseAlert;\n// Enterprise Tooltip Component\nfunction EnterpriseTooltip(param) {\n    let { children, content, position = 'top', className = '' } = param;\n    _s1();\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const positionClasses = {\n        top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',\n        bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',\n        left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',\n        right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative inline-block \".concat(className),\n        onMouseEnter: ()=>setVisible(true),\n        onMouseLeave: ()=>setVisible(false),\n        children: [\n            children,\n            visible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg whitespace-nowrap \".concat(positionClasses[position]),\n                children: [\n                    content,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-2 h-2 bg-gray-900 transform rotate-45\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\AIthentiqMind\\\\frontend\\\\src\\\\components\\\\ui\\\\enterprise-components.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, this);\n}\n_s1(EnterpriseTooltip, \"OGsIWlGlwYpVUqIrDReJ1GWx7rw=\");\n_c8 = EnterpriseTooltip;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"EnterpriseButton\");\n$RefreshReg$(_c1, \"EnterpriseInput\");\n$RefreshReg$(_c2, \"EnterpriseCard\");\n$RefreshReg$(_c3, \"EnterpriseNav\");\n$RefreshReg$(_c4, \"EnterpriseSidebar\");\n$RefreshReg$(_c5, \"EnterpriseProgress\");\n$RefreshReg$(_c6, \"EnterpriseBadge\");\n$RefreshReg$(_c7, \"EnterpriseAlert\");\n$RefreshReg$(_c8, \"EnterpriseTooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/enterprise-components.tsx\n"));

/***/ })

});