"""
Workflow management API endpoints
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from app.core.auth import get_current_active_user, get_current_tenant, TenantContext, get_tenant_context
from app.core.logging import get_logger
from app.db.database import get_db
from app.models.user import User
from app.models.tenant import Tenant
from app.services.workflow.manager import get_workflow_manager
from app.services.workflow.templates import WorkflowTemplates
from app.services.audit import get_audit_service

logger = get_logger(__name__)
router = APIRouter()

# Pydantic models
class WorkflowCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    template_id: Optional[str] = None
    configuration: Dict[str, Any] = {}

class WorkflowUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class WorkflowExecuteRequest(BaseModel):
    input_data: Dict[str, Any] = {}

class WebhookTriggerRequest(BaseModel):
    webhook_path: str
    data: Dict[str, Any]
    method: str = "POST"


@router.get("/templates")
async def get_workflow_templates(
    current_user: User = Depends(get_current_active_user)
):
    """Get all available workflow templates"""
    try:
        templates = WorkflowTemplates.get_all_templates()
        
        return {
            "templates": templates,
            "count": len(templates)
        }
        
    except Exception as e:
        logger.error(f"Failed to get workflow templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve workflow templates"
        )


@router.get("/templates/{template_id}")
async def get_workflow_template(
    template_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Get specific workflow template"""
    try:
        template = WorkflowTemplates.get_template_by_id(template_id)
        
        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Template not found: {template_id}"
            )
        
        return template
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get workflow template {template_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve workflow template"
        )


@router.post("/")
async def create_workflow(
    request: WorkflowCreateRequest,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Create a new workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        if request.template_id:
            # Create from template
            workflow = await workflow_manager.create_workflow_from_template(
                tenant_id=str(tenant_context.tenant_id),
                template_id=request.template_id,
                name=request.name,
                configuration=request.configuration,
                db=db
            )
        else:
            # Create custom workflow
            if not request.configuration:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Configuration required for custom workflows"
                )
            
            workflow = await workflow_manager.create_custom_workflow(
                tenant_id=str(tenant_context.tenant_id),
                name=request.name,
                description=request.description or "",
                workflow_data=request.configuration,
                db=db
            )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.create",
            resource="workflow",
            resource_id=str(workflow.id),
            details={
                "workflow_name": workflow.name,
                "template_id": request.template_id,
                "n8n_workflow_id": workflow.n8n_workflow_id
            }
        )
        
        return workflow.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create workflow: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create workflow"
        )


@router.get("/")
async def list_workflows(
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """List all workflows for the current tenant"""
    try:
        workflow_manager = get_workflow_manager()
        
        workflows = await workflow_manager.get_workflows(
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        return {
            "workflows": [workflow.to_dict() for workflow in workflows],
            "count": len(workflows)
        }
        
    except Exception as e:
        logger.error(f"Failed to list workflows: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve workflows"
        )


@router.get("/{workflow_id}")
async def get_workflow(
    workflow_id: str,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get specific workflow"""
    try:
        workflow_manager = get_workflow_manager()
        
        workflow = await workflow_manager.get_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        return workflow.to_dict()
        
    except Exception as e:
        logger.error(f"Failed to get workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve workflow"
        )


@router.put("/{workflow_id}")
async def update_workflow(
    workflow_id: str,
    request: WorkflowUpdateRequest,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Update workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        # Prepare updates
        updates = {}
        if request.name is not None:
            updates["name"] = request.name
        if request.description is not None:
            updates["description"] = request.description
        if request.configuration is not None:
            updates["configuration"] = request.configuration
        if request.is_active is not None:
            updates["is_active"] = request.is_active
        
        workflow = await workflow_manager.update_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            updates=updates,
            db=db
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.update",
            resource="workflow",
            resource_id=workflow_id,
            details={
                "updates": updates,
                "workflow_name": workflow.name
            }
        )
        
        return workflow.to_dict()
        
    except Exception as e:
        logger.error(f"Failed to update workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update workflow"
        )


@router.delete("/{workflow_id}")
async def delete_workflow(
    workflow_id: str,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Delete workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        # Get workflow info before deletion
        workflow = await workflow_manager.get_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        await workflow_manager.delete_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.delete",
            resource="workflow",
            resource_id=workflow_id,
            details={
                "workflow_name": workflow.name,
                "n8n_workflow_id": workflow.n8n_workflow_id
            }
        )
        
        return {"message": "Workflow deleted successfully"}
        
    except Exception as e:
        logger.error(f"Failed to delete workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete workflow"
        )


@router.post("/{workflow_id}/activate")
async def activate_workflow(
    workflow_id: str,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Activate workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        workflow = await workflow_manager.activate_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.activate",
            resource="workflow",
            resource_id=workflow_id,
            details={"workflow_name": workflow.name}
        )
        
        return workflow.to_dict()
        
    except Exception as e:
        logger.error(f"Failed to activate workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate workflow"
        )


@router.post("/{workflow_id}/deactivate")
async def deactivate_workflow(
    workflow_id: str,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Deactivate workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        workflow = await workflow_manager.deactivate_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            db=db
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.deactivate",
            resource="workflow",
            resource_id=workflow_id,
            details={"workflow_name": workflow.name}
        )
        
        return workflow.to_dict()
        
    except Exception as e:
        logger.error(f"Failed to deactivate workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate workflow"
        )


@router.post("/{workflow_id}/execute")
async def execute_workflow(
    workflow_id: str,
    request: WorkflowExecuteRequest,
    background_tasks: BackgroundTasks,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Execute workflow manually"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        result = await workflow_manager.execute_workflow(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            input_data=request.input_data,
            db=db
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(tenant_context.tenant_id),
            user_id=str(tenant_context.user_id),
            action="workflow.execute",
            resource="workflow",
            resource_id=workflow_id,
            details={
                "execution_id": result.get("execution_id"),
                "input_data_keys": list(request.input_data.keys())
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to execute workflow {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to execute workflow"
        )


@router.get("/{workflow_id}/executions")
async def get_workflow_executions(
    workflow_id: str,
    limit: int = 20,
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get workflow execution history"""
    try:
        workflow_manager = get_workflow_manager()
        
        executions = await workflow_manager.get_workflow_executions(
            workflow_id=workflow_id,
            tenant_id=str(tenant_context.tenant_id),
            limit=limit,
            db=db
        )
        
        return {
            "executions": executions,
            "count": len(executions)
        }
        
    except Exception as e:
        logger.error(f"Failed to get workflow executions for {workflow_id}: {e}")
        if "not found" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Workflow not found: {workflow_id}"
            )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve workflow executions"
        )


@router.post("/webhook/trigger")
async def trigger_webhook(
    request: WebhookTriggerRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user)
):
    """Trigger a webhook workflow"""
    try:
        workflow_manager = get_workflow_manager()
        audit_service = get_audit_service()
        
        result = await workflow_manager.trigger_webhook(
            webhook_path=request.webhook_path,
            data=request.data,
            method=request.method
        )
        
        # Log audit event
        background_tasks.add_task(
            audit_service.log_action,
            tenant_id=str(current_user.tenant_id),
            user_id=str(current_user.id),
            action="webhook.trigger",
            resource="webhook",
            resource_id=request.webhook_path,
            details={
                "method": request.method,
                "data_keys": list(request.data.keys())
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to trigger webhook {request.webhook_path}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger webhook"
        )


@router.get("/health")
async def workflow_health_check():
    """Check workflow service health"""
    try:
        workflow_manager = get_workflow_manager()
        health = await workflow_manager.health_check()
        
        return health
        
    except Exception as e:
        logger.error(f"Workflow health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
