# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Redis for caching and queues
redis==5.0.1
celery==5.3.4

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
authlib==1.2.1

# Vector database
qdrant-client==1.6.9

# LangChain and AI
langchain==0.0.340
langchain-openai==0.0.2
langchain-community==0.0.3
openai==1.3.7
tiktoken==0.5.2

# Document processing
PyPDF2==3.0.1
python-docx==1.1.0
python-pptx==0.6.23
beautifulsoup4==4.12.2
markdown==3.5.1
unstructured==0.11.6

# File storage
boto3==1.34.0
minio==7.2.0

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# Utilities
python-dotenv==1.0.0
structlog==23.2.0
rich==13.7.0
typer==0.9.0

# Monitoring and observability
prometheus-client==0.19.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
factory-boy==3.3.0

# Code quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Development
watchdog==3.0.0
pre-commit==3.6.0

# Email
emails==0.6.0

# Background tasks
dramatiq[redis]==1.15.0

# Rate limiting
slowapi==0.1.9

# CORS
python-cors==1.0.0

# UUID
uuid==1.30

# Datetime
python-dateutil==2.8.2

# JSON Web Tokens
pyjwt==2.8.0

# Encryption
cryptography==41.0.8

# Environment variables
environs==10.0.0

# Async database
databases[postgresql]==0.8.0

# File type detection
python-magic==0.4.27

# Text processing
spacy==3.7.2
nltk==3.8.1

# Embeddings
sentence-transformers==2.2.2

# Configuration management
dynaconf==3.2.4

# API documentation
fastapi-users==12.1.2

# Workflow integration (n8n)
requests==2.31.0

# Trust scoring
numpy==1.25.2
scipy==1.11.4

# Audit logging
sqlalchemy-audit==0.1.0
