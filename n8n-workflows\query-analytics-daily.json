{"name": "Daily Query Analytics Report", "nodes": [{"parameters": {"rule": {"interval": [{"field": "days", "daysInterval": 1}]}, "options": {"timezone": "UTC"}}, "name": "Daily Trigger", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/analytics/daily-summary", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {"headers": {"parameter": [{"name": "Authorization", "value": "Bearer {{$json[\"api_token\"]}}"}]}}}, "name": "Fetch Analytics", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"conditions": {"number": [{"value1": "={{$json[\"total_queries\"]}}", "operation": "larger", "value2": 0}]}}, "name": "Has Activity", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"operation": "create", "resource": "message", "channel": "={{$json[\"slack_channel\"] || '#analytics'}}", "text": "📊 *Daily Analytics Report*\n\n*Date:* {{new Date().toLocaleDateString()}}\n*Total Queries:* {{$json[\"total_queries\"]}}\n*Unique Users:* {{$json[\"unique_users\"]}}\n*Average Trust Score:* {{($json[\"avg_trust_score\"] * 100).toFixed(1)}}%\n*Top Documents:*\n{{$json[\"top_documents\"].map(doc => `• ${doc.title} (${doc.query_count} queries)`).join('\\n')}}\n\n*Performance:*\n• Avg Response Time: {{$json[\"avg_response_time\"]}}ms\n• Success Rate: {{($json[\"success_rate\"] * 100).toFixed(1)}}%\n\n{{$json[\"total_queries\"] > $json[\"previous_day_queries\"] ? '📈 ' + (((($json[\"total_queries\"] - $json[\"previous_day_queries\"]) / $json[\"previous_day_queries\"]) * 100).toFixed(1)) + '% increase from yesterday' : '📉 ' + ((($json[\"previous_day_queries\"] - $json[\"total_queries\"]) / $json[\"previous_day_queries\"]) * 100).toFixed(1) + '% decrease from yesterday'}}", "username": "AIthentiqMind Analytics", "iconEmoji": ":chart_with_upwards_trend:"}, "name": "Send Analytics Report", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [850, 200]}, {"parameters": {"conditions": {"number": [{"value1": "={{$json[\"avg_trust_score\"]}}", "operation": "smaller", "value2": 0.7}]}}, "name": "Low Trust Score Alert", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [850, 400]}, {"parameters": {"operation": "create", "resource": "message", "channel": "={{$json[\"alert_channel\"] || '#alerts'}}", "text": "⚠️ *Trust Score Alert*\n\nDaily average trust score has dropped to {{($json[\"avg_trust_score\"] * 100).toFixed(1)}}%\n\nThis is below the 70% threshold. Please review:\n• Document quality\n• Embedding model performance\n• Query patterns\n\n*Affected Queries:* {{$json[\"low_trust_queries\"]}}\n*Recommended Actions:*\n• Review and update document sources\n• Check for outdated information\n• Consider retraining embeddings", "username": "<PERSON><PERSON><PERSON>q<PERSON><PERSON>", "iconEmoji": ":warning:"}, "name": "Send Trust Score Alert", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [1050, 400]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{$json[\"admin_email\"]}}", "subject": "AIthentiqMind Daily Analytics Report - {{new Date().toLocaleDateString()}}", "html": "<h2>Daily Analytics Report</h2><p><strong>Date:</strong> {{new Date().toLocaleDateString()}}</p><table border='1' style='border-collapse: collapse; width: 100%;'><tr><th>Metric</th><th>Value</th></tr><tr><td>Total Queries</td><td>{{$json[\"total_queries\"]}}</td></tr><tr><td>Unique Users</td><td>{{$json[\"unique_users\"]}}</td></tr><tr><td>Average Trust Score</td><td>{{($json[\"avg_trust_score\"] * 100).toFixed(1)}}%</td></tr><tr><td>Average Response Time</td><td>{{$json[\"avg_response_time\"]}}ms</td></tr><tr><td>Success Rate</td><td>{{($json[\"success_rate\"] * 100).toFixed(1)}}%</td></tr></table><h3>Top Documents</h3><ul>{{$json[\"top_documents\"].map(doc => `<li>${doc.title} (${doc.query_count} queries)</li>`).join('')}}</ul>"}, "name": "Send Email Report", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1050, 200]}], "connections": {"Daily Trigger": {"main": [[{"node": "Fetch Analytics", "type": "main", "index": 0}]]}, "Fetch Analytics": {"main": [[{"node": "Has Activity", "type": "main", "index": 0}]]}, "Has Activity": {"main": [[{"node": "Send Analytics Report", "type": "main", "index": 0}, {"node": "Low Trust Score Alert", "type": "main", "index": 0}]]}, "Send Analytics Report": {"main": [[{"node": "Send Email Report", "type": "main", "index": 0}]]}, "Low Trust Score Alert": {"main": [[{"node": "Send Trust Score Alert", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true}, "id": "2", "tags": [{"name": "aithentiqmind", "id": "1"}, {"name": "analytics", "id": "3"}, {"name": "reporting", "id": "4"}]}