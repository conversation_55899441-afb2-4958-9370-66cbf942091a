@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enterprise CSS Variables */
@layer base {
  :root {
    /* Light Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    
    /* Enterprise Brand Colors */
    --brand-primary: 221.2 83.2% 53.3%;
    --brand-secondary: 262.1 83.3% 57.8%;
    --brand-accent: 142.1 76.2% 36.3%;
    --brand-neutral: 215 16.3% 46.9%;
    
    /* Status Colors */
    --success: 142.1 76.2% 36.3%;
    --warning: 32.1 94.6% 43.7%;
    --error: 0 84.2% 60.2%;
    --info: 221.2 83.2% 53.3%;
    
    /* Enterprise Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-hard: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);
    
    /* Enterprise Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--brand-primary)) 0%, hsl(var(--brand-secondary)) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(var(--brand-accent)) 0%, hsl(var(--brand-primary)) 100%);
    --gradient-mesh: radial-gradient(at 40% 20%, hsla(228,100%,74%,0.3) 0px, transparent 50%), 
                     radial-gradient(at 80% 0%, hsla(189,100%,56%,0.3) 0px, transparent 50%), 
                     radial-gradient(at 0% 50%, hsla(355,100%,93%,0.3) 0px, transparent 50%);
  }

  .dark {
    /* Dark Theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* Dark Enterprise Shadows */
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.25), 0 10px 20px -2px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.15);
    --shadow-hard: 0 10px 40px -10px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
}

/* Enterprise Base Styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
  
  /* Selection */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
  
  /* Focus Styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

/* Enterprise Component Classes */
@layer components {
  /* Glass Morphism */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }
  
  /* Enterprise Cards */
  .card-enterprise {
    @apply bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl shadow-lg;
  }
  
  .card-enterprise-hover {
    @apply card-enterprise hover:shadow-xl hover:border-border transition-all duration-300;
  }
  
  /* Enterprise Buttons */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105;
  }
  
  .btn-secondary {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 text-white px-6 py-3 rounded-lg font-medium hover:bg-white/20 transition-all duration-200;
  }
  
  .btn-ghost {
    @apply text-foreground px-6 py-3 rounded-lg font-medium hover:bg-muted transition-all duration-200;
  }
  
  /* Enterprise Inputs */
  .input-enterprise {
    @apply bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg px-4 py-3 text-white placeholder:text-gray-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 transition-all duration-200;
  }
  
  /* Status Indicators */
  .status-online {
    @apply w-2 h-2 bg-green-500 rounded-full animate-pulse;
  }
  
  .status-offline {
    @apply w-2 h-2 bg-red-500 rounded-full;
  }
  
  .status-warning {
    @apply w-2 h-2 bg-yellow-500 rounded-full animate-pulse;
  }
  
  /* Enterprise Text */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent;
  }
  
  .text-enterprise {
    @apply text-foreground/90 leading-relaxed;
  }
  
  /* Enterprise Animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Enterprise Loading */
  .loading-spinner {
    @apply w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin;
  }
  
  /* Enterprise Metrics */
  .metric-card {
    @apply card-enterprise p-6 hover:shadow-xl transition-all duration-300;
  }
  
  .metric-value {
    @apply text-3xl font-bold text-gradient mb-2;
  }
  
  .metric-label {
    @apply text-sm font-medium text-gray-600;
  }
  
  .metric-change {
    @apply text-xs font-medium;
  }
  
  .metric-change-positive {
    @apply metric-change text-green-600;
  }
  
  .metric-change-negative {
    @apply metric-change text-red-600;
  }
  
  /* Enterprise Navigation */
  .nav-enterprise {
    @apply bg-white/90 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50;
  }
  
  .nav-link {
    @apply text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium;
  }
  
  .nav-link-active {
    @apply nav-link text-blue-600;
  }
}

/* Enterprise Keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Enterprise Responsive */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full {
    width: 100%;
  }
}

/* Enterprise Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
