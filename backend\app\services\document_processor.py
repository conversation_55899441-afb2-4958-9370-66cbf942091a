"""
Enhanced document processing service with RagFlow integration
"""

import asyncio
import hashlib
import mimetypes
from datetime import datetime
from typing import Dict, Any, List, Optional, BinaryIO
from pathlib import Path

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import get_logger
from app.core.config import settings
from app.models.document import Document
from app.models.tenant import Tenant
from app.services.ragflow.manager import get_ragflow_manager
from app.services.storage import get_storage_service
from app.services.audit import get_audit_service

logger = get_logger(__name__)


class DocumentProcessor:
    """Enhanced document processor with RagFlow integration"""
    
    def __init__(self):
        self.logger = get_logger("document_processor")
        self.supported_types = {
            'application/pdf': 'pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
            'application/msword': 'doc',
            'text/plain': 'txt',
            'text/markdown': 'md',
            'text/html': 'html',
            'application/rtf': 'rtf'
        }
    
    async def process_document(
        self,
        file_content: bytes,
        filename: str,
        tenant_id: str,
        user_id: str,
        title: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        db: AsyncSession = None
    ) -> Document:
        """Process a document through the complete pipeline"""
        
        try:
            # Validate file
            await self._validate_file(file_content, filename)
            
            # Extract metadata
            file_metadata = await self._extract_metadata(file_content, filename)
            
            # Create document record
            document = await self._create_document_record(
                filename=filename,
                title=title or filename,
                file_content=file_content,
                file_metadata=file_metadata,
                tenant_id=tenant_id,
                user_id=user_id,
                tags=tags or [],
                metadata=metadata or {},
                db=db
            )
            
            # Store file
            storage_service = get_storage_service()
            file_path = await storage_service.store_file(
                file_content=file_content,
                filename=f"{document.id}/{filename}",
                tenant_id=tenant_id
            )
            
            # Update document with file path
            await db.execute(
                update(Document)
                .where(Document.id == document.id)
                .values(file_path=file_path)
            )
            await db.commit()
            
            # Process with RagFlow (async)
            asyncio.create_task(
                self._process_with_ragflow(document.id, file_content, filename, db)
            )
            
            # Log audit event
            audit_service = get_audit_service()
            await audit_service.log_action(
                tenant_id=tenant_id,
                user_id=user_id,
                action="document.upload",
                resource="document",
                resource_id=str(document.id),
                details={
                    "filename": filename,
                    "file_size": len(file_content),
                    "file_type": file_metadata.get("file_type"),
                    "title": document.title
                }
            )
            
            return document
            
        except Exception as e:
            self.logger.error(f"Failed to process document {filename}: {e}")
            raise
    
    async def _validate_file(self, file_content: bytes, filename: str):
        """Validate file content and type"""
        
        # Check file size
        if len(file_content) > settings.MAX_FILE_SIZE:
            raise ValueError(f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes")
        
        # Check file type
        mime_type, _ = mimetypes.guess_type(filename)
        if mime_type not in self.supported_types:
            raise ValueError(f"Unsupported file type: {mime_type}")
        
        # Basic content validation
        if len(file_content) == 0:
            raise ValueError("File is empty")
    
    async def _extract_metadata(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Extract metadata from file"""
        
        mime_type, encoding = mimetypes.guess_type(filename)
        file_extension = Path(filename).suffix.lower()
        
        metadata = {
            "filename": filename,
            "file_size": len(file_content),
            "mime_type": mime_type,
            "encoding": encoding,
            "file_extension": file_extension,
            "file_type": self.supported_types.get(mime_type, "unknown"),
            "content_hash": hashlib.md5(file_content).hexdigest(),
            "upload_timestamp": datetime.utcnow().isoformat()
        }
        
        # Extract additional metadata based on file type
        if mime_type == 'application/pdf':
            metadata.update(await self._extract_pdf_metadata(file_content))
        elif mime_type in ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword']:
            metadata.update(await self._extract_docx_metadata(file_content))
        
        return metadata
    
    async def _extract_pdf_metadata(self, file_content: bytes) -> Dict[str, Any]:
        """Extract PDF-specific metadata"""
        try:
            # This would use a PDF library like PyPDF2 or pdfplumber
            # For now, return basic metadata
            return {
                "pdf_version": "unknown",
                "page_count": 0,
                "has_text": True,
                "has_images": False
            }
        except Exception as e:
            self.logger.warning(f"Failed to extract PDF metadata: {e}")
            return {}
    
    async def _extract_docx_metadata(self, file_content: bytes) -> Dict[str, Any]:
        """Extract DOCX-specific metadata"""
        try:
            # This would use python-docx library
            # For now, return basic metadata
            return {
                "word_count": 0,
                "paragraph_count": 0,
                "has_tables": False,
                "has_images": False
            }
        except Exception as e:
            self.logger.warning(f"Failed to extract DOCX metadata: {e}")
            return {}
    
    async def _create_document_record(
        self,
        filename: str,
        title: str,
        file_content: bytes,
        file_metadata: Dict[str, Any],
        tenant_id: str,
        user_id: str,
        tags: List[str],
        metadata: Dict[str, Any],
        db: AsyncSession
    ) -> Document:
        """Create document record in database"""
        
        document = Document(
            tenant_id=tenant_id,
            user_id=user_id,
            title=title,
            filename=filename,
            content="",  # Will be extracted later
            content_hash=file_metadata["content_hash"],
            file_type=file_metadata["file_type"],
            mime_type=file_metadata["mime_type"],
            file_size=file_metadata["file_size"],
            status="uploaded",
            metadata={**file_metadata, **metadata},
            tags=tags,
            is_public=False
        )
        
        db.add(document)
        await db.commit()
        await db.refresh(document)
        
        return document
    
    async def _process_with_ragflow(
        self,
        document_id: str,
        file_content: bytes,
        filename: str,
        db: AsyncSession
    ):
        """Process document with RagFlow (async background task)"""
        
        try:
            # Get document and tenant info
            result = await db.execute(
                select(Document.tenant_id, Document.title)
                .where(Document.id == document_id)
            )
            tenant_id, title = result.first()
            
            # Upload to RagFlow
            ragflow_manager = get_ragflow_manager()
            ragflow_doc_id = await ragflow_manager.upload_document_to_ragflow(
                document_id=document_id,
                tenant_id=tenant_id,
                file_content=file_content,
                filename=filename,
                db=db
            )
            
            self.logger.info(f"Document {document_id} uploaded to RagFlow as {ragflow_doc_id}")
            
            # Monitor processing status
            await self._monitor_ragflow_processing(document_id, db)
            
        except Exception as e:
            self.logger.error(f"Failed to process document {document_id} with RagFlow: {e}")
            
            # Update document status to failed
            await db.execute(
                update(Document)
                .where(Document.id == document_id)
                .values(status="processing_failed")
            )
            await db.commit()
    
    async def _monitor_ragflow_processing(self, document_id: str, db: AsyncSession):
        """Monitor RagFlow processing status"""
        
        ragflow_manager = get_ragflow_manager()
        max_attempts = 30  # 5 minutes with 10-second intervals
        attempt = 0
        
        while attempt < max_attempts:
            try:
                status_info = await ragflow_manager.check_document_processing_status(
                    document_id=document_id,
                    db=db
                )
                
                if status_info["status"] == "1":  # Processed successfully
                    self.logger.info(f"Document {document_id} processed successfully")
                    break
                elif status_info["status"] == "-1":  # Failed
                    self.logger.error(f"Document {document_id} processing failed")
                    break
                
                # Still processing, wait and retry
                await asyncio.sleep(10)
                attempt += 1
                
            except Exception as e:
                self.logger.error(f"Error monitoring document {document_id}: {e}")
                break
    
    async def delete_document(
        self,
        document_id: str,
        tenant_id: str,
        user_id: str,
        db: AsyncSession
    ) -> bool:
        """Delete document and all associated data"""
        
        try:
            # Get document info
            result = await db.execute(
                select(Document).where(
                    Document.id == document_id,
                    Document.tenant_id == tenant_id
                )
            )
            document = result.scalar_one_or_none()
            
            if not document:
                raise ValueError(f"Document {document_id} not found")
            
            # Delete from RagFlow
            ragflow_manager = get_ragflow_manager()
            await ragflow_manager.delete_document_from_ragflow(document_id, db)
            
            # Delete from storage
            storage_service = get_storage_service()
            if document.file_path:
                await storage_service.delete_file(document.file_path, tenant_id)
            
            # Delete from database
            await db.delete(document)
            await db.commit()
            
            # Log audit event
            audit_service = get_audit_service()
            await audit_service.log_action(
                tenant_id=tenant_id,
                user_id=user_id,
                action="document.delete",
                resource="document",
                resource_id=document_id,
                details={
                    "filename": document.filename,
                    "title": document.title
                }
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete document {document_id}: {e}")
            return False
    
    async def get_processing_status(self, document_id: str, db: AsyncSession) -> Dict[str, Any]:
        """Get document processing status"""
        
        try:
            # Get document from database
            result = await db.execute(
                select(Document).where(Document.id == document_id)
            )
            document = result.scalar_one_or_none()
            
            if not document:
                return {"status": "not_found"}
            
            # If document has RagFlow ID, check RagFlow status
            if document.ragflow_document_id:
                ragflow_manager = get_ragflow_manager()
                ragflow_status = await ragflow_manager.check_document_processing_status(
                    document_id=document_id,
                    db=db
                )
                
                return {
                    "status": document.status,
                    "ragflow_status": ragflow_status,
                    "chunk_count": document.chunk_count,
                    "created_at": document.created_at.isoformat(),
                    "updated_at": document.updated_at.isoformat()
                }
            
            return {
                "status": document.status,
                "chunk_count": document.chunk_count,
                "created_at": document.created_at.isoformat(),
                "updated_at": document.updated_at.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get processing status for {document_id}: {e}")
            return {"status": "error", "error": str(e)}


# Global instance
document_processor = DocumentProcessor()


def get_document_processor() -> DocumentProcessor:
    """Get document processor instance"""
    return document_processor
