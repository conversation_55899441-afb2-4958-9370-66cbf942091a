# AIthentiqMind Development Setup Script
# This script sets up the development environment for the AIthentiqMind platform

param(
    [switch]$SkipDocker,
    [switch]$SkipDependencies,
    [switch]$Force
)

Write-Host "🚀 Setting up AIthentiqMind Development Environment" -ForegroundColor Green

# Check if Docker is installed and running
if (-not $SkipDocker) {
    Write-Host "📦 Checking Docker installation..." -ForegroundColor Yellow
    try {
        $dockerVersion = docker --version
        Write-Host "✅ Docker found: $dockerVersion" -ForegroundColor Green
        
        # Check if Docker is running
        docker info | Out-Null
        Write-Host "✅ Docker is running" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Docker is not installed or not running" -ForegroundColor Red
        Write-Host "Please install Docker Desktop and ensure it's running" -ForegroundColor Red
        exit 1
    }
}

# Create .env file from example if it doesn't exist
if (-not (Test-Path ".env") -or $Force) {
    Write-Host "📝 Creating .env file from template..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✅ .env file created. Please update with your actual values." -ForegroundColor Green
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @(
    "backend/uploads",
    "backend/logs",
    "frontend/.next",
    "infrastructure/nginx/logs",
    "infrastructure/nginx/ssl",
    "infrastructure/prometheus",
    "infrastructure/grafana/provisioning",
    "backups/postgres",
    "backups/qdrant",
    "n8n-workflows/templates",
    "docs/api",
    "tests/integration",
    "tests/unit"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  ✅ Created: $dir" -ForegroundColor Green
    }
}

# Install backend dependencies
if (-not $SkipDependencies) {
    Write-Host "🐍 Setting up Python backend..." -ForegroundColor Yellow
    
    # Check if Python is installed
    try {
        $pythonVersion = python --version
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Python is not installed" -ForegroundColor Red
        Write-Host "Please install Python 3.9+ from https://python.org" -ForegroundColor Red
        exit 1
    }
    
    # Create virtual environment for backend
    if (-not (Test-Path "backend/venv") -or $Force) {
        Write-Host "  📦 Creating Python virtual environment..." -ForegroundColor Yellow
        Set-Location backend
        python -m venv venv
        Set-Location ..
        Write-Host "  ✅ Virtual environment created" -ForegroundColor Green
    }
    
    # Install Node.js dependencies for frontend
    Write-Host "📦 Setting up Node.js frontend..." -ForegroundColor Yellow
    
    # Check if Node.js is installed
    try {
        $nodeVersion = node --version
        Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Node.js is not installed" -ForegroundColor Red
        Write-Host "Please install Node.js 18+ from https://nodejs.org" -ForegroundColor Red
        exit 1
    }
}

# Generate SSL certificates for development
Write-Host "🔐 Generating development SSL certificates..." -ForegroundColor Yellow
if (-not (Test-Path "infrastructure/nginx/ssl/cert.pem") -or $Force) {
    # Create self-signed certificate for development
    $certPath = "infrastructure/nginx/ssl"
    openssl req -x509 -newkey rsa:4096 -keyout "$certPath/key.pem" -out "$certPath/cert.pem" -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"
    Write-Host "  ✅ SSL certificates generated" -ForegroundColor Green
}

# Create initial configuration files
Write-Host "⚙️ Creating configuration files..." -ForegroundColor Yellow

# Prometheus configuration
$prometheusConfig = @"
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'aithentiq-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'aithentiq-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'qdrant'
    static_configs:
      - targets: ['qdrant:6333']
"@

$prometheusConfig | Out-File -FilePath "infrastructure/prometheus/prometheus.yml" -Encoding UTF8

Write-Host "✅ Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Next steps:" -ForegroundColor Cyan
Write-Host "1. Update .env file with your API keys and configuration" -ForegroundColor White
Write-Host "2. Run: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d" -ForegroundColor White
Write-Host "3. Access the application at:" -ForegroundColor White
Write-Host "   - Frontend: http://localhost:3000" -ForegroundColor White
Write-Host "   - Backend API: http://localhost:8000" -ForegroundColor White
Write-Host "   - n8n: http://localhost:5678" -ForegroundColor White
Write-Host "   - Qdrant: http://localhost:6333" -ForegroundColor White
Write-Host "   - Grafana: http://localhost:3001" -ForegroundColor White
Write-Host ""
