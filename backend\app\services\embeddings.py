"""
Embedding service with OpenAI integration
"""

import asyncio
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json

import openai
from openai import AsyncOpenA<PERSON>

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class EmbeddingService:
    """Service for generating embeddings using OpenAI"""
    
    def __init__(self):
        self.logger = get_logger("embeddings")
        self.client: Optional[AsyncOpenAI] = None
        self.model = settings.OPENAI_EMBEDDING_MODEL
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = timedelta(hours=24)
        
        # Initialize OpenAI client
        self._init_client()
    
    def _init_client(self):
        """Initialize OpenAI client"""
        try:
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY and settings.OPENAI_API_KEY != "mock-openai-key":
                self.client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
                self.logger.info("OpenAI client initialized")
            else:
                self.logger.warning("OpenAI API key not configured, using mock embeddings")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI client: {e}")
            self.client = None
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text"""
        try:
            # Check cache first
            cache_key = self._get_cache_key(text)
            if cache_key in self.cache:
                cached_item = self.cache[cache_key]
                if datetime.utcnow() - cached_item["timestamp"] < self.cache_ttl:
                    self.logger.debug(f"Using cached embedding for text: {text[:50]}...")
                    return cached_item["embedding"]
                else:
                    # Remove expired cache entry
                    del self.cache[cache_key]
            
            # Generate new embedding
            if self.client:
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=text
                )
                embedding = response.data[0].embedding
                
                # Cache the result
                self.cache[cache_key] = {
                    "embedding": embedding,
                    "timestamp": datetime.utcnow()
                }
                
                self.logger.debug(f"Generated embedding for text: {text[:50]}...")
                return embedding
            else:
                # Return mock embedding for development
                return self._generate_mock_embedding(text)
                
        except Exception as e:
            self.logger.error(f"Failed to generate embedding: {e}")
            # Return mock embedding as fallback
            return self._generate_mock_embedding(text)
    
    async def generate_embeddings_batch(
        self, 
        texts: List[str],
        batch_size: int = 100
    ) -> List[List[float]]:
        """Generate embeddings for multiple texts in batches"""
        try:
            embeddings = []
            
            # Process in batches
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                if self.client:
                    # Use OpenAI batch API
                    response = await self.client.embeddings.create(
                        model=self.model,
                        input=batch
                    )
                    
                    batch_embeddings = [item.embedding for item in response.data]
                    embeddings.extend(batch_embeddings)
                    
                    # Cache results
                    for text, embedding in zip(batch, batch_embeddings):
                        cache_key = self._get_cache_key(text)
                        self.cache[cache_key] = {
                            "embedding": embedding,
                            "timestamp": datetime.utcnow()
                        }
                else:
                    # Generate mock embeddings
                    for text in batch:
                        embeddings.append(self._generate_mock_embedding(text))
                
                # Small delay to respect rate limits
                if i + batch_size < len(texts):
                    await asyncio.sleep(0.1)
            
            self.logger.info(f"Generated {len(embeddings)} embeddings in batches")
            return embeddings
            
        except Exception as e:
            self.logger.error(f"Failed to generate batch embeddings: {e}")
            # Return mock embeddings as fallback
            return [self._generate_mock_embedding(text) for text in texts]
    
    def _generate_mock_embedding(self, text: str) -> List[float]:
        """Generate mock embedding for development/testing"""
        # Create a deterministic but varied embedding based on text content
        import hashlib
        import numpy as np
        
        # Use text hash to create deterministic embedding
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Convert hash to numbers and normalize
        np.random.seed(int(text_hash[:8], 16))
        embedding = np.random.normal(0, 1, 1536).tolist()
        
        # Normalize to unit vector
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = (np.array(embedding) / norm).tolist()
        
        return embedding
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key for text"""
        return hashlib.md5(f"{self.model}:{text}".encode()).hexdigest()
    
    async def clear_cache(self):
        """Clear embedding cache"""
        self.cache.clear()
        self.logger.info("Embedding cache cleared")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        now = datetime.utcnow()
        valid_entries = 0
        expired_entries = 0
        
        for cache_key, cached_item in self.cache.items():
            if now - cached_item["timestamp"] < self.cache_ttl:
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            "total_entries": len(self.cache),
            "valid_entries": valid_entries,
            "expired_entries": expired_entries,
            "cache_hit_rate": valid_entries / max(len(self.cache), 1),
            "model": self.model
        }
    
    async def cleanup_expired_cache(self):
        """Remove expired cache entries"""
        now = datetime.utcnow()
        expired_keys = []
        
        for cache_key, cached_item in self.cache.items():
            if now - cached_item["timestamp"] >= self.cache_ttl:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    async def estimate_cost(self, texts: List[str]) -> Dict[str, Any]:
        """Estimate cost for embedding generation"""
        total_tokens = sum(len(text.split()) for text in texts)
        
        # OpenAI pricing (approximate)
        cost_per_1k_tokens = 0.0001  # $0.0001 per 1K tokens for text-embedding-ada-002
        estimated_cost = (total_tokens / 1000) * cost_per_1k_tokens
        
        return {
            "total_texts": len(texts),
            "estimated_tokens": total_tokens,
            "estimated_cost_usd": round(estimated_cost, 6),
            "model": self.model
        }
    
    async def health_check(self) -> bool:
        """Check if embedding service is healthy"""
        try:
            if self.client:
                # Test with a simple embedding
                test_embedding = await self.generate_embedding("test")
                return len(test_embedding) == 1536  # Expected dimension
            else:
                # Mock mode is always healthy
                return True
                
        except Exception as e:
            self.logger.error(f"Embedding service health check failed: {e}")
            return False


class ChunkEmbeddingService:
    """Service for embedding document chunks"""
    
    def __init__(self, embedding_service: EmbeddingService):
        self.embedding_service = embedding_service
        self.logger = get_logger("chunk_embeddings")
    
    async def embed_document_chunks(
        self,
        chunks: List[Dict[str, Any]],
        document_id: str,
        tenant_id: str
    ) -> List[Dict[str, Any]]:
        """Generate embeddings for document chunks"""
        try:
            # Extract text from chunks
            texts = [chunk["content"] for chunk in chunks]
            
            # Generate embeddings
            embeddings = await self.embedding_service.generate_embeddings_batch(texts)
            
            # Combine chunks with embeddings
            embedded_chunks = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                embedded_chunk = {
                    "id": f"{document_id}_chunk_{i}",
                    "document_id": document_id,
                    "tenant_id": tenant_id,
                    "chunk_index": i,
                    "content": chunk["content"],
                    "embedding": embedding,
                    "metadata": {
                        **chunk.get("metadata", {}),
                        "chunk_size": len(chunk["content"]),
                        "embedding_model": self.embedding_service.model,
                        "created_at": datetime.utcnow().isoformat()
                    }
                }
                embedded_chunks.append(embedded_chunk)
            
            self.logger.info(f"Generated embeddings for {len(embedded_chunks)} chunks from document {document_id}")
            return embedded_chunks
            
        except Exception as e:
            self.logger.error(f"Failed to embed document chunks: {e}")
            raise
    
    async def embed_query(self, query: str) -> List[float]:
        """Generate embedding for a query"""
        return await self.embedding_service.generate_embedding(query)


# Global embedding service instance
embedding_service = EmbeddingService()
chunk_embedding_service = ChunkEmbeddingService(embedding_service)


def get_embedding_service() -> EmbeddingService:
    """Get embedding service instance"""
    return embedding_service


def get_chunk_embedding_service() -> ChunkEmbeddingService:
    """Get chunk embedding service instance"""
    return chunk_embedding_service
