"""
Query model with detailed tracking and analytics
"""

from typing import Dict, Any, List
from sqlalchemy import Column, String, DateTime, Text, Float, ForeignKey, JSON, Integer, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Query(Base):
    __tablename__ = "queries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)

    # Query content
    question = Column(Text, nullable=False)
    answer = Column(Text, nullable=True)

    # Context and sources
    context_chunks = Column(JSON, default=list)  # Retrieved chunks
    sources = Column(JSON, default=list)  # Source documents
    document_ids = Column(JSON, default=list)  # Filtered document IDs

    # Trust and quality metrics
    trust_score = Column(Float, nullable=True)
    trust_factors = Column(JSON, default=dict)
    confidence_score = Column(Float, nullable=True)
    relevance_score = Column(Float, nullable=True)

    # Performance metrics
    response_time = Column(Float, nullable=True)  # Total response time in seconds
    retrieval_time = Column(Float, nullable=True)  # Time to retrieve context
    generation_time = Column(Float, nullable=True)  # Time to generate response

    # Query parameters
    top_k = Column(Integer, default=5)
    temperature = Column(Float, nullable=True)
    model_used = Column(String, nullable=True)
    embedding_model_used = Column(String, nullable=True)

    # Status and feedback
    status = Column(String, default="completed")  # processing, completed, failed
    error_message = Column(Text, nullable=True)
    user_feedback = Column(String, nullable=True)  # thumbs_up, thumbs_down
    user_rating = Column(Integer, nullable=True)  # 1-5 stars

    # Analytics
    tokens_used = Column(Integer, nullable=True)
    cost_estimate = Column(Float, nullable=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship("Tenant", back_populates="queries")
    user = relationship("User", back_populates="queries")
    trust_score_record = relationship("TrustScore", back_populates="query", uselist=False)

    def get_context_chunks(self) -> List[Dict[str, Any]]:
        """Get context chunks"""
        return self.context_chunks or []

    def get_sources(self) -> List[Dict[str, Any]]:
        """Get source information"""
        return self.sources or []

    def get_document_ids(self) -> List[str]:
        """Get filtered document IDs"""
        return self.document_ids or []

    def get_trust_factors(self) -> Dict[str, Any]:
        """Get trust score factors"""
        return self.trust_factors or {}

    def add_context_chunk(self, chunk: Dict[str, Any]):
        """Add context chunk"""
        chunks = self.get_context_chunks()
        chunks.append(chunk)
        self.context_chunks = chunks

    def add_source(self, source: Dict[str, Any]):
        """Add source information"""
        sources = self.get_sources()
        sources.append(source)
        self.sources = sources

    def set_trust_score(self, score: float, factors: Dict[str, Any]):
        """Set trust score and factors"""
        self.trust_score = score
        self.trust_factors = factors

        # Extract individual factor scores
        if factors:
            self.confidence_score = factors.get("confidence_score")
            self.relevance_score = factors.get("relevance_score")

    def set_performance_metrics(
        self,
        total_time: float,
        retrieval_time: float = None,
        generation_time: float = None
    ):
        """Set performance metrics"""
        self.response_time = total_time
        if retrieval_time is not None:
            self.retrieval_time = retrieval_time
        if generation_time is not None:
            self.generation_time = generation_time

    def mark_completed(self):
        """Mark query as completed"""
        from datetime import datetime
        self.status = "completed"
        self.completed_at = datetime.utcnow()
        self.error_message = None

    def mark_failed(self, error_message: str):
        """Mark query as failed"""
        self.status = "failed"
        self.error_message = error_message
        self.completed_at = None

    def set_user_feedback(self, feedback: str, rating: int = None):
        """Set user feedback"""
        if feedback in ["thumbs_up", "thumbs_down"]:
            self.user_feedback = feedback
        if rating and 1 <= rating <= 5:
            self.user_rating = rating

    def calculate_cost_estimate(self):
        """Calculate estimated cost based on tokens used"""
        if not self.tokens_used:
            return 0.0

        # Rough cost estimation (adjust based on actual pricing)
        cost_per_1k_tokens = 0.002  # Example: $0.002 per 1K tokens
        self.cost_estimate = (self.tokens_used / 1000) * cost_per_1k_tokens
        return self.cost_estimate

    def get_quality_metrics(self) -> Dict[str, Any]:
        """Get quality metrics summary"""
        return {
            "trust_score": self.trust_score,
            "confidence_score": self.confidence_score,
            "relevance_score": self.relevance_score,
            "user_feedback": self.user_feedback,
            "user_rating": self.user_rating,
            "response_time": self.response_time,
            "source_count": len(self.get_sources())
        }

    def to_dict(self) -> Dict[str, Any]:
        """Convert query to dictionary"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "user_id": str(self.user_id),
            "question": self.question,
            "answer": self.answer,
            "trust_score": self.trust_score,
            "trust_factors": self.get_trust_factors(),
            "sources": self.get_sources(),
            "document_ids": self.get_document_ids(),
            "quality_metrics": self.get_quality_metrics(),
            "performance": {
                "response_time": self.response_time,
                "retrieval_time": self.retrieval_time,
                "generation_time": self.generation_time
            },
            "parameters": {
                "top_k": self.top_k,
                "temperature": self.temperature,
                "model_used": self.model_used,
                "embedding_model_used": self.embedding_model_used
            },
            "status": self.status,
            "error_message": self.error_message,
            "tokens_used": self.tokens_used,
            "cost_estimate": self.cost_estimate,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }
