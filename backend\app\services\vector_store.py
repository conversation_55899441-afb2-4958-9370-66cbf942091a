"""
Vector store service with Qdrant integration
"""

import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import numpy as np

from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import Distance, VectorParams, PointStruct

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class VectorStore:
    """Vector store service with Qdrant backend"""
    
    def __init__(self):
        self.logger = get_logger("vector_store")
        self.client: Optional[QdrantClient] = None
        self.embedding_dim = 1536  # OpenAI text-embedding-ada-002 dimension
        
        # Initialize Qdrant client
        self._init_client()
    
    def _init_client(self):
        """Initialize Qdrant client"""
        try:
            if hasattr(settings, 'QDRANT_URL') and settings.QDRANT_URL:
                self.client = QdrantClient(
                    url=settings.QDRANT_URL,
                    api_key=getattr(settings, 'QDRANT_API_KEY', None)
                )
                self.logger.info("Qdrant client initialized")
            else:
                # Use in-memory mode for development
                self.client = QdrantClient(":memory:")
                self.logger.info("Qdrant client initialized in memory mode")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Qdrant client: {e}")
            self.client = None
    
    async def create_collection(self, collection_name: str) -> bool:
        """Create a new collection"""
        try:
            if not self.client:
                return False
            
            # Check if collection exists
            collections = self.client.get_collections()
            existing_names = [col.name for col in collections.collections]
            
            if collection_name in existing_names:
                self.logger.info(f"Collection {collection_name} already exists")
                return True
            
            # Create collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=self.embedding_dim,
                    distance=Distance.COSINE
                )
            )
            
            self.logger.info(f"Created collection: {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create collection {collection_name}: {e}")
            return False
    
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection"""
        try:
            if not self.client:
                return False
            
            self.client.delete_collection(collection_name)
            self.logger.info(f"Deleted collection: {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete collection {collection_name}: {e}")
            return False
    
    async def upsert_vectors(
        self,
        collection_name: str,
        vectors: List[Dict[str, Any]]
    ) -> bool:
        """Upsert vectors into collection"""
        try:
            if not self.client:
                return False
            
            # Ensure collection exists
            await self.create_collection(collection_name)
            
            # Convert to Qdrant points
            points = []
            for vector_data in vectors:
                point = PointStruct(
                    id=vector_data["id"],
                    vector=vector_data["vector"],
                    payload=vector_data.get("metadata", {})
                )
                points.append(point)
            
            # Upsert points
            self.client.upsert(
                collection_name=collection_name,
                points=points
            )
            
            self.logger.info(f"Upserted {len(points)} vectors to {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to upsert vectors to {collection_name}: {e}")
            return False
    
    async def search_similar(
        self,
        collection_name: str,
        query_vector: List[float],
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """Search for similar vectors"""
        try:
            if not self.client:
                return []
            
            # Build filter conditions
            filter_conditions = None
            if filters:
                filter_conditions = self._build_filter_conditions(filters)
            
            # Search
            search_result = self.client.search(
                collection_name=collection_name,
                query_vector=query_vector,
                limit=top_k,
                query_filter=filter_conditions,
                score_threshold=score_threshold
            )
            
            # Convert results
            results = []
            for hit in search_result:
                result = {
                    "id": hit.id,
                    "score": hit.score,
                    "metadata": hit.payload
                }
                results.append(result)
            
            self.logger.info(f"Found {len(results)} similar vectors in {collection_name}")
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search in {collection_name}: {e}")
            return []
    
    def _build_filter_conditions(self, filters: Dict[str, Any]) -> models.Filter:
        """Build Qdrant filter conditions"""
        conditions = []
        
        for key, value in filters.items():
            if isinstance(value, list):
                # Multiple values - use should (OR)
                should_conditions = []
                for v in value:
                    should_conditions.append(
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=v)
                        )
                    )
                conditions.append(
                    models.Filter(should=should_conditions)
                )
            else:
                # Single value
                conditions.append(
                    models.FieldCondition(
                        key=key,
                        match=models.MatchValue(value=value)
                    )
                )
        
        return models.Filter(must=conditions) if conditions else None
    
    async def delete_vectors(
        self,
        collection_name: str,
        vector_ids: List[Union[str, int]]
    ) -> bool:
        """Delete vectors by IDs"""
        try:
            if not self.client:
                return False
            
            self.client.delete(
                collection_name=collection_name,
                points_selector=models.PointIdsList(
                    points=vector_ids
                )
            )
            
            self.logger.info(f"Deleted {len(vector_ids)} vectors from {collection_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete vectors from {collection_name}: {e}")
            return False
    
    async def get_collection_info(self, collection_name: str) -> Dict[str, Any]:
        """Get collection information"""
        try:
            if not self.client:
                return {"error": "Client not initialized"}
            
            info = self.client.get_collection(collection_name)
            
            return {
                "name": collection_name,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status.value,
                "optimizer_status": info.optimizer_status.status.value,
                "disk_data_size": info.disk_data_size,
                "ram_data_size": info.ram_data_size
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get collection info for {collection_name}: {e}")
            return {"error": str(e)}
    
    async def list_collections(self) -> List[str]:
        """List all collections"""
        try:
            if not self.client:
                return []
            
            collections = self.client.get_collections()
            return [col.name for col in collections.collections]
            
        except Exception as e:
            self.logger.error(f"Failed to list collections: {e}")
            return []
    
    async def health_check(self) -> bool:
        """Check if vector store is healthy"""
        try:
            if not self.client:
                return False
            
            # Try to list collections as a health check
            collections = self.client.get_collections()
            return True
            
        except Exception as e:
            self.logger.error(f"Vector store health check failed: {e}")
            return False


# Global vector store instance
vector_store = VectorStore()


def get_vector_store() -> VectorStore:
    """Get vector store instance"""
    return vector_store
