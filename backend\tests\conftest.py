"""
Pytest configuration and fixtures
"""

import asyncio
import pytest
import pytest_asyncio
from typing import Async<PERSON>enerator, Generator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from httpx import AsyncClient
from fastapi.testclient import TestClient

from app.main import app
from app.db.database import Base, get_db
from app.core.config import settings
from app.models.tenant import Tenant
from app.models.user import User
from app.models.document import Document
from app.models.query import Query


# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,
    future=True
)

# Create test session factory
TestSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False
)


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session"""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async with TestSessionLocal() as session:
        yield session
    
    # Drop tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create a test client"""
    
    def get_test_db():
        return db_session
    
    app.dependency_overrides[get_db] = get_test_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture
def sync_client() -> TestClient:
    """Create a synchronous test client"""
    return TestClient(app)


@pytest_asyncio.fixture
async def test_tenant(db_session: AsyncSession) -> Tenant:
    """Create a test tenant"""
    tenant = Tenant(
        name="Test Tenant",
        domain="test.example.com",
        subdomain="test",
        is_active=True,
        plan="enterprise",
        contact_email="<EMAIL>",
        contact_name="Test Admin",
        max_users=100,
        max_documents=1000,
        max_storage_gb=10,
        max_queries_per_month=10000
    )
    
    db_session.add(tenant)
    await db_session.commit()
    await db_session.refresh(tenant)
    
    return tenant


@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession, test_tenant: Tenant) -> User:
    """Create a test user"""
    user = User(
        tenant_id=test_tenant.id,
        email="<EMAIL>",
        full_name="Test User",
        auth0_user_id="auth0|test123",
        is_active=True,
        is_admin=False,
        is_tenant_admin=True,
        roles=["user", "admin"],
        permissions=["read", "write", "admin"]
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def test_admin_user(db_session: AsyncSession, test_tenant: Tenant) -> User:
    """Create a test admin user"""
    user = User(
        tenant_id=test_tenant.id,
        email="<EMAIL>",
        full_name="Admin User",
        auth0_user_id="auth0|admin123",
        is_active=True,
        is_admin=True,
        is_tenant_admin=True,
        roles=["user", "admin", "super_admin"],
        permissions=["read", "write", "admin", "super_admin"]
    )
    
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def test_document(db_session: AsyncSession, test_tenant: Tenant, test_user: User) -> Document:
    """Create a test document"""
    document = Document(
        tenant_id=test_tenant.id,
        user_id=test_user.id,
        title="Test Document",
        filename="test.pdf",
        content="This is a test document content for testing purposes.",
        content_hash="test_hash_123",
        file_type="pdf",
        mime_type="application/pdf",
        file_size=1024,
        status="processed",
        chunk_count=5,
        embedding_model="text-embedding-ada-002",
        metadata={"test": True},
        tags=["test", "document"],
        is_public=False
    )
    
    db_session.add(document)
    await db_session.commit()
    await db_session.refresh(document)
    
    return document


@pytest_asyncio.fixture
async def test_query(db_session: AsyncSession, test_tenant: Tenant, test_user: User) -> Query:
    """Create a test query"""
    query = Query(
        tenant_id=test_tenant.id,
        user_id=test_user.id,
        question="What is this document about?",
        answer="This document is about testing the RAG system.",
        context_chunks=[
            {
                "content": "This is a test document content",
                "document_id": "test_doc_id",
                "chunk_id": "chunk_1",
                "relevance_score": 0.95
            }
        ],
        sources=[
            {
                "document_id": "test_doc_id",
                "title": "Test Document",
                "relevance_score": 0.95
            }
        ],
        document_ids=["test_doc_id"],
        trust_score=0.85,
        confidence_score=0.9,
        relevance_score=0.95,
        response_time=1.5,
        retrieval_time=0.5,
        generation_time=1.0,
        top_k=5,
        temperature=0.1,
        model_used="gpt-4",
        embedding_model_used="text-embedding-ada-002",
        status="completed",
        tokens_used=150,
        cost_estimate=0.001
    )
    
    db_session.add(query)
    await db_session.commit()
    await db_session.refresh(query)
    
    return query


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response"""
    return {
        "choices": [
            {
                "message": {
                    "content": "This is a test response from the AI model."
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 50,
            "completion_tokens": 10,
            "total_tokens": 60
        }
    }


@pytest.fixture
def mock_embedding_response():
    """Mock OpenAI embedding response"""
    return {
        "data": [
            {
                "embedding": [0.1] * 1536  # Mock 1536-dimensional embedding
            }
        ],
        "usage": {
            "total_tokens": 10
        }
    }


@pytest.fixture
def sample_pdf_content():
    """Sample PDF content for testing"""
    return b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Test PDF Content) Tj\nET\nendstream\nendobj\nxref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000206 00000 n \ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n299\n%%EOF"


@pytest.fixture
def auth_headers(test_user: User):
    """Create authentication headers for testing"""
    # In a real implementation, you'd create a proper JWT token
    return {
        "Authorization": f"Bearer test_token_{test_user.id}",
        "X-Tenant-ID": str(test_user.tenant_id)
    }


@pytest.fixture
def admin_auth_headers(test_admin_user: User):
    """Create admin authentication headers for testing"""
    return {
        "Authorization": f"Bearer admin_token_{test_admin_user.id}",
        "X-Tenant-ID": str(test_admin_user.tenant_id)
    }


# Test utilities
class TestUtils:
    """Utility functions for testing"""
    
    @staticmethod
    def create_test_file(content: str, filename: str = "test.txt") -> bytes:
        """Create a test file with given content"""
        return content.encode('utf-8')
    
    @staticmethod
    def assert_response_success(response, expected_status: int = 200):
        """Assert that response is successful"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    @staticmethod
    def assert_response_error(response, expected_status: int = 400):
        """Assert that response is an error"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"


@pytest.fixture
def test_utils():
    """Provide test utilities"""
    return TestUtils
