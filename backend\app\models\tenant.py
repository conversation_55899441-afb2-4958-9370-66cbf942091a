"""
Tenant model with settings and limits
"""

from typing import Dict, Any
from sqlalchemy import Column, String, DateTime, Boolean, JSON, Integer, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Tenant(Base):
    __tablename__ = "tenants"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    domain = Column(String, unique=True, nullable=False, index=True)
    subdomain = Column(String, unique=True, nullable=True, index=True)

    # Status and configuration
    is_active = Column(Boolean, default=True)
    plan = Column(String, default="free")  # free, pro, enterprise

    # Contact information
    contact_email = Column(String, nullable=True)
    contact_name = Column(String, nullable=True)

    # Limits and quotas
    max_users = Column(Integer, default=5)
    max_documents = Column(Integer, default=100)
    max_storage_gb = Column(Integer, default=1)
    max_queries_per_month = Column(Integer, default=1000)

    # Current usage
    current_users = Column(Integer, default=0)
    current_documents = Column(Integer, default=0)
    current_storage_bytes = Column(Integer, default=0)
    current_queries_this_month = Column(Integer, default=0)

    # Settings and preferences
    settings = Column(JSON, default=dict)
    ai_settings = Column(JSON, default=dict)

    # Billing information
    stripe_customer_id = Column(String, nullable=True)
    subscription_status = Column(String, default="active")
    trial_ends_at = Column(DateTime(timezone=True), nullable=True)

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    users = relationship("User", back_populates="tenant")
    documents = relationship("Document", back_populates="tenant")
    queries = relationship("Query", back_populates="tenant")
    workflows = relationship("Workflow", back_populates="tenant")
    data_sources = relationship("DataSource", back_populates="tenant")
    audit_logs = relationship("AuditLog", back_populates="tenant")

    def get_settings(self) -> Dict[str, Any]:
        """Get tenant settings with defaults"""
        default_settings = {
            "ai_model": "gpt-4",
            "embedding_model": "text-embedding-ada-002",
            "max_tokens": 4000,
            "temperature": 0.1,
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "trust_score_threshold": 0.7,
            "enable_workflows": True,
            "enable_analytics": True,
            "data_retention_days": 365
        }

        tenant_settings = self.settings or {}
        return {**default_settings, **tenant_settings}

    def get_ai_settings(self) -> Dict[str, Any]:
        """Get AI-specific settings"""
        default_ai_settings = {
            "provider": "openai",
            "model": "gpt-4",
            "embedding_model": "text-embedding-ada-002",
            "max_tokens": 4000,
            "temperature": 0.1,
            "system_prompt": None,
            "enable_streaming": True,
            "enable_citations": True
        }

        ai_settings = self.ai_settings or {}
        return {**default_ai_settings, **ai_settings}

    def check_limits(self) -> Dict[str, Any]:
        """Check current usage against limits"""
        return {
            "users": {
                "current": self.current_users,
                "limit": self.max_users,
                "percentage": (self.current_users / self.max_users * 100) if self.max_users > 0 else 0,
                "exceeded": self.current_users >= self.max_users
            },
            "documents": {
                "current": self.current_documents,
                "limit": self.max_documents,
                "percentage": (self.current_documents / self.max_documents * 100) if self.max_documents > 0 else 0,
                "exceeded": self.current_documents >= self.max_documents
            },
            "storage": {
                "current_gb": round(self.current_storage_bytes / (1024**3), 2),
                "limit_gb": self.max_storage_gb,
                "percentage": (self.current_storage_bytes / (self.max_storage_gb * 1024**3) * 100) if self.max_storage_gb > 0 else 0,
                "exceeded": self.current_storage_bytes >= (self.max_storage_gb * 1024**3)
            },
            "queries": {
                "current": self.current_queries_this_month,
                "limit": self.max_queries_per_month,
                "percentage": (self.current_queries_this_month / self.max_queries_per_month * 100) if self.max_queries_per_month > 0 else 0,
                "exceeded": self.current_queries_this_month >= self.max_queries_per_month
            }
        }

    def can_add_user(self) -> bool:
        """Check if tenant can add another user"""
        return self.current_users < self.max_users

    def can_add_document(self) -> bool:
        """Check if tenant can add another document"""
        return self.current_documents < self.max_documents

    def can_use_storage(self, additional_bytes: int) -> bool:
        """Check if tenant can use additional storage"""
        total_bytes = self.current_storage_bytes + additional_bytes
        return total_bytes <= (self.max_storage_gb * 1024**3)

    def can_make_query(self) -> bool:
        """Check if tenant can make another query this month"""
        return self.current_queries_this_month < self.max_queries_per_month

    def increment_usage(self, metric: str, amount: int = 1):
        """Increment usage counter"""
        if metric == "users":
            self.current_users += amount
        elif metric == "documents":
            self.current_documents += amount
        elif metric == "storage":
            self.current_storage_bytes += amount
        elif metric == "queries":
            self.current_queries_this_month += amount

    def decrement_usage(self, metric: str, amount: int = 1):
        """Decrement usage counter"""
        if metric == "users":
            self.current_users = max(0, self.current_users - amount)
        elif metric == "documents":
            self.current_documents = max(0, self.current_documents - amount)
        elif metric == "storage":
            self.current_storage_bytes = max(0, self.current_storage_bytes - amount)
        elif metric == "queries":
            self.current_queries_this_month = max(0, self.current_queries_this_month - amount)

    def reset_monthly_usage(self):
        """Reset monthly usage counters"""
        self.current_queries_this_month = 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert tenant to dictionary"""
        return {
            "id": str(self.id),
            "name": self.name,
            "domain": self.domain,
            "subdomain": self.subdomain,
            "is_active": self.is_active,
            "plan": self.plan,
            "contact_email": self.contact_email,
            "contact_name": self.contact_name,
            "limits": self.check_limits(),
            "settings": self.get_settings(),
            "ai_settings": self.get_ai_settings(),
            "subscription_status": self.subscription_status,
            "trial_ends_at": self.trial_ends_at.isoformat() if self.trial_ends_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
