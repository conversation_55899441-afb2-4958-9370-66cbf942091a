"""
Workflow model for n8n automation workflows
"""

from typing import Dict, Any
from sqlalchemy import Column, String, DateTime, Boolean, Text, ForeignKey, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Workflow(Base):
    __tablename__ = "workflows"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)

    # Workflow details
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    template_id = Column(String, nullable=True)  # Template used to create this workflow

    # n8n integration
    n8n_workflow_id = Column(String, nullable=True, unique=True, index=True)

    # Status and configuration
    is_active = Column(Boolean, default=False)
    configuration = Column(JSON, default=dict)

    # Execution statistics
    execution_count = Column(Integer, default=0)
    last_execution = Column(DateTime(timezone=True), nullable=True)
    last_execution_status = Column(String, nullable=True)  # success, failed, running

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant", back_populates="workflows")

    def get_configuration(self) -> Dict[str, Any]:
        """Get workflow configuration"""
        return self.configuration or {}

    def update_configuration(self, config: Dict[str, Any]):
        """Update workflow configuration"""
        current_config = self.get_configuration()
        current_config.update(config)
        self.configuration = current_config

    def increment_execution_count(self):
        """Increment execution counter"""
        self.execution_count = (self.execution_count or 0) + 1
        self.last_execution = func.now()

    def set_execution_status(self, status: str):
        """Set last execution status"""
        self.last_execution_status = status
        self.last_execution = func.now()

    def to_dict(self) -> Dict[str, Any]:
        """Convert workflow to dictionary"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "name": self.name,
            "description": self.description,
            "template_id": self.template_id,
            "n8n_workflow_id": self.n8n_workflow_id,
            "is_active": self.is_active,
            "configuration": self.get_configuration(),
            "execution_count": self.execution_count,
            "last_execution": self.last_execution.isoformat() if self.last_execution else None,
            "last_execution_status": self.last_execution_status,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
