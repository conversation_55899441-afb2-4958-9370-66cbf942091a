#!/bin/bash

# AIthentiqMind Production Setup Script
# This script sets up the production environment prerequisites

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Generate secure secrets
generate_secrets() {
    log_info "Generating secure secrets..."
    
    # Generate random passwords and keys
    DB_PASSWORD=$(openssl rand -base64 32)
    REDIS_PASSWORD=$(openssl rand -base64 32)
    SECRET_KEY=$(openssl rand -hex 64)
    MINIO_ACCESS_KEY=$(openssl rand -base64 20 | tr -d "=+/" | cut -c1-20)
    MINIO_SECRET_KEY=$(openssl rand -base64 40 | tr -d "=+/" | cut -c1-40)
    N8N_API_KEY=$(openssl rand -hex 32)
    QDRANT_API_KEY=$(openssl rand -hex 32)
    
    # Create secrets file
    cat > k8s/base/secrets-production.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: aithentiqmind-secrets
  namespace: aithentiqmind
type: Opaque
stringData:
  # Database
  DATABASE_USER: "postgres"
  DATABASE_PASSWORD: "$DB_PASSWORD"
  
  # Redis
  REDIS_PASSWORD: "$REDIS_PASSWORD"
  
  # MinIO
  MINIO_ACCESS_KEY: "$MINIO_ACCESS_KEY"
  MINIO_SECRET_KEY: "$MINIO_SECRET_KEY"
  
  # Application
  SECRET_KEY: "$SECRET_KEY"
  
  # OpenAI (REPLACE WITH YOUR ACTUAL API KEY)
  OPENAI_API_KEY: "REPLACE_WITH_YOUR_OPENAI_API_KEY"
  
  # Auth0 (REPLACE WITH YOUR ACTUAL CREDENTIALS)
  AUTH0_DOMAIN: "your-domain.auth0.com"
  AUTH0_CLIENT_ID: "REPLACE_WITH_YOUR_CLIENT_ID"
  AUTH0_CLIENT_SECRET: "REPLACE_WITH_YOUR_CLIENT_SECRET"
  AUTH0_AUDIENCE: "https://api.yourdomain.com"
  
  # n8n
  N8N_API_KEY: "$N8N_API_KEY"
  
  # Qdrant
  QDRANT_API_KEY: "$QDRANT_API_KEY"
EOF
    
    log_success "Secrets generated and saved to k8s/base/secrets-production.yaml"
    log_warning "IMPORTANT: Update the following values in secrets-production.yaml:"
    log_warning "  - OPENAI_API_KEY"
    log_warning "  - AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET"
    log_warning "  - AUTH0_AUDIENCE (your API domain)"
}

# Setup SSL certificates with cert-manager
setup_ssl() {
    log_info "Setting up SSL certificates with cert-manager..."
    
    # Install cert-manager
    kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml
    
    # Wait for cert-manager to be ready
    log_info "Waiting for cert-manager to be ready..."
    kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
    kubectl wait --for=condition=ready pod -l app=cainjector -n cert-manager --timeout=300s
    kubectl wait --for=condition=ready pod -l app=webhook -n cert-manager --timeout=300s
    
    # Create ClusterIssuer for Let's Encrypt
    cat > cluster-issuer.yaml << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # REPLACE WITH YOUR EMAIL
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
    
    kubectl apply -f cluster-issuer.yaml
    rm cluster-issuer.yaml
    
    log_success "SSL setup completed"
    log_warning "IMPORTANT: Update the email address in the ClusterIssuer"
}

# Setup monitoring with Prometheus and Grafana
setup_monitoring() {
    log_info "Setting up monitoring with Prometheus and Grafana..."
    
    # Add Prometheus Helm repository
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    # Install Prometheus and Grafana
    helm install monitoring prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set grafana.adminPassword=admin123 \
        --set grafana.service.type=LoadBalancer \
        --set prometheus.service.type=LoadBalancer
    
    log_success "Monitoring setup completed"
    log_info "Grafana admin password: admin123"
    log_warning "IMPORTANT: Change the Grafana admin password after first login"
}

# Setup ingress controller
setup_ingress() {
    log_info "Setting up NGINX Ingress Controller..."
    
    # Install NGINX Ingress Controller
    kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.2/deploy/static/provider/cloud/deploy.yaml
    
    # Wait for ingress controller to be ready
    log_info "Waiting for ingress controller to be ready..."
    kubectl wait --namespace ingress-nginx \
        --for=condition=ready pod \
        --selector=app.kubernetes.io/component=controller \
        --timeout=300s
    
    log_success "Ingress controller setup completed"
}

# Setup backup system
setup_backup() {
    log_info "Setting up backup system..."
    
    # Create backup namespace
    kubectl create namespace backup --dry-run=client -o yaml | kubectl apply -f -
    
    # Create backup CronJob for PostgreSQL
    cat > backup-cronjob.yaml << EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: backup
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: postgres-backup
            image: postgres:15-alpine
            command:
            - /bin/bash
            - -c
            - |
              TIMESTAMP=\$(date +%Y%m%d-%H%M%S)
              pg_dump \$DATABASE_URL > /backup/backup-\$TIMESTAMP.sql
              echo "Backup completed: backup-\$TIMESTAMP.sql"
              # Keep only last 7 days of backups
              find /backup -name "backup-*.sql" -mtime +7 -delete
            env:
            - name: DATABASE_URL
              value: "postgresql://postgres:\$(DATABASE_PASSWORD)@postgres-service.aithentiqmind.svc.cluster.local:5432/aithentiqmind"
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: aithentiqmind-secrets
                  key: DATABASE_PASSWORD
                  namespace: aithentiqmind
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: backup-pvc
  namespace: backup
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
EOF
    
    kubectl apply -f backup-cronjob.yaml
    rm backup-cronjob.yaml
    
    log_success "Backup system setup completed"
}

# Setup network policies for security
setup_network_policies() {
    log_info "Setting up network policies..."
    
    cat > network-policies.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: aithentiqmind-network-policy
  namespace: aithentiqmind
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  - from:
    - podSelector: {}
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - podSelector: {}
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
EOF
    
    kubectl apply -f network-policies.yaml
    rm network-policies.yaml
    
    log_success "Network policies setup completed"
}

# Create production configuration
create_production_config() {
    log_info "Creating production configuration..."
    
    # Update configmap for production
    cat > k8s/base/configmap-production.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: aithentiqmind-config
  namespace: aithentiqmind
data:
  # Database
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "aithentiqmind"
  
  # Redis
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # Qdrant
  QDRANT_URL: "http://qdrant-service:6333"
  
  # MinIO
  MINIO_ENDPOINT: "minio-service:9000"
  MINIO_BUCKET: "aithentiqmind-documents"
  
  # n8n
  N8N_BASE_URL: "http://n8n-service:5678"
  
  # Application
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  DEBUG: "false"
  
  # Performance settings
  WORKERS: "4"
  MAX_CONNECTIONS: "100"
  POOL_SIZE: "20"
  MAX_OVERFLOW: "30"
  
  # RAG Configuration
  CHUNK_SIZE: "1000"
  CHUNK_OVERLAP: "200"
  RETRIEVAL_TOP_K: "5"
  MAX_CHUNKS_PER_DOCUMENT: "1000"
  MAX_FILE_SIZE: "52428800"  # 50MB
  
  # Trust Scoring
  CONFIDENCE_WEIGHT: "0.3"
  RELEVANCE_WEIGHT: "0.3"
  RECENCY_WEIGHT: "0.2"
  MIN_SOURCE_DIVERSITY: "2"
  
  # OpenAI
  OPENAI_MODEL: "gpt-4"
  OPENAI_EMBEDDING_MODEL: "text-embedding-ada-002"
  OPENAI_MAX_TOKENS: "4000"
  OPENAI_TEMPERATURE: "0.1"
  
  # Security
  CORS_ORIGINS: "https://app.yourdomain.com,https://yourdomain.com"
  ALLOWED_HOSTS: "api.yourdomain.com,yourdomain.com"
  
  # Rate Limiting
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  
  # File Types
  ALLOWED_FILE_TYPES: "pdf,docx,txt,md,html"
  
  # JWT
  JWT_ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "1440"  # 24 hours
EOF
    
    log_success "Production configuration created"
    log_warning "IMPORTANT: Update domain names in configmap-production.yaml"
}

# Main setup function
main() {
    log_info "Setting up AIthentiqMind production environment"
    echo
    
    case "${1:-all}" in
        "all")
            generate_secrets
            create_production_config
            setup_ingress
            setup_ssl
            setup_monitoring
            setup_backup
            setup_network_policies
            log_success "Production setup completed!"
            echo
            log_info "Next steps:"
            log_info "1. Update secrets in k8s/base/secrets-production.yaml"
            log_info "2. Update domain names in k8s/base/configmap-production.yaml"
            log_info "3. Update email in ClusterIssuer"
            log_info "4. Run: ./scripts/deploy.sh"
            ;;
        "secrets")
            generate_secrets
            ;;
        "ssl")
            setup_ssl
            ;;
        "monitoring")
            setup_monitoring
            ;;
        "ingress")
            setup_ingress
            ;;
        "backup")
            setup_backup
            ;;
        "network")
            setup_network_policies
            ;;
        "config")
            create_production_config
            ;;
        *)
            echo "Usage: $0 [all|secrets|ssl|monitoring|ingress|backup|network|config]"
            echo
            echo "Commands:"
            echo "  all        - Complete production setup (default)"
            echo "  secrets    - Generate secure secrets"
            echo "  ssl        - Setup SSL certificates"
            echo "  monitoring - Setup Prometheus and Grafana"
            echo "  ingress    - Setup NGINX Ingress Controller"
            echo "  backup     - Setup backup system"
            echo "  network    - Setup network policies"
            echo "  config     - Create production configuration"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
