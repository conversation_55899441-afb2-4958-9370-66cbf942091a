"""
Workflow management service
"""

import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.logging import get_logger
from app.core.exceptions import WorkflowError
from app.models.workflow import Workflow
from app.services.workflow.n8n_client import get_n8n_client
from app.services.workflow.templates import WorkflowTemplates

logger = get_logger(__name__)


class WorkflowManager:
    """Workflow management service"""
    
    def __init__(self):
        self.n8n_client = get_n8n_client()
        self.templates = WorkflowTemplates()
    
    async def create_workflow_from_template(
        self,
        tenant_id: str,
        template_id: str,
        name: str,
        configuration: Dict[str, Any],
        db: AsyncSession
    ) -> Workflow:
        """Create workflow from template"""
        try:
            # Get template
            template_info = self.templates.get_template_by_id(template_id)
            if not template_info:
                raise WorkflowError(f"Template not found: {template_id}")
            
            # Customize template with configuration
            workflow_data = template_info["template"].copy()
            workflow_data["name"] = name
            
            # Apply configuration to workflow
            workflow_data = self._apply_configuration(workflow_data, configuration)
            
            # Create workflow in n8n
            n8n_workflow = await self.n8n_client.create_workflow(workflow_data)
            
            # Save workflow to database
            db_workflow = Workflow(
                tenant_id=tenant_id,
                name=name,
                description=template_info["description"],
                n8n_workflow_id=n8n_workflow["id"],
                template_id=template_id,
                configuration=configuration,
                is_active=False
            )
            
            db.add(db_workflow)
            await db.commit()
            await db.refresh(db_workflow)
            
            logger.info(f"Created workflow from template: {template_id}")
            return db_workflow
            
        except Exception as e:
            logger.error(f"Failed to create workflow from template: {e}")
            raise WorkflowError(f"Failed to create workflow: {str(e)}")
    
    async def create_custom_workflow(
        self,
        tenant_id: str,
        name: str,
        description: str,
        workflow_data: Dict[str, Any],
        db: AsyncSession
    ) -> Workflow:
        """Create custom workflow"""
        try:
            # Create workflow in n8n
            n8n_workflow = await self.n8n_client.create_workflow(workflow_data)
            
            # Save workflow to database
            db_workflow = Workflow(
                tenant_id=tenant_id,
                name=name,
                description=description,
                n8n_workflow_id=n8n_workflow["id"],
                configuration=workflow_data,
                is_active=False
            )
            
            db.add(db_workflow)
            await db.commit()
            await db.refresh(db_workflow)
            
            logger.info(f"Created custom workflow: {name}")
            return db_workflow
            
        except Exception as e:
            logger.error(f"Failed to create custom workflow: {e}")
            raise WorkflowError(f"Failed to create workflow: {str(e)}")
    
    async def get_workflows(self, tenant_id: str, db: AsyncSession) -> List[Workflow]:
        """Get all workflows for tenant"""
        try:
            result = await db.execute(
                select(Workflow).where(Workflow.tenant_id == tenant_id)
            )
            workflows = result.scalars().all()
            return list(workflows)
            
        except Exception as e:
            logger.error(f"Failed to get workflows: {e}")
            raise WorkflowError(f"Failed to get workflows: {str(e)}")
    
    async def get_workflow(self, workflow_id: str, tenant_id: str, db: AsyncSession) -> Workflow:
        """Get workflow by ID"""
        try:
            result = await db.execute(
                select(Workflow).where(
                    Workflow.id == workflow_id,
                    Workflow.tenant_id == tenant_id
                )
            )
            workflow = result.scalar_one_or_none()
            
            if not workflow:
                raise WorkflowError(f"Workflow not found: {workflow_id}")
            
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to get workflow: {e}")
            raise WorkflowError(f"Failed to get workflow: {str(e)}")
    
    async def update_workflow(
        self,
        workflow_id: str,
        tenant_id: str,
        updates: Dict[str, Any],
        db: AsyncSession
    ) -> Workflow:
        """Update workflow"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Update n8n workflow if configuration changed
            if "configuration" in updates:
                await self.n8n_client.update_workflow(
                    workflow.n8n_workflow_id,
                    updates["configuration"]
                )
                workflow.configuration = updates["configuration"]
            
            # Update other fields
            for field, value in updates.items():
                if field != "configuration" and hasattr(workflow, field):
                    setattr(workflow, field, value)
            
            await db.commit()
            await db.refresh(workflow)
            
            logger.info(f"Updated workflow: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to update workflow: {e}")
            raise WorkflowError(f"Failed to update workflow: {str(e)}")
    
    async def delete_workflow(
        self,
        workflow_id: str,
        tenant_id: str,
        db: AsyncSession
    ) -> bool:
        """Delete workflow"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Delete from n8n
            await self.n8n_client.delete_workflow(workflow.n8n_workflow_id)
            
            # Delete from database
            await db.delete(workflow)
            await db.commit()
            
            logger.info(f"Deleted workflow: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete workflow: {e}")
            raise WorkflowError(f"Failed to delete workflow: {str(e)}")
    
    async def activate_workflow(
        self,
        workflow_id: str,
        tenant_id: str,
        db: AsyncSession
    ) -> Workflow:
        """Activate workflow"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Activate in n8n
            await self.n8n_client.activate_workflow(workflow.n8n_workflow_id)
            
            # Update database
            workflow.is_active = True
            await db.commit()
            await db.refresh(workflow)
            
            logger.info(f"Activated workflow: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to activate workflow: {e}")
            raise WorkflowError(f"Failed to activate workflow: {str(e)}")
    
    async def deactivate_workflow(
        self,
        workflow_id: str,
        tenant_id: str,
        db: AsyncSession
    ) -> Workflow:
        """Deactivate workflow"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Deactivate in n8n
            await self.n8n_client.deactivate_workflow(workflow.n8n_workflow_id)
            
            # Update database
            workflow.is_active = False
            await db.commit()
            await db.refresh(workflow)
            
            logger.info(f"Deactivated workflow: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to deactivate workflow: {e}")
            raise WorkflowError(f"Failed to deactivate workflow: {str(e)}")
    
    async def execute_workflow(
        self,
        workflow_id: str,
        tenant_id: str,
        input_data: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """Execute workflow manually"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Execute in n8n
            result = await self.n8n_client.execute_workflow(
                workflow.n8n_workflow_id,
                input_data
            )
            
            logger.info(f"Executed workflow: {workflow_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute workflow: {e}")
            raise WorkflowError(f"Failed to execute workflow: {str(e)}")
    
    async def trigger_webhook(
        self,
        webhook_path: str,
        data: Dict[str, Any],
        method: str = "POST"
    ) -> Dict[str, Any]:
        """Trigger webhook workflow"""
        try:
            result = await self.n8n_client.trigger_webhook(webhook_path, data, method)
            logger.info(f"Triggered webhook: {webhook_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to trigger webhook: {e}")
            raise WorkflowError(f"Failed to trigger webhook: {str(e)}")
    
    def _apply_configuration(
        self,
        workflow_data: Dict[str, Any],
        configuration: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Apply configuration to workflow template"""
        # This is a simplified implementation
        # In a real scenario, you'd have more sophisticated template processing
        
        # Replace placeholders in workflow data
        workflow_str = str(workflow_data)
        for key, value in configuration.items():
            placeholder = f"{{{{ {key} }}}}"
            workflow_str = workflow_str.replace(placeholder, str(value))
        
        # Convert back to dict (this is simplified - use proper JSON processing)
        import json
        try:
            return json.loads(workflow_str.replace("'", '"'))
        except:
            return workflow_data
    
    async def get_workflow_executions(
        self,
        workflow_id: str,
        tenant_id: str,
        limit: int = 20,
        db: AsyncSession
    ) -> List[Dict[str, Any]]:
        """Get workflow execution history"""
        try:
            # Get workflow from database
            workflow = await self.get_workflow(workflow_id, tenant_id, db)
            
            # Get executions from n8n
            executions = await self.n8n_client.get_executions(
                workflow.n8n_workflow_id,
                limit
            )
            
            return executions
            
        except Exception as e:
            logger.error(f"Failed to get workflow executions: {e}")
            raise WorkflowError(f"Failed to get executions: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check workflow service health"""
        try:
            n8n_health = await self.n8n_client.health_check()
            
            return {
                "status": "healthy" if n8n_health["status"] == "healthy" else "unhealthy",
                "n8n": n8n_health,
                "templates_available": len(self.templates.get_all_templates())
            }
            
        except Exception as e:
            logger.error(f"Workflow health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }


# Global workflow manager instance
_workflow_manager = None

def get_workflow_manager() -> WorkflowManager:
    """Get or create workflow manager instance"""
    global _workflow_manager
    if _workflow_manager is None:
        _workflow_manager = WorkflowManager()
    return _workflow_manager
