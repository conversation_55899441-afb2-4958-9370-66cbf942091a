# AIthentiqMind Development Guide

This guide will help you set up and develop the AIthentiqMind platform locally.

## Prerequisites

- **Docker Desktop** (latest version)
- **Node.js** 18+ and npm
- **Python** 3.11+
- **Git**
- **PowerShell** (Windows) or **Bash** (Linux/macOS)

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd AIthentiqMind
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# At minimum, set:
# - OPENAI_API_KEY (for AI functionality)
# - AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET (for authentication)
```

### 3. Run Setup Script

**Windows (PowerShell):**
```powershell
.\scripts\setup-dev.ps1
```

**Linux/macOS:**
```bash
make setup
```

### 4. Start Development Environment

```bash
# Start all services
make dev

# Or manually with Docker Compose
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### 5. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **n8n Workflows**: http://localhost:5678
- **Qdrant Vector DB**: http://localhost:6333
- **Grafana Monitoring**: http://localhost:3001

## Development Workflow

### Backend Development

The backend is built with FastAPI and follows a modular architecture:

```
backend/
├── app/
│   ├── api/           # API endpoints
│   ├── core/          # Core configuration and utilities
│   ├── models/        # Database models
│   ├── schemas/       # Pydantic schemas
│   ├── services/      # Business logic
│   └── utils/         # Utility functions
├── tests/             # Test files
└── alembic/           # Database migrations
```

**Key Commands:**
```bash
# Install dependencies
make install

# Run tests
make test

# Run linting
make lint

# Format code
make format

# Database migrations
make migrate

# Create new migration
make migration MSG="description"

# Access backend shell
make shell-backend
```

### Frontend Development

The frontend is built with Next.js 14, TypeScript, and Tailwind CSS:

```
frontend/
├── src/
│   ├── app/           # Next.js app router
│   ├── components/    # React components
│   ├── hooks/         # Custom hooks
│   ├── lib/           # Utility libraries
│   ├── store/         # State management
│   ├── styles/        # Global styles
│   └── types/         # TypeScript types
└── public/            # Static assets
```

**Key Commands:**
```bash
# Install dependencies
cd frontend && npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

### Database Development

The platform uses PostgreSQL with SQLAlchemy ORM:

**Key Commands:**
```bash
# Access database shell
make shell-postgres

# Create backup
make backup

# Restore from backup
make restore-postgres FILE=backup.sql

# View database stats
curl http://localhost:8000/api/v1/admin/db/stats
```

### Vector Database (Qdrant)

Qdrant is used for vector storage and similarity search:

**Access Qdrant:**
- Web UI: http://localhost:6333/dashboard
- API: http://localhost:6333

### Workflow Automation (n8n)

n8n provides visual workflow automation:

**Access n8n:**
- Web UI: http://localhost:5678
- Credentials: admin / admin123

## Architecture Overview

### Multi-Tenant Architecture

The platform implements tenant isolation at multiple levels:

1. **Database Level**: Tenant-specific schemas
2. **Vector Database**: Tenant-specific collections
3. **File Storage**: Tenant-specific buckets/folders
4. **API Level**: Tenant context in all requests

### Key Components

1. **RAG Engine**: LangChain-based document processing and query answering
2. **Vector Store**: Qdrant for semantic search
3. **Workflow Engine**: n8n for automation
4. **Authentication**: Auth0 for SSO and user management
5. **Monitoring**: Prometheus + Grafana for observability

## Testing

### Backend Testing

```bash
# Run all tests
make test

# Run unit tests only
make test-unit

# Run integration tests only
make test-int

# Run with coverage
pytest --cov=app tests/
```

### Frontend Testing

```bash
# Run all tests
npm test

# Run in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage
```

## Debugging

### Backend Debugging

1. **Logs**: Check container logs with `make logs-backend`
2. **Database**: Use `make shell-postgres` to inspect data
3. **API**: Use the interactive docs at http://localhost:8000/docs

### Frontend Debugging

1. **Browser DevTools**: Use React DevTools extension
2. **Network**: Monitor API calls in Network tab
3. **Console**: Check for JavaScript errors

### Common Issues

1. **Port Conflicts**: Ensure ports 3000, 8000, 5432, 6379, 6333, 5678 are available
2. **Docker Issues**: Run `docker system prune` to clean up
3. **Permission Issues**: Ensure Docker has proper permissions

## Production Deployment

### Environment Variables

Update `.env` for production:

```bash
# Security
DEBUG=false
SECRET_KEY=<strong-secret-key>

# Database
DATABASE_URL=<production-database-url>

# External Services
OPENAI_API_KEY=<production-api-key>
AUTH0_DOMAIN=<production-auth0-domain>
```

### Docker Production

```bash
# Build and start production environment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Or use make command
make prod
```

## Contributing

1. **Code Style**: Follow the established patterns
2. **Testing**: Write tests for new features
3. **Documentation**: Update docs for significant changes
4. **Commits**: Use conventional commit messages

## Monitoring and Observability

### Metrics

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin123)

### Logging

- **Structured Logging**: All services use structured JSON logging
- **Log Aggregation**: Logs are collected and can be viewed with `make logs`

### Health Checks

- **Backend Health**: http://localhost:8000/health
- **Frontend Health**: http://localhost:3000/api/health
- **Database Health**: Included in backend health check

## Troubleshooting

### Service Won't Start

1. Check Docker is running
2. Verify port availability
3. Check environment variables
4. Review service logs

### Database Connection Issues

1. Verify PostgreSQL is running
2. Check connection string
3. Ensure database exists
4. Check network connectivity

### Authentication Issues

1. Verify Auth0 configuration
2. Check redirect URLs
3. Ensure proper environment variables

For more help, check the logs with `make logs` or specific service logs with `make logs-<service>`.
