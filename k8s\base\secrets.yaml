apiVersion: v1
kind: Secret
metadata:
  name: aithentiqmind-secrets
  namespace: aithentiqmind
type: Opaque
stringData:
  # Database
  DATABASE_USER: "postgres"
  DATABASE_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
  # Redis
  REDIS_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  
  # MinIO
  MINIO_ACCESS_KEY: "CHANGE_ME_IN_PRODUCTION"
  MINIO_SECRET_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # Application
  SECRET_KEY: "CHANGE_ME_IN_PRODUCTION_VERY_LONG_SECRET_KEY"
  
  # OpenAI
  OPENAI_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # Auth0
  AUTH0_DOMAIN: "your-domain.auth0.com"
  AUTH0_CLIENT_ID: "CHANGE_ME_IN_PRODUCTION"
  AUTH0_CLIENT_SECRET: "CHANGE_ME_IN_PRODUCTION"
  AUTH0_AUDIENCE: "https://api.aithentiqmind.com"
  
  # n8n
  N8N_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # Qdrant
  QDRANT_API_KEY: "CHANGE_ME_IN_PRODUCTION"
