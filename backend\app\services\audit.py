"""
Audit logging service for tracking user actions and security events
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from fastapi import Request

from app.core.logging import get_logger
from app.db.database import get_db
from app.models.audit import AuditLog
from app.models.user import User
from app.models.tenant import Tenant

logger = get_logger(__name__)


class AuditService:
    """Service for audit logging and compliance tracking"""
    
    def __init__(self):
        self.logger = get_logger("audit")
    
    async def log_action(
        self,
        tenant_id: str,
        user_id: Optional[str],
        action: str,
        resource: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> bool:
        """Log an audit event"""
        try:
            if not db:
                async for session in get_db():
                    db = session
                    break
            
            audit_log = AuditLog(
                tenant_id=tenant_id,
                user_id=user_id,
                action=action,
                resource=resource,
                resource_id=resource_id,
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(audit_log)
            await db.commit()
            
            # Also log to structured logger
            self.logger.info(
                f"AUDIT: {action} on {resource}",
                extra={
                    "tenant_id": tenant_id,
                    "user_id": user_id,
                    "action": action,
                    "resource": resource,
                    "resource_id": resource_id,
                    "ip_address": ip_address,
                    "details": details
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
            return False
    
    async def log_authentication_event(
        self,
        user_id: Optional[str],
        tenant_id: Optional[str],
        event_type: str,  # login_success, login_failure, logout, token_refresh
        details: Dict[str, Any],
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> bool:
        """Log authentication-related events"""
        return await self.log_action(
            tenant_id=tenant_id or "system",
            user_id=user_id,
            action=f"auth.{event_type}",
            resource="authentication",
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            db=db
        )
    
    async def log_security_event(
        self,
        event_type: str,  # suspicious_activity, rate_limit_exceeded, unauthorized_access
        details: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> bool:
        """Log security-related events"""
        return await self.log_action(
            tenant_id=tenant_id or "system",
            user_id=user_id,
            action=f"security.{event_type}",
            resource="security",
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            db=db
        )
    
    async def log_data_access(
        self,
        tenant_id: str,
        user_id: str,
        resource_type: str,  # document, query, user, etc.
        resource_id: str,
        action: str,  # read, create, update, delete
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        db: AsyncSession = None
    ) -> bool:
        """Log data access events for compliance"""
        return await self.log_action(
            tenant_id=tenant_id,
            user_id=user_id,
            action=f"data.{action}",
            resource=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            db=db
        )
    
    async def get_audit_logs(
        self,
        tenant_id: str,
        user_id: Optional[str] = None,
        action: Optional[str] = None,
        resource: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0,
        db: AsyncSession = None
    ) -> List[AuditLog]:
        """Retrieve audit logs with filtering"""
        try:
            if not db:
                async for session in get_db():
                    db = session
                    break
            
            query = select(AuditLog).where(AuditLog.tenant_id == tenant_id)
            
            if user_id:
                query = query.where(AuditLog.user_id == user_id)
            
            if action:
                query = query.where(AuditLog.action.like(f"%{action}%"))
            
            if resource:
                query = query.where(AuditLog.resource == resource)
            
            if start_date:
                query = query.where(AuditLog.created_at >= start_date)
            
            if end_date:
                query = query.where(AuditLog.created_at <= end_date)
            
            query = query.order_by(desc(AuditLog.created_at)).limit(limit).offset(offset)
            
            result = await db.execute(query)
            return list(result.scalars().all())
            
        except Exception as e:
            logger.error(f"Failed to retrieve audit logs: {e}")
            return []
    
    async def get_security_summary(
        self,
        tenant_id: str,
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get security summary for the last N days"""
        try:
            if not db:
                async for session in get_db():
                    db = session
                    break
            
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Get security events
            security_query = select(AuditLog).where(
                AuditLog.tenant_id == tenant_id,
                AuditLog.action.like("security.%"),
                AuditLog.created_at >= start_date
            )
            
            security_result = await db.execute(security_query)
            security_events = list(security_result.scalars().all())
            
            # Get authentication events
            auth_query = select(AuditLog).where(
                AuditLog.tenant_id == tenant_id,
                AuditLog.action.like("auth.%"),
                AuditLog.created_at >= start_date
            )
            
            auth_result = await db.execute(auth_query)
            auth_events = list(auth_result.scalars().all())
            
            # Analyze events
            failed_logins = len([e for e in auth_events if e.action == "auth.login_failure"])
            successful_logins = len([e for e in auth_events if e.action == "auth.login_success"])
            
            # Get unique IP addresses
            unique_ips = set()
            for event in security_events + auth_events:
                if event.ip_address:
                    unique_ips.add(event.ip_address)
            
            return {
                "period_days": days,
                "security_events": len(security_events),
                "failed_logins": failed_logins,
                "successful_logins": successful_logins,
                "unique_ip_addresses": len(unique_ips),
                "login_success_rate": round(
                    (successful_logins / (successful_logins + failed_logins)) * 100, 2
                ) if (successful_logins + failed_logins) > 0 else 0,
                "recent_security_events": [
                    {
                        "action": event.action,
                        "created_at": event.created_at.isoformat(),
                        "ip_address": event.ip_address,
                        "details": event.details
                    }
                    for event in security_events[-10:]  # Last 10 events
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get security summary: {e}")
            return {"error": str(e)}


class AuditMiddleware:
    """Middleware for automatic audit logging"""
    
    def __init__(self, audit_service: AuditService):
        self.audit_service = audit_service
    
    async def log_request(
        self,
        request: Request,
        user: Optional[User] = None,
        tenant: Optional[Tenant] = None
    ):
        """Log HTTP request for audit purposes"""
        try:
            # Extract request details
            method = request.method
            path = str(request.url.path)
            ip_address = self._get_client_ip(request)
            user_agent = request.headers.get("user-agent")
            
            # Determine action and resource from path
            action, resource = self._parse_request_path(method, path)
            
            if action and resource:
                await self.audit_service.log_action(
                    tenant_id=str(tenant.id) if tenant else "system",
                    user_id=str(user.id) if user else None,
                    action=action,
                    resource=resource,
                    details={
                        "method": method,
                        "path": path,
                        "query_params": dict(request.query_params)
                    },
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
        except Exception as e:
            logger.error(f"Failed to log request audit: {e}")
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Extract client IP address from request"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return None
    
    def _parse_request_path(self, method: str, path: str) -> tuple:
        """Parse request path to determine action and resource"""
        # Simple mapping - can be enhanced based on your API structure
        path_parts = path.strip("/").split("/")
        
        if len(path_parts) < 2:
            return None, None
        
        # Skip API version prefix
        if path_parts[0] in ["api", "v1"]:
            path_parts = path_parts[1:]
        
        if not path_parts:
            return None, None
        
        resource = path_parts[0]
        
        # Map HTTP methods to actions
        action_map = {
            "GET": "read",
            "POST": "create",
            "PUT": "update",
            "PATCH": "update",
            "DELETE": "delete"
        }
        
        action = action_map.get(method, "access")
        
        return f"api.{action}", resource


# Global audit service instance
audit_service = AuditService()
audit_middleware = AuditMiddleware(audit_service)


def get_audit_service() -> AuditService:
    """Get audit service instance"""
    return audit_service


def get_audit_middleware() -> AuditMiddleware:
    """Get audit middleware instance"""
    return audit_middleware
