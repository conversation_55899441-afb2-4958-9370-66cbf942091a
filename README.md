# AIthentiqMind - Enterprise RAG SaaS Platform

A full-stack, enterprise-ready SaaS platform that combines n8n workflow orchestration with RAGFlow (Retrieval-Augmented Generation) for intelligent document-based answers.

## 🏗️ Architecture Overview

- **Frontend**: Next.js (TypeScript), Tailwind CSS, shadcn/ui, React Query
- **Backend API**: FastAPI (Python) with multi-tenant architecture
- **RAG Engine**: LangChain with OpenAI/Ollama LLMs
- **Vector DB**: Qdrant (self-hosted) with tenant isolation
- **Automation**: n8n (self-hosted) for workflow orchestration
- **Storage**: PostgreSQL, Redis, S3-compatible (MinIO)
- **Authentication**: Auth0 with OAuth + SSO support
- **Deployment**: Docker + Docker Compose, Kubernetes ready
- **Monitoring**: Sentry + Prometheus + Grafana

## 🚀 Quick Start

```bash
# Clone and setup
git clone <repository-url>
cd AIthentiqMind

# Start all services
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# n8n: http://localhost:5678
# Qdrant: http://localhost:6333
```

## 📁 Project Structure

```
AIthentiqMind/
├── frontend/                 # Next.js application
├── backend/                  # FastAPI application
├── rag-engine/              # LangChain RAG implementation
├── n8n-workflows/           # n8n workflow templates
├── infrastructure/          # Docker, K8s, monitoring configs
├── docs/                    # Documentation
├── tests/                   # Integration tests
└── scripts/                 # Deployment and utility scripts
```

## 🔧 Development Setup

See [Development Guide](docs/development.md) for detailed setup instructions.

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Architecture Guide](docs/architecture.md)
- [Deployment Guide](docs/deployment.md)
- [Security Guide](docs/security.md)

## 🔐 Enterprise Features

- Multi-tenant SaaS architecture with data isolation
- Role-based access control (RBAC) and audit logging
- SSO integration (SAML, OIDC)
- Trust scoring and source traceability
- Workflow automation and custom templates
- Real-time analytics and monitoring

## 📄 License

[License details to be added]
