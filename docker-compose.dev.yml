version: '3.8'

services:
  # Development overrides for backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    environment:
      DEBUG: true
      LOG_LEVEL: DEBUG
      RELOAD: true
    volumes:
      - ./backend:/app
      - backend_dev_cache:/app/.cache
    command: ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # Development overrides for frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    environment:
      NODE_ENV: development
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    command: ["npm", "run", "dev"]

  # Development database with exposed ports
  postgres:
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: aithentiq_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_password

  # Development Redis with exposed ports
  redis:
    ports:
      - "6379:6379"

  # Development Qdrant with exposed ports
  qdrant:
    ports:
      - "6333:6333"
      - "6334:6334"

  # Development MinIO with exposed ports
  minio:
    ports:
      - "9000:9000"
      - "9001:9001"

  # Development n8n with exposed ports
  n8n:
    ports:
      - "5678:5678"
    environment:
      N8N_LOG_LEVEL: debug
      N8N_LOG_OUTPUT: console

  # Prometheus for monitoring (development)
  prometheus:
    image: prom/prometheus:latest
    container_name: aithentiq_prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - aithentiq_network

  # Grafana for visualization (development)
  grafana:
    image: grafana/grafana:latest
    container_name: aithentiq_grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    networks:
      - aithentiq_network
    depends_on:
      - prometheus

  # Mailhog for email testing (development)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: aithentiq_mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - aithentiq_network

volumes:
  backend_dev_cache:
  prometheus_data:
  grafana_data:
