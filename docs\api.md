# AIthentiqMind API Documentation

## Overview

The AIthentiqMind API is a RESTful API built with FastAPI that provides enterprise-grade RAG (Retrieval-Augmented Generation) capabilities with workflow orchestration.

**Base URL**: `http://localhost:8000/api/v1`

**Authentication**: Bearer token (JWT) via Auth0

## Authentication

### Login
```http
GET /api/auth/login
```
Redirects to Auth0 login page.

### Logout
```http
GET /api/auth/logout
```
Logs out the current user.

### User Profile
```http
GET /api/v1/auth/me
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "full_name": "<PERSON>",
  "tenant_id": "uuid",
  "roles": ["user"],
  "is_active": true
}
```

## Health Checks

### Basic Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "service": "AIthentiqMind",
  "version": "1.0.0",
  "timestamp": **********.789
}
```

### Detailed Health Check
```http
GET /api/v1/health/detailed
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": "healthy",
  "service": "AIthentiqMind",
  "version": "1.0.0",
  "timestamp": **********.789,
  "checks": {
    "database": {
      "status": "healthy",
      "version": "PostgreSQL 15.0"
    },
    "redis": {
      "status": "healthy",
      "message": "Redis connection OK"
    },
    "vector_db": {
      "status": "healthy",
      "message": "Qdrant connection OK"
    }
  }
}
```

## Tenants

### Get Current Tenant
```http
GET /api/v1/tenants/current
Authorization: Bearer <token>
```

### Update Tenant Settings
```http
PUT /api/v1/tenants/current
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Tenant Name",
  "settings": {
    "max_documents": 10000,
    "ai_model": "gpt-4"
  }
}
```

## Documents

### Upload Document
```http
POST /api/v1/documents/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data

file: <binary-file>
title: "Document Title" (optional)
```

**Response:**
```json
{
  "id": "uuid",
  "title": "Document Title",
  "file_type": "pdf",
  "file_size": 1024000,
  "status": "processing",
  "created_at": "2024-01-01T00:00:00Z"
}
```

### List Documents
```http
GET /api/v1/documents?page=1&size=20&status=processed
Authorization: Bearer <token>
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `size`: Page size (default: 20, max: 100)
- `status`: Filter by status (processing, processed, failed)
- `search`: Search in title and content

**Response:**
```json
{
  "items": [
    {
      "id": "uuid",
      "title": "Document Title",
      "file_type": "pdf",
      "file_size": 1024000,
      "status": "processed",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:05:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "size": 20,
  "pages": 5
}
```

### Get Document
```http
GET /api/v1/documents/{document_id}
Authorization: Bearer <token>
```

### Delete Document
```http
DELETE /api/v1/documents/{document_id}
Authorization: Bearer <token>
```

## Queries (RAG)

### Ask Question
```http
POST /api/v1/queries/ask
Authorization: Bearer <token>
Content-Type: application/json

{
  "question": "What is the main topic of the uploaded documents?",
  "context_limit": 5,
  "include_sources": true
}
```

**Response:**
```json
{
  "id": "uuid",
  "question": "What is the main topic of the uploaded documents?",
  "answer": "Based on the uploaded documents, the main topic appears to be...",
  "trust_score": 0.85,
  "sources": [
    {
      "document_id": "uuid",
      "document_title": "Document Title",
      "chunk_text": "Relevant text excerpt...",
      "relevance_score": 0.92
    }
  ],
  "response_time": 2.34,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### Get Query History
```http
GET /api/v1/queries?page=1&size=20
Authorization: Bearer <token>
```

### Get Query Details
```http
GET /api/v1/queries/{query_id}
Authorization: Bearer <token>
```

## Workflows

### List Workflows
```http
GET /api/v1/workflows
Authorization: Bearer <token>
```

### Create Workflow
```http
POST /api/v1/workflows
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Document Processing Workflow",
  "description": "Automatically process new documents",
  "trigger": "document_uploaded",
  "actions": [
    {
      "type": "email_notification",
      "config": {
        "to": "<EMAIL>",
        "subject": "New document uploaded"
      }
    }
  ]
}
```

### Trigger Workflow
```http
POST /api/v1/workflows/{workflow_id}/trigger
Authorization: Bearer <token>
Content-Type: application/json

{
  "data": {
    "document_id": "uuid",
    "custom_field": "value"
  }
}
```

## Analytics

### Get Dashboard Stats
```http
GET /api/v1/analytics/dashboard
Authorization: Bearer <token>
```

**Response:**
```json
{
  "total_documents": 150,
  "total_queries": 1250,
  "avg_trust_score": 0.82,
  "avg_response_time": 1.45,
  "documents_this_month": 25,
  "queries_this_month": 180,
  "top_queries": [
    {
      "question": "What is our refund policy?",
      "count": 45
    }
  ]
}
```

### Get Usage Analytics
```http
GET /api/v1/analytics/usage?period=30d
Authorization: Bearer <token>
```

**Query Parameters:**
- `period`: Time period (7d, 30d, 90d, 1y)

## Admin Endpoints

### Get System Stats
```http
GET /api/v1/admin/stats
Authorization: Bearer <admin-token>
```

### Get All Tenants
```http
GET /api/v1/admin/tenants
Authorization: Bearer <admin-token>
```

### Create Tenant
```http
POST /api/v1/admin/tenants
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "name": "New Tenant",
  "domain": "newtenant.com",
  "settings": {
    "max_documents": 5000,
    "max_users": 25
  }
}
```

## Error Responses

All error responses follow this format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Additional error details"
    }
  }
}
```

### Common Error Codes

- `AUTHENTICATION_ERROR` (401): Invalid or missing authentication
- `AUTHORIZATION_ERROR` (403): Insufficient permissions
- `VALIDATION_ERROR` (422): Request validation failed
- `NOT_FOUND` (404): Resource not found
- `RATE_LIMIT_ERROR` (429): Rate limit exceeded
- `INTERNAL_SERVER_ERROR` (500): Server error

## Rate Limiting

API endpoints are rate limited:
- **Default**: 100 requests per hour per user
- **Upload endpoints**: 10 requests per hour per user
- **Query endpoints**: 50 requests per hour per user

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Webhooks

### Document Processing Complete
```json
{
  "event": "document.processed",
  "data": {
    "document_id": "uuid",
    "tenant_id": "uuid",
    "status": "processed",
    "chunks_created": 25
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Query Completed
```json
{
  "event": "query.completed",
  "data": {
    "query_id": "uuid",
    "tenant_id": "uuid",
    "trust_score": 0.85,
    "response_time": 2.34
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## SDK Examples

### Python
```python
import requests

# Authentication
headers = {"Authorization": "Bearer <token>"}

# Upload document
with open("document.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8000/api/v1/documents/upload",
        headers=headers,
        files={"file": f}
    )

# Ask question
response = requests.post(
    "http://localhost:8000/api/v1/queries/ask",
    headers=headers,
    json={"question": "What is this document about?"}
)
```

### JavaScript
```javascript
// Upload document
const formData = new FormData();
formData.append('file', fileInput.files[0]);

const response = await fetch('/api/v1/documents/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Ask question
const queryResponse = await fetch('/api/v1/queries/ask', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    question: 'What is this document about?'
  })
});
```
