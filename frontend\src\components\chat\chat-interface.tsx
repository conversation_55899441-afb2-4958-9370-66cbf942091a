'use client'

import { useState, useRef, useEffect } from 'react'
import { Send, Upload, FileText, Loader2, AlertCircle, ChevronDown, ChevronUp, ExternalLink, Copy } from 'lucide-react'
import { useDropzone } from 'react-dropzone'

import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  sources?: Source[]
  trustScore?: number
  isStreaming?: boolean
}

interface Source {
  documentId: string
  chunkId: string
  content: string
  relevanceScore: number
  metadata: {
    filename?: string
    pageNumber?: number
  }
}

interface ChatInterfaceProps {
  className?: string
}

interface SourceCitationsProps {
  sources: Source[]
}

function SourceCitations({ sources }: SourceCitationsProps) {
  const [expandedSources, setExpandedSources] = useState<Set<number>>(new Set())

  const toggleSource = (index: number) => {
    const newExpanded = new Set(expandedSources)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSources(newExpanded)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="mt-3 space-y-2">
      <Separator />
      <div className="text-xs font-medium text-muted-foreground flex items-center gap-2">
        <FileText className="h-3 w-3" />
        Sources ({sources.length}):
      </div>
      {sources.map((source, index) => {
        const isExpanded = expandedSources.has(index)
        return (
          <div key={index} className="text-xs bg-muted/50 rounded-lg border border-muted">
            <div
              className="p-3 cursor-pointer hover:bg-muted/70 transition-colors"
              onClick={() => toggleSource(index)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-foreground flex items-center gap-2">
                    <FileText className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate">
                      {source.metadata.filename || 'Unknown Document'}
                    </span>
                    {source.metadata.pageNumber && (
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        Page {source.metadata.pageNumber}
                      </Badge>
                    )}
                  </div>
                  <div className="text-muted-foreground mt-1 line-clamp-2">
                    {isExpanded ? source.content : `${source.content.substring(0, 120)}...`}
                  </div>
                  <div className="flex items-center gap-3 mt-2">
                    <Badge
                      variant="outline"
                      className={cn(
                        "text-xs px-2 py-0",
                        source.relevanceScore >= 0.8 ? "bg-green-50 text-green-700 border-green-200" :
                        source.relevanceScore >= 0.6 ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                        "bg-red-50 text-red-700 border-red-200"
                      )}
                    >
                      {(source.relevanceScore * 100).toFixed(0)}% relevant
                    </Badge>
                    <span className="text-muted-foreground">
                      Chunk {source.chunkId}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation()
                      copyToClipboard(source.content)
                    }}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              </div>
            </div>

            {isExpanded && (
              <div className="px-3 pb-3 border-t border-muted">
                <div className="mt-2 p-2 bg-background rounded text-xs">
                  <div className="whitespace-pre-wrap">{source.content}</div>
                </div>
                <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                  <span>Document ID: {source.documentId}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 text-xs"
                    onClick={() => copyToClipboard(source.content)}
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy
                  </Button>
                </div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export function ChatInterface({ className }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'system',
      content: 'Hello! I\'m your AI assistant. Upload documents and ask me questions about them.',
      timestamp: new Date(),
    }
  ])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'text/html': ['.html']
    },
    maxSize: 50 * 1024 * 1024, // 50MB
    onDrop: (acceptedFiles) => {
      setUploadedFiles(prev => [...prev, ...acceptedFiles])
      handleFileUpload(acceptedFiles)
    }
  })

  const handleFileUpload = async (files: File[]) => {
    for (const file of files) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('title', file.name)

        const response = await fetch('/api/proxy/documents/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`Upload failed: ${response.statusText}`)
        }

        const result = await response.json()
        
        // Add system message about successful upload
        const uploadMessage: Message = {
          id: Date.now().toString(),
          type: 'system',
          content: `✅ Document "${file.name}" uploaded successfully and is being processed.`,
          timestamp: new Date(),
        }
        
        setMessages(prev => [...prev, uploadMessage])
        
      } catch (error) {
        console.error('Upload error:', error)
        
        const errorMessage: Message = {
          id: Date.now().toString(),
          type: 'system',
          content: `❌ Failed to upload "${file.name}": ${error instanceof Error ? error.message : 'Unknown error'}`,
          timestamp: new Date(),
        }
        
        setMessages(prev => [...prev, errorMessage])
      }
    }
  }

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: input.trim(),
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    const question = input.trim()
    setInput('')
    setIsLoading(true)

    // Create streaming assistant message
    const assistantMessageId = Date.now().toString()
    const streamingMessage: Message = {
      id: assistantMessageId,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true,
    }

    setMessages(prev => [...prev, streamingMessage])

    try {
      const response = await fetch('/api/proxy/queries/ask-stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question,
          include_sources: true,
          top_k: 5,
        }),
      })

      if (!response.ok) {
        throw new Error(`Query failed: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let accumulatedContent = ''
      let sources: Source[] = []
      let trustScore: number | undefined

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.type === 'content') {
                  accumulatedContent += data.content
                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                      ? { ...msg, content: accumulatedContent }
                      : msg
                  ))
                } else if (data.type === 'sources') {
                  sources = data.sources
                } else if (data.type === 'trust_score') {
                  trustScore = data.trust_score
                } else if (data.type === 'complete') {
                  // Final update with all data
                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                      ? {
                          ...msg,
                          content: accumulatedContent,
                          sources: sources,
                          trustScore: trustScore,
                          isStreaming: false
                        }
                      : msg
                  ))
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e)
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('Query error:', error)

      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessageId
          ? {
              ...msg,
              content: `I encountered an error while processing your question: ${error instanceof Error ? error.message : 'Unknown error'}`,
              isStreaming: false
            }
          : msg
      ))
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getTrustScoreColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-100 text-green-800 border-green-200'
    if (score >= 0.6) return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    return 'bg-red-100 text-red-800 border-red-200'
  }

  const getTrustScoreLabel = (score: number) => {
    if (score >= 0.8) return 'High Confidence'
    if (score >= 0.6) return 'Medium Confidence'
    return 'Low Confidence'
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* File Upload Area */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Document Upload</CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={cn(
              'file-upload-area cursor-pointer',
              isDragActive && 'dragover'
            )}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center space-y-2">
              <Upload className="h-8 w-8 text-muted-foreground" />
              <div className="text-center">
                <p className="text-sm font-medium">
                  {isDragActive ? 'Drop files here' : 'Upload documents'}
                </p>
                <p className="text-xs text-muted-foreground">
                  PDF, DOCX, TXT, MD, HTML (max 50MB)
                </p>
              </div>
            </div>
          </div>
          
          {uploadedFiles.length > 0 && (
            <div className="mt-3 space-y-1">
              {uploadedFiles.map((file, index) => (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <FileText className="h-3 w-3" />
                  <span className="truncate">{file.name}</span>
                  <span className="text-muted-foreground">
                    ({(file.size / 1024 / 1024).toFixed(1)} MB)
                  </span>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Messages */}
      <ScrollArea className="flex-1 pr-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={cn(
                'flex',
                message.type === 'user' ? 'justify-end' : 'justify-start'
              )}
            >
              <div
                className={cn(
                  'max-w-[80%] rounded-lg px-4 py-2',
                  message.type === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : message.type === 'system'
                    ? 'bg-muted text-muted-foreground'
                    : 'bg-card border'
                )}
              >
                <div className="whitespace-pre-wrap text-sm">
                  {message.content}
                </div>
                
                {message.trustScore !== undefined && (
                  <div className="mt-2 flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className={cn('text-xs', getTrustScoreColor(message.trustScore))}
                    >
                      {getTrustScoreLabel(message.trustScore)} ({(message.trustScore * 100).toFixed(0)}%)
                    </Badge>
                  </div>
                )}
                
                {message.sources && message.sources.length > 0 && (
                  <SourceCitations sources={message.sources} />
                )}
                
                <div className="text-xs text-muted-foreground mt-2">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-card border rounded-lg px-4 py-2 max-w-[80%]">
                <TypingIndicator />
              </div>
            </div>
          )}
        </div>
        <div ref={messagesEndRef} />
      </ScrollArea>

      {/* Input */}
      <div className="mt-4 flex space-x-2">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Ask a question about your documents..."
          disabled={isLoading}
          className="flex-1"
        />
        <Button
          onClick={handleSendMessage}
          disabled={!input.trim() || isLoading}
          size="icon"
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  )
}

function TypingIndicator() {
  const [dots, setDots] = useState('')

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="flex items-center space-x-2">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
        <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
      </div>
      <span className="text-sm text-muted-foreground">
        AI is thinking{dots}
      </span>
    </div>
  )
}
