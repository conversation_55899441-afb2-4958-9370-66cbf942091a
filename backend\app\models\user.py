"""
User model with roles and permissions
"""

from typing import List, Dict, Any
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, JSON, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String, nullable=False)
    auth0_user_id = Column(String, unique=True, nullable=True, index=True)
    profile_picture = Column(String, nullable=True)

    # Status and permissions
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    is_tenant_admin = Column(Boolean, default=False)

    # Role-based access control
    roles = Column(JSON, default=list)  # List of role names
    permissions = Column(JSON, default=list)  # List of permission names

    # User preferences
    preferences = Column(JSON, default=dict)

    # Audit fields
    last_login = Column(DateTime(timezone=True), nullable=True)
    login_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    documents = relationship("Document", back_populates="user")
    queries = relationship("Query", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")

    def get_roles(self) -> List[str]:
        """Get user roles"""
        return self.roles or []

    def get_permissions(self) -> List[str]:
        """Get user permissions (from roles and direct permissions)"""
        all_permissions = set(self.permissions or [])

        # Add permissions from roles
        role_permissions = self._get_role_permissions()
        all_permissions.update(role_permissions)

        # Add admin permissions
        if self.is_admin:
            all_permissions.update(self._get_admin_permissions())

        if self.is_tenant_admin:
            all_permissions.update(self._get_tenant_admin_permissions())

        return list(all_permissions)

    def _get_role_permissions(self) -> List[str]:
        """Get permissions from user roles"""
        role_permission_map = {
            "viewer": [
                "documents:read",
                "queries:create",
                "queries:read_own"
            ],
            "editor": [
                "documents:read",
                "documents:create",
                "documents:update_own",
                "documents:delete_own",
                "queries:create",
                "queries:read_own",
                "workflows:read"
            ],
            "manager": [
                "documents:read",
                "documents:create",
                "documents:update",
                "documents:delete",
                "queries:create",
                "queries:read",
                "workflows:read",
                "workflows:create",
                "workflows:update",
                "analytics:read"
            ]
        }

        permissions = []
        for role in self.get_roles():
            permissions.extend(role_permission_map.get(role, []))

        return permissions

    def _get_admin_permissions(self) -> List[str]:
        """Get admin permissions"""
        return [
            "admin:*",
            "tenants:*",
            "users:*",
            "documents:*",
            "queries:*",
            "workflows:*",
            "analytics:*",
            "system:*"
        ]

    def _get_tenant_admin_permissions(self) -> List[str]:
        """Get tenant admin permissions"""
        return [
            "tenant:manage",
            "users:manage",
            "documents:manage",
            "workflows:manage",
            "analytics:read",
            "settings:manage"
        ]

    def has_permission(self, permission: str) -> bool:
        """Check if user has specific permission"""
        user_permissions = self.get_permissions()

        # Check for exact match
        if permission in user_permissions:
            return True

        # Check for wildcard permissions
        for perm in user_permissions:
            if perm.endswith(":*"):
                prefix = perm[:-1]  # Remove the *
                if permission.startswith(prefix):
                    return True

        return False

    def add_role(self, role: str):
        """Add role to user"""
        current_roles = self.get_roles()
        if role not in current_roles:
            current_roles.append(role)
            self.roles = current_roles

    def remove_role(self, role: str):
        """Remove role from user"""
        current_roles = self.get_roles()
        if role in current_roles:
            current_roles.remove(role)
            self.roles = current_roles

    def add_permission(self, permission: str):
        """Add direct permission to user"""
        current_permissions = self.permissions or []
        if permission not in current_permissions:
            current_permissions.append(permission)
            self.permissions = current_permissions

    def remove_permission(self, permission: str):
        """Remove direct permission from user"""
        current_permissions = self.permissions or []
        if permission in current_permissions:
            current_permissions.remove(permission)
            self.permissions = current_permissions

    def update_last_login(self):
        """Update last login timestamp and increment login count"""
        from datetime import datetime
        self.last_login = datetime.utcnow()
        self.login_count = (self.login_count or 0) + 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert user to dictionary"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "email": self.email,
            "full_name": self.full_name,
            "profile_picture": self.profile_picture,
            "is_active": self.is_active,
            "is_admin": self.is_admin,
            "is_tenant_admin": self.is_tenant_admin,
            "roles": self.get_roles(),
            "permissions": self.get_permissions(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "login_count": self.login_count,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
