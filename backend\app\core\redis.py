"""
Redis configuration and connection management
"""

import asyncio
import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import datetime, timedelta
import redis.asyncio as redis
from redis.asyncio import ConnectionPool

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class RedisManager:
    """Redis connection and operation manager"""
    
    def __init__(self):
        self.pool = None
        self.client = None
        self._connected = False
    
    async def connect(self):
        """Initialize Redis connection pool"""
        try:
            # Create connection pool
            self.pool = ConnectionPool.from_url(
                str(settings.REDIS_URL),
                max_connections=20,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
                encoding='utf-8',
                decode_responses=True
            )
            
            # Create Redis client
            self.client = redis.Redis(connection_pool=self.pool)
            
            # Test connection
            await self.client.ping()
            self._connected = True
            
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    async def disconnect(self):
        """Close Redis connections"""
        if self.client:
            await self.client.close()
        if self.pool:
            await self.pool.disconnect()
        
        self._connected = False
        logger.info("Redis connection closed")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check"""
        try:
            if not self._connected:
                return {"status": "disconnected"}
            
            # Test basic operations
            start_time = datetime.now()
            await self.client.ping()
            ping_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Get Redis info
            info = await self.client.info()
            
            return {
                "status": "healthy",
                "ping_ms": round(ping_time, 2),
                "version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory": info.get("used_memory_human"),
                "used_memory_peak": info.get("used_memory_peak_human"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
                "total_commands_processed": info.get("total_commands_processed")
            }
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def get_client(self) -> redis.Redis:
        """Get Redis client instance"""
        if not self._connected or not self.client:
            raise RuntimeError("Redis not connected. Call connect() first.")
        return self.client


class CacheManager:
    """High-level cache operations manager"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
        self.default_ttl = 3600  # 1 hour
    
    @property
    def client(self) -> redis.Redis:
        return self.redis_manager.get_client()
    
    def _get_key(self, key: str, tenant_id: str = None) -> str:
        """Generate namespaced cache key"""
        if tenant_id:
            return f"tenant:{tenant_id}:{key}"
        return f"global:{key}"
    
    async def get(self, key: str, tenant_id: str = None) -> Optional[Any]:
        """Get value from cache"""
        try:
            cache_key = self._get_key(key, tenant_id)
            value = await self.client.get(cache_key)
            
            if value is None:
                return None
            
            # Try to deserialize JSON first, then pickle
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                try:
                    return pickle.loads(value.encode('latin1'))
                except:
                    return value
                    
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: int = None, 
        tenant_id: str = None
    ) -> bool:
        """Set value in cache"""
        try:
            cache_key = self._get_key(key, tenant_id)
            ttl = ttl or self.default_ttl
            
            # Serialize value
            if isinstance(value, (dict, list, tuple)):
                serialized_value = json.dumps(value, default=str)
            elif isinstance(value, (str, int, float, bool)):
                serialized_value = value
            else:
                serialized_value = pickle.dumps(value).decode('latin1')
            
            await self.client.setex(cache_key, ttl, serialized_value)
            return True
            
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str, tenant_id: str = None) -> bool:
        """Delete value from cache"""
        try:
            cache_key = self._get_key(key, tenant_id)
            result = await self.client.delete(cache_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str, tenant_id: str = None) -> bool:
        """Check if key exists in cache"""
        try:
            cache_key = self._get_key(key, tenant_id)
            return await self.client.exists(cache_key) > 0
            
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1, tenant_id: str = None) -> int:
        """Increment counter in cache"""
        try:
            cache_key = self._get_key(key, tenant_id)
            return await self.client.incrby(cache_key, amount)
            
        except Exception as e:
            logger.error(f"Cache increment error for key {key}: {e}")
            return 0
    
    async def expire(self, key: str, ttl: int, tenant_id: str = None) -> bool:
        """Set expiration for existing key"""
        try:
            cache_key = self._get_key(key, tenant_id)
            return await self.client.expire(cache_key, ttl)
            
        except Exception as e:
            logger.error(f"Cache expire error for key {key}: {e}")
            return False
    
    async def get_keys(self, pattern: str, tenant_id: str = None) -> List[str]:
        """Get keys matching pattern"""
        try:
            if tenant_id:
                search_pattern = f"tenant:{tenant_id}:{pattern}"
            else:
                search_pattern = f"global:{pattern}"
            
            keys = await self.client.keys(search_pattern)
            
            # Remove namespace prefix from keys
            prefix = f"tenant:{tenant_id}:" if tenant_id else "global:"
            return [key.replace(prefix, "") for key in keys]
            
        except Exception as e:
            logger.error(f"Cache get_keys error for pattern {pattern}: {e}")
            return []
    
    async def clear_tenant_cache(self, tenant_id: str) -> int:
        """Clear all cache entries for a tenant"""
        try:
            pattern = f"tenant:{tenant_id}:*"
            keys = await self.client.keys(pattern)
            
            if keys:
                return await self.client.delete(*keys)
            return 0
            
        except Exception as e:
            logger.error(f"Clear tenant cache error for {tenant_id}: {e}")
            return 0


class SessionManager:
    """Session management using Redis"""
    
    def __init__(self, redis_manager: RedisManager):
        self.redis_manager = redis_manager
        self.session_ttl = 86400  # 24 hours
    
    @property
    def client(self) -> redis.Redis:
        return self.redis_manager.get_client()
    
    def _get_session_key(self, session_id: str) -> str:
        """Generate session key"""
        return f"session:{session_id}"
    
    async def create_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Create new session"""
        try:
            session_key = self._get_session_key(session_id)
            session_data = {
                **data,
                "created_at": datetime.utcnow().isoformat(),
                "last_accessed": datetime.utcnow().isoformat()
            }
            
            serialized_data = json.dumps(session_data, default=str)
            await self.client.setex(session_key, self.session_ttl, serialized_data)
            
            logger.debug(f"Created session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create session {session_id}: {e}")
            return False
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        try:
            session_key = self._get_session_key(session_id)
            data = await self.client.get(session_key)
            
            if not data:
                return None
            
            session_data = json.loads(data)
            
            # Update last accessed time
            session_data["last_accessed"] = datetime.utcnow().isoformat()
            await self.client.setex(session_key, self.session_ttl, json.dumps(session_data, default=str))
            
            return session_data
            
        except Exception as e:
            logger.error(f"Failed to get session {session_id}: {e}")
            return None
    
    async def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """Update session data"""
        try:
            session_key = self._get_session_key(session_id)
            existing_data = await self.get_session(session_id)
            
            if not existing_data:
                return False
            
            # Merge data
            updated_data = {**existing_data, **data}
            updated_data["last_accessed"] = datetime.utcnow().isoformat()
            
            serialized_data = json.dumps(updated_data, default=str)
            await self.client.setex(session_key, self.session_ttl, serialized_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete session"""
        try:
            session_key = self._get_session_key(session_id)
            result = await self.client.delete(session_key)
            
            logger.debug(f"Deleted session: {session_id}")
            return result > 0
            
        except Exception as e:
            logger.error(f"Failed to delete session {session_id}: {e}")
            return False
    
    async def extend_session(self, session_id: str, ttl: int = None) -> bool:
        """Extend session expiration"""
        try:
            session_key = self._get_session_key(session_id)
            ttl = ttl or self.session_ttl
            
            return await self.client.expire(session_key, ttl)
            
        except Exception as e:
            logger.error(f"Failed to extend session {session_id}: {e}")
            return False


# Global instances
redis_manager = RedisManager()
cache_manager = CacheManager(redis_manager)
session_manager = SessionManager(redis_manager)


async def init_redis():
    """Initialize Redis connections"""
    await redis_manager.connect()
    logger.info("Redis initialized successfully")


async def close_redis():
    """Close Redis connections"""
    await redis_manager.disconnect()
    logger.info("Redis connections closed")


def get_cache() -> CacheManager:
    """Get cache manager instance"""
    return cache_manager


def get_session_manager() -> SessionManager:
    """Get session manager instance"""
    return session_manager


def get_redis_client() -> redis.Redis:
    """Get Redis client instance"""
    return redis_manager.get_client()
