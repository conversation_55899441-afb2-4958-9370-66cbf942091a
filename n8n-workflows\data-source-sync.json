{"name": "Data Source Synchronization", "nodes": [{"parameters": {"rule": {"interval": [{"field": "hours", "hoursInterval": 6}]}}, "name": "Sync Schedule", "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/data-sources/active", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Get Active Data Sources", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [450, 300]}, {"parameters": {"batchSize": 1, "options": {}}, "name": "Process Each Source", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 1, "position": [650, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"sync_enabled\"]}}", "operation": "equal", "value2": "true"}, {"value1": "={{$json[\"source_type\"]}}", "operation": "notEqual", "value2": "manual"}]}}, "name": "Check Sync Enabled", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [850, 300]}, {"parameters": {"url": "http://backend:8000/api/v1/data-sources/={{$json[\"id\"]}}/sync", "sendBody": true, "bodyContentType": "json", "jsonBody": "{\n  \"force_sync\": false,\n  \"incremental\": true\n}", "options": {"headers": {"parameter": [{"name": "Authorization", "value": "Bearer {{$json[\"api_token\"]}}"}]}}}, "name": "<PERSON><PERSON> Sync", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1050, 200]}, {"parameters": {"amount": 30, "unit": "seconds"}, "name": "Wait for Sync", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1250, 200]}, {"parameters": {"url": "http://backend:8000/api/v1/data-sources/={{$json[\"id\"]}}/status", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth"}, "name": "Check Sync Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1450, 200]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json[\"sync_status\"]}}", "operation": "equal", "value2": "failed"}]}}, "name": "Check for Errors", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1650, 200]}, {"parameters": {"channel": "={{$json[\"alert_channel\"] || '#alerts'}}", "text": "🔄 *Data Source Sync Report*\n\n*Source:* {{$json[\"name\"]}}\n*Type:* {{$json[\"source_type\"]}}\n*Status:* {{$json[\"sync_status\"]}}\n\n{{$json[\"sync_status\"] === 'success' ? '✅ Sync completed successfully\\n• Documents synced: ' + $json[\"synced_documents\"] + '\\n• New documents: ' + $json[\"new_documents\"] + '\\n• Updated documents: ' + $json[\"updated_documents\"] : '❌ Sync failed\\n• Error: ' + $json[\"sync_error\"] + '\\n• Last successful sync: ' + $json[\"last_successful_sync\"]}}", "username": "AIthentiqMind Sync", "iconEmoji": ":arrows_counterclockwise:"}, "name": "Send Sync Report", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [1850, 300]}, {"parameters": {"channel": "={{$json[\"alert_channel\"] || '#alerts'}}", "text": "🚨 *Data Source Sync Failed*\n\n*Source:* {{$json[\"name\"]}}\n*Type:* {{$json[\"source_type\"]}}\n*Error:* {{$json[\"sync_error\"]}}\n*Last Successful Sync:* {{$json[\"last_successful_sync\"]}}\n\n*Recommended Actions:*\n• Check data source credentials\n• Verify network connectivity\n• Review error logs\n• Contact system administrator if issue persists", "username": "<PERSON><PERSON><PERSON>q<PERSON><PERSON>", "iconEmoji": ":rotating_light:"}, "name": "Send <PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.slack", "typeVersion": 1, "position": [1850, 100]}, {"parameters": {"url": "http://backend:8000/api/v1/webhooks/sync-complete", "sendBody": true, "bodyContentType": "json", "jsonBody": "{\n  \"data_source_id\": \"{{$json[\"id\"]}}\",\n  \"sync_status\": \"{{$json[\"sync_status\"]}}\",\n  \"documents_synced\": {{$json[\"synced_documents\"]}},\n  \"sync_duration\": {{$json[\"sync_duration\"]}},\n  \"timestamp\": \"{{new Date().toISOString()}}\"\n}"}, "name": "Update Backend", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2050, 200]}], "connections": {"Sync Schedule": {"main": [[{"node": "Get Active Data Sources", "type": "main", "index": 0}]]}, "Get Active Data Sources": {"main": [[{"node": "Process Each Source", "type": "main", "index": 0}]]}, "Process Each Source": {"main": [[{"node": "Check Sync Enabled", "type": "main", "index": 0}]]}, "Check Sync Enabled": {"main": [[{"node": "<PERSON><PERSON> Sync", "type": "main", "index": 0}]]}, "Trigger Sync": {"main": [[{"node": "Wait for Sync", "type": "main", "index": 0}]]}, "Wait for Sync": {"main": [[{"node": "Check Sync Status", "type": "main", "index": 0}]]}, "Check Sync Status": {"main": [[{"node": "Check for Errors", "type": "main", "index": 0}]]}, "Check for Errors": {"main": [[{"node": "Send <PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Send Sync Report", "type": "main", "index": 0}]]}, "Send Sync Report": {"main": [[{"node": "Update Backend", "type": "main", "index": 0}]]}, "Send Error Alert": {"main": [[{"node": "Update Backend", "type": "main", "index": 0}]]}}, "active": false, "settings": {"timezone": "UTC", "saveManualExecutions": true, "callerPolicy": "workflowsFromSameOwner"}, "id": "3", "tags": [{"name": "aithentiqmind", "id": "1"}, {"name": "data-sync", "id": "5"}, {"name": "automation", "id": "6"}]}