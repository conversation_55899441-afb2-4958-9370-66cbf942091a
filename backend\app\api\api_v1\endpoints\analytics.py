"""
Analytics endpoints for dashboard insights and reporting
"""

from typing import Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query as QueryParam
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import get_current_active_user, get_tenant_context, TenantContext
from app.core.logging import get_logger
from app.db.database import get_db
from app.models.user import User
from app.services.analytics import get_analytics_service

logger = get_logger(__name__)
router = APIRouter()


@router.get("/dashboard")
async def get_dashboard_analytics(
    days: int = QueryParam(30, ge=1, le=365, description="Number of days to analyze"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive dashboard analytics"""
    try:
        analytics_service = get_analytics_service()

        stats = await analytics_service.get_dashboard_stats(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        return stats

    except Exception as e:
        logger.error(f"Failed to get dashboard analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dashboard analytics"
        )


@router.get("/trust-scores")
async def get_trust_score_analytics(
    days: int = QueryParam(30, ge=1, le=365, description="Number of days to analyze"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get detailed trust score analytics"""
    try:
        analytics_service = get_analytics_service()

        analytics = await analytics_service.get_trust_score_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        return analytics

    except Exception as e:
        logger.error(f"Failed to get trust score analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trust score analytics"
        )


@router.get("/users")
async def get_user_analytics(
    days: int = QueryParam(30, ge=1, le=365, description="Number of days to analyze"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get user behavior analytics"""
    try:
        analytics_service = get_analytics_service()

        analytics = await analytics_service.get_user_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        return analytics

    except Exception as e:
        logger.error(f"Failed to get user analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user analytics"
        )


@router.get("/documents")
async def get_document_analytics(
    days: int = QueryParam(30, ge=1, le=365, description="Number of days to analyze"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get document usage analytics"""
    try:
        analytics_service = get_analytics_service()

        analytics = await analytics_service.get_document_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        return analytics

    except Exception as e:
        logger.error(f"Failed to get document analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve document analytics"
        )


@router.get("/summary")
async def get_analytics_summary(
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Get quick analytics summary for overview"""
    try:
        analytics_service = get_analytics_service()

        # Get 7-day and 30-day summaries
        week_stats = await analytics_service.get_dashboard_stats(
            tenant_id=str(tenant_context.tenant_id),
            days=7,
            db=db
        )

        month_stats = await analytics_service.get_dashboard_stats(
            tenant_id=str(tenant_context.tenant_id),
            days=30,
            db=db
        )

        return {
            "week": week_stats,
            "month": month_stats,
            "comparison": {
                "queries_growth": _calculate_growth(
                    week_stats.get("period_stats", {}).get("queries", 0),
                    month_stats.get("period_stats", {}).get("queries", 0) / 4  # Weekly average
                ),
                "trust_score_change": _calculate_change(
                    week_stats.get("trust_analytics", {}).get("average", 0),
                    month_stats.get("trust_analytics", {}).get("average", 0)
                )
            }
        }

    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analytics summary"
        )


@router.get("/export")
async def export_analytics(
    days: int = QueryParam(30, ge=1, le=365, description="Number of days to export"),
    format: str = QueryParam("json", regex="^(json|csv)$", description="Export format"),
    tenant_context: TenantContext = Depends(get_tenant_context),
    db: AsyncSession = Depends(get_db)
):
    """Export analytics data"""
    try:
        analytics_service = get_analytics_service()

        # Get comprehensive analytics
        dashboard_stats = await analytics_service.get_dashboard_stats(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        trust_analytics = await analytics_service.get_trust_score_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        user_analytics = await analytics_service.get_user_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        document_analytics = await analytics_service.get_document_analytics(
            tenant_id=str(tenant_context.tenant_id),
            days=days,
            db=db
        )

        export_data = {
            "export_date": datetime.utcnow().isoformat(),
            "period_days": days,
            "tenant_id": str(tenant_context.tenant_id),
            "dashboard": dashboard_stats,
            "trust_scores": trust_analytics,
            "users": user_analytics,
            "documents": document_analytics
        }

        if format == "csv":
            # Convert to CSV format (simplified)
            return {
                "message": "CSV export not yet implemented",
                "data": export_data
            }

        return export_data

    except Exception as e:
        logger.error(f"Failed to export analytics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export analytics data"
        )


def _calculate_growth(current: float, previous: float) -> float:
    """Calculate growth percentage"""
    if previous == 0:
        return 100.0 if current > 0 else 0.0
    return round(((current - previous) / previous) * 100, 2)


def _calculate_change(current: float, previous: float) -> float:
    """Calculate absolute change"""
    return round(current - previous, 3)
