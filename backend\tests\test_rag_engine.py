"""
Tests for RAG engine functionality
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.rag.engine import RAGEngine
from app.services.rag.query_processor import QueryProcessor
from app.services.rag.trust_scorer import TrustScorer
from app.models.tenant import Tenant
from app.models.user import User
from app.models.document import Document
from app.models.query import Query


class TestRAGEngine:
    """Test RAG engine functionality"""
    
    @pytest_asyncio.fixture
    async def rag_engine(self):
        """Create RAG engine instance"""
        return RAGEngine()
    
    @pytest_asyncio.fixture
    async def mock_vector_store(self):
        """Mock vector store"""
        mock_store = AsyncMock()
        mock_store.search_similar.return_value = [
            {
                "id": "chunk_1",
                "score": 0.95,
                "document_id": "doc_1",
                "chunk_id": "chunk_1",
                "chunk_index": 0,
                "content": "This is a test chunk about AI and machine learning.",
                "metadata": {
                    "document_title": "AI Guide",
                    "filename": "ai_guide.pdf",
                    "page_number": 1
                }
            },
            {
                "id": "chunk_2",
                "score": 0.87,
                "document_id": "doc_2",
                "chunk_id": "chunk_2",
                "chunk_index": 0,
                "content": "Machine learning is a subset of artificial intelligence.",
                "metadata": {
                    "document_title": "ML Basics",
                    "filename": "ml_basics.pdf",
                    "page_number": 1
                }
            }
        ]
        return mock_store
    
    @pytest_asyncio.fixture
    async def mock_llm_service(self):
        """Mock LLM service"""
        mock_llm = AsyncMock()
        mock_llm.generate_answer.return_value = {
            "answer": "AI and machine learning are related fields where machine learning is a subset of artificial intelligence.",
            "tokens_used": 150,
            "model": "gpt-4",
            "cost": 0.001
        }
        return mock_llm
    
    @pytest.mark.asyncio
    async def test_process_query_success(
        self,
        rag_engine: RAGEngine,
        mock_vector_store,
        mock_llm_service,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test successful query processing"""
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store), \
             patch.object(rag_engine, 'llm_service', mock_llm_service), \
             patch.object(rag_engine, 'trust_scorer') as mock_trust_scorer:
            
            # Mock trust scorer
            mock_trust_scorer.calculate_trust_score.return_value = {
                "overall_score": 0.85,
                "confidence_score": 0.9,
                "relevance_score": 0.95,
                "recency_score": 0.8,
                "source_diversity_score": 0.7,
                "factors": {"test": True}
            }
            
            # Process query
            result = await rag_engine.process_query(
                question="What is machine learning?",
                tenant_id=str(test_tenant.id),
                user_id=str(test_user.id),
                top_k=5,
                db=db_session
            )
            
            # Assertions
            assert result is not None
            assert "query_id" in result
            assert result["answer"] == "AI and machine learning are related fields where machine learning is a subset of artificial intelligence."
            assert result["trust_score"] == 0.85
            assert len(result["sources"]) == 2
            assert result["sources"][0]["relevanceScore"] == 0.95
            assert result["sources"][1]["relevanceScore"] == 0.87
            
            # Verify vector store was called
            mock_vector_store.search_similar.assert_called_once()
            
            # Verify LLM service was called
            mock_llm_service.generate_answer.assert_called_once()
            
            # Verify trust scorer was called
            mock_trust_scorer.calculate_trust_score.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_query_no_results(
        self,
        rag_engine: RAGEngine,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test query processing with no search results"""
        
        mock_vector_store = AsyncMock()
        mock_vector_store.search_similar.return_value = []
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store):
            
            result = await rag_engine.process_query(
                question="What is quantum computing?",
                tenant_id=str(test_tenant.id),
                user_id=str(test_user.id),
                top_k=5,
                db=db_session
            )
            
            # Should return a response indicating no relevant information found
            assert result is not None
            assert "query_id" in result
            assert "no relevant information" in result["answer"].lower()
            assert result["sources"] == []
            assert result["trust_score"] < 0.5  # Low trust score for no sources
    
    @pytest.mark.asyncio
    async def test_process_query_with_filters(
        self,
        rag_engine: RAGEngine,
        mock_vector_store,
        mock_llm_service,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test query processing with document filters"""
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store), \
             patch.object(rag_engine, 'llm_service', mock_llm_service), \
             patch.object(rag_engine, 'trust_scorer') as mock_trust_scorer:
            
            mock_trust_scorer.calculate_trust_score.return_value = {
                "overall_score": 0.85,
                "confidence_score": 0.9,
                "relevance_score": 0.95,
                "recency_score": 0.8,
                "source_diversity_score": 0.7,
                "factors": {"test": True}
            }
            
            # Process query with filters
            result = await rag_engine.process_query(
                question="What is machine learning?",
                tenant_id=str(test_tenant.id),
                user_id=str(test_user.id),
                top_k=5,
                document_filters={"tags": ["ai", "ml"]},
                db=db_session
            )
            
            # Verify filters were passed to vector store
            mock_vector_store.search_similar.assert_called_once()
            call_args = mock_vector_store.search_similar.call_args
            assert "filters" in call_args.kwargs
            assert call_args.kwargs["filters"]["tags"] == ["ai", "ml"]
    
    @pytest.mark.asyncio
    async def test_query_processor_preprocessing(self):
        """Test query preprocessing"""
        processor = QueryProcessor()
        
        # Test query cleaning
        cleaned = processor.preprocess_query("  What is AI?  ")
        assert cleaned == "What is AI?"
        
        # Test query expansion
        expanded = processor.expand_query("ML")
        assert "machine learning" in expanded.lower()
        
        # Test intent detection
        intent = processor.detect_intent("How do I implement neural networks?")
        assert intent in ["how_to", "implementation", "tutorial"]
    
    @pytest.mark.asyncio
    async def test_trust_scorer_calculation(self):
        """Test trust score calculation"""
        scorer = TrustScorer()
        
        # Mock search results
        search_results = [
            {
                "score": 0.95,
                "document_id": "doc_1",
                "metadata": {"created_at": "2024-01-01T00:00:00Z"}
            },
            {
                "score": 0.87,
                "document_id": "doc_2",
                "metadata": {"created_at": "2024-01-02T00:00:00Z"}
            }
        ]
        
        # Mock LLM response
        llm_response = {
            "answer": "This is a comprehensive answer about AI.",
            "confidence": 0.9
        }
        
        trust_score = scorer.calculate_trust_score(
            query="What is AI?",
            search_results=search_results,
            llm_response=llm_response
        )
        
        assert 0 <= trust_score["overall_score"] <= 1
        assert "confidence_score" in trust_score
        assert "relevance_score" in trust_score
        assert "recency_score" in trust_score
        assert "source_diversity_score" in trust_score
    
    @pytest.mark.asyncio
    async def test_error_handling(
        self,
        rag_engine: RAGEngine,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test error handling in RAG engine"""
        
        # Mock vector store to raise exception
        mock_vector_store = AsyncMock()
        mock_vector_store.search_similar.side_effect = Exception("Vector store error")
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store):
            
            with pytest.raises(Exception) as exc_info:
                await rag_engine.process_query(
                    question="What is AI?",
                    tenant_id=str(test_tenant.id),
                    user_id=str(test_user.id),
                    top_k=5,
                    db=db_session
                )
            
            assert "Vector store error" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_streaming_response(
        self,
        rag_engine: RAGEngine,
        mock_vector_store,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test streaming response generation"""
        
        # Mock streaming LLM service
        mock_llm_service = AsyncMock()
        
        async def mock_stream():
            yield {"type": "content", "content": "AI is "}
            yield {"type": "content", "content": "a field of "}
            yield {"type": "content", "content": "computer science."}
            yield {"type": "sources", "sources": []}
            yield {"type": "complete", "final": True}
        
        mock_llm_service.generate_answer_stream.return_value = mock_stream()
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store), \
             patch.object(rag_engine, 'llm_service', mock_llm_service):
            
            # Test streaming
            stream = rag_engine.process_query_stream(
                question="What is AI?",
                tenant_id=str(test_tenant.id),
                user_id=str(test_user.id),
                top_k=5,
                db=db_session
            )
            
            chunks = []
            async for chunk in stream:
                chunks.append(chunk)
            
            assert len(chunks) > 0
            assert any(chunk.get("type") == "content" for chunk in chunks)
            assert any(chunk.get("type") == "complete" for chunk in chunks)
    
    @pytest.mark.asyncio
    async def test_performance_metrics(
        self,
        rag_engine: RAGEngine,
        mock_vector_store,
        mock_llm_service,
        test_tenant: Tenant,
        test_user: User,
        db_session: AsyncSession
    ):
        """Test performance metrics collection"""
        
        with patch.object(rag_engine, 'vector_store', mock_vector_store), \
             patch.object(rag_engine, 'llm_service', mock_llm_service), \
             patch.object(rag_engine, 'trust_scorer') as mock_trust_scorer:
            
            mock_trust_scorer.calculate_trust_score.return_value = {
                "overall_score": 0.85,
                "confidence_score": 0.9,
                "relevance_score": 0.95,
                "recency_score": 0.8,
                "source_diversity_score": 0.7,
                "factors": {"test": True}
            }
            
            result = await rag_engine.process_query(
                question="What is machine learning?",
                tenant_id=str(test_tenant.id),
                user_id=str(test_user.id),
                top_k=5,
                db=db_session
            )
            
            # Check performance metrics are included
            assert "response_time" in result
            assert "retrieval_time" in result
            assert "generation_time" in result
            assert result["response_time"] > 0
            assert result["retrieval_time"] >= 0
            assert result["generation_time"] >= 0
