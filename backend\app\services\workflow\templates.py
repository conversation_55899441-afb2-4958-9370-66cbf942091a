"""
Pre-built workflow templates for common automation scenarios
"""

from typing import Dict, Any, List
import uuid

from app.core.logging import get_logger

logger = get_logger(__name__)


class WorkflowTemplates:
    """Pre-built workflow templates"""
    
    @staticmethod
    def document_processing_notification() -> Dict[str, Any]:
        """Template for document processing notifications"""
        return {
            "name": "Document Processing Notification",
            "nodes": [
                {
                    "parameters": {},
                    "name": "Webhook",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [250, 300],
                    "webhookId": str(uuid.uuid4()),
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "document-processed",
                        "responseMode": "onReceived"
                    }
                },
                {
                    "parameters": {
                        "conditions": {
                            "string": [
                                {
                                    "value1": "={{$json[\"status\"]}}",
                                    "operation": "equal",
                                    "value2": "processed"
                                }
                            ]
                        }
                    },
                    "name": "Check Status",
                    "type": "n8n-nodes-base.if",
                    "typeVersion": 1,
                    "position": [450, 300]
                },
                {
                    "parameters": {
                        "authentication": "generic",
                        "genericAuthType": "httpBasicAuth",
                        "url": "={{$json[\"notification_url\"]}}",
                        "options": {},
                        "bodyParametersUi": {
                            "parameter": [
                                {
                                    "name": "document_id",
                                    "value": "={{$json[\"document_id\"]}}"
                                },
                                {
                                    "name": "status",
                                    "value": "={{$json[\"status\"]}}"
                                },
                                {
                                    "name": "message",
                                    "value": "Document processing completed successfully"
                                }
                            ]
                        }
                    },
                    "name": "Send Success Notification",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [650, 200]
                },
                {
                    "parameters": {
                        "authentication": "generic",
                        "genericAuthType": "httpBasicAuth",
                        "url": "={{$json[\"notification_url\"]}}",
                        "options": {},
                        "bodyParametersUi": {
                            "parameter": [
                                {
                                    "name": "document_id",
                                    "value": "={{$json[\"document_id\"]}}"
                                },
                                {
                                    "name": "status",
                                    "value": "={{$json[\"status\"]}}"
                                },
                                {
                                    "name": "error",
                                    "value": "={{$json[\"error\"]}}"
                                },
                                {
                                    "name": "message",
                                    "value": "Document processing failed"
                                }
                            ]
                        }
                    },
                    "name": "Send Error Notification",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [650, 400]
                }
            ],
            "connections": {
                "Webhook": {
                    "main": [
                        [
                            {
                                "node": "Check Status",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Check Status": {
                    "main": [
                        [
                            {
                                "node": "Send Success Notification",
                                "type": "main",
                                "index": 0
                            }
                        ],
                        [
                            {
                                "node": "Send Error Notification",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": False,
            "settings": {},
            "id": str(uuid.uuid4())
        }
    
    @staticmethod
    def email_notification_workflow() -> Dict[str, Any]:
        """Template for email notifications"""
        return {
            "name": "Email Notification Workflow",
            "nodes": [
                {
                    "parameters": {},
                    "name": "Webhook",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [250, 300],
                    "webhookId": str(uuid.uuid4()),
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "send-email",
                        "responseMode": "onReceived"
                    }
                },
                {
                    "parameters": {
                        "fromEmail": "<EMAIL>",
                        "toEmail": "={{$json[\"to_email\"]}}",
                        "subject": "={{$json[\"subject\"]}}",
                        "text": "={{$json[\"message\"]}}",
                        "html": "={{$json[\"html_message\"]}}"
                    },
                    "name": "Send Email",
                    "type": "n8n-nodes-base.emailSend",
                    "typeVersion": 1,
                    "position": [450, 300]
                }
            ],
            "connections": {
                "Webhook": {
                    "main": [
                        [
                            {
                                "node": "Send Email",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": False,
            "settings": {},
            "id": str(uuid.uuid4())
        }
    
    @staticmethod
    def slack_notification_workflow() -> Dict[str, Any]:
        """Template for Slack notifications"""
        return {
            "name": "Slack Notification Workflow",
            "nodes": [
                {
                    "parameters": {},
                    "name": "Webhook",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [250, 300],
                    "webhookId": str(uuid.uuid4()),
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "slack-notify",
                        "responseMode": "onReceived"
                    }
                },
                {
                    "parameters": {
                        "channel": "={{$json[\"channel\"]}}",
                        "text": "={{$json[\"message\"]}}",
                        "username": "AIthentiqMind Bot",
                        "iconEmoji": ":robot_face:"
                    },
                    "name": "Send Slack Message",
                    "type": "n8n-nodes-base.slack",
                    "typeVersion": 1,
                    "position": [450, 300]
                }
            ],
            "connections": {
                "Webhook": {
                    "main": [
                        [
                            {
                                "node": "Send Slack Message",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": False,
            "settings": {},
            "id": str(uuid.uuid4())
        }
    
    @staticmethod
    def data_sync_workflow() -> Dict[str, Any]:
        """Template for data synchronization"""
        return {
            "name": "Data Sync Workflow",
            "nodes": [
                {
                    "parameters": {
                        "rule": {
                            "interval": [
                                {
                                    "field": "hours",
                                    "hoursInterval": 6
                                }
                            ]
                        }
                    },
                    "name": "Schedule Trigger",
                    "type": "n8n-nodes-base.scheduleTrigger",
                    "typeVersion": 1,
                    "position": [250, 300]
                },
                {
                    "parameters": {
                        "url": "={{$json[\"source_api_url\"]}}",
                        "authentication": "genericCredentialType",
                        "genericAuthType": "httpBasicAuth",
                        "options": {}
                    },
                    "name": "Fetch Data",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [450, 300]
                },
                {
                    "parameters": {
                        "url": "={{$json[\"destination_api_url\"]}}",
                        "sendBody": True,
                        "bodyContentType": "json",
                        "jsonBody": "={{$json}}",
                        "options": {}
                    },
                    "name": "Sync Data",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [650, 300]
                }
            ],
            "connections": {
                "Schedule Trigger": {
                    "main": [
                        [
                            {
                                "node": "Fetch Data",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Fetch Data": {
                    "main": [
                        [
                            {
                                "node": "Sync Data",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": False,
            "settings": {},
            "id": str(uuid.uuid4())
        }
    
    @staticmethod
    def query_analytics_workflow() -> Dict[str, Any]:
        """Template for query analytics and reporting"""
        return {
            "name": "Query Analytics Workflow",
            "nodes": [
                {
                    "parameters": {
                        "rule": {
                            "interval": [
                                {
                                    "field": "days",
                                    "daysInterval": 1
                                }
                            ]
                        }
                    },
                    "name": "Daily Trigger",
                    "type": "n8n-nodes-base.scheduleTrigger",
                    "typeVersion": 1,
                    "position": [250, 300]
                },
                {
                    "parameters": {
                        "url": "={{$json[\"analytics_api_url\"]}}",
                        "authentication": "genericCredentialType",
                        "genericAuthType": "httpBasicAuth",
                        "options": {}
                    },
                    "name": "Fetch Analytics",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [450, 300]
                },
                {
                    "parameters": {
                        "conditions": {
                            "number": [
                                {
                                    "value1": "={{$json[\"query_count\"]}}",
                                    "operation": "larger",
                                    "value2": 100
                                }
                            ]
                        }
                    },
                    "name": "Check Threshold",
                    "type": "n8n-nodes-base.if",
                    "typeVersion": 1,
                    "position": [650, 300]
                },
                {
                    "parameters": {
                        "url": "={{$json[\"report_webhook_url\"]}}",
                        "sendBody": True,
                        "bodyContentType": "json",
                        "jsonBody": "={{$json}}",
                        "options": {}
                    },
                    "name": "Send Report",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [850, 300]
                }
            ],
            "connections": {
                "Daily Trigger": {
                    "main": [
                        [
                            {
                                "node": "Fetch Analytics",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Fetch Analytics": {
                    "main": [
                        [
                            {
                                "node": "Check Threshold",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Check Threshold": {
                    "main": [
                        [
                            {
                                "node": "Send Report",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            },
            "active": False,
            "settings": {},
            "id": str(uuid.uuid4())
        }
    
    @classmethod
    def get_all_templates(cls) -> List[Dict[str, Any]]:
        """Get all available workflow templates"""
        return [
            {
                "id": "document-processing-notification",
                "name": "Document Processing Notification",
                "description": "Send notifications when documents are processed",
                "category": "notifications",
                "template": cls.document_processing_notification()
            },
            {
                "id": "email-notification",
                "name": "Email Notification",
                "description": "Send email notifications via webhook",
                "category": "notifications",
                "template": cls.email_notification_workflow()
            },
            {
                "id": "slack-notification",
                "name": "Slack Notification",
                "description": "Send Slack notifications via webhook",
                "category": "notifications",
                "template": cls.slack_notification_workflow()
            },
            {
                "id": "data-sync",
                "name": "Data Synchronization",
                "description": "Scheduled data synchronization between systems",
                "category": "integration",
                "template": cls.data_sync_workflow()
            },
            {
                "id": "query-analytics",
                "name": "Query Analytics",
                "description": "Daily analytics reporting for query metrics",
                "category": "analytics",
                "template": cls.query_analytics_workflow()
            }
        ]
    
    @classmethod
    def get_template_by_id(cls, template_id: str) -> Dict[str, Any]:
        """Get template by ID"""
        templates = cls.get_all_templates()
        for template in templates:
            if template["id"] == template_id:
                return template
        return None
