"""
Document processing service for extracting text and metadata from various file formats
"""

import asyncio
import hashlib
import mimetypes
import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import uuid

from langchain.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
    UnstructuredPowerPointLoader,
    UnstructuredExcelLoader
)
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import magic

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import DocumentProcessingError
from app.services.document.extractors import (
    PDFExtractor,
    DocxExtractor,
    TextExtractor,
    HTMLExtractor,
    MarkdownExtractor
)

logger = get_logger(__name__)


class DocumentProcessor:
    """Main document processing service"""
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        # File type to extractor mapping
        self.extractors = {
            'application/pdf': PDFExtractor(),
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': DocxExtractor(),
            'application/msword': DocxExtractor(),
            'text/plain': TextExtractor(),
            'text/html': HTMLExtractor(),
            'text/markdown': MarkdownExtractor(),
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': None,  # TODO: Implement
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': None,  # TODO: Implement
        }
    
    async def process_file(
        self,
        file_path: str,
        filename: str,
        tenant_id: str,
        user_id: str,
        document_id: str = None
    ) -> Dict[str, Any]:
        """
        Process a file and extract text, metadata, and chunks
        
        Args:
            file_path: Path to the uploaded file
            filename: Original filename
            tenant_id: Tenant identifier
            user_id: User identifier
            document_id: Optional document ID
            
        Returns:
            Dictionary containing extracted content and metadata
        """
        try:
            logger.info(
                "Starting document processing",
                file_path=file_path,
                filename=filename,
                tenant_id=tenant_id,
                user_id=user_id
            )
            
            # Generate document ID if not provided
            if not document_id:
                document_id = str(uuid.uuid4())
            
            # Detect file type
            mime_type = await self._detect_mime_type(file_path)
            file_size = os.path.getsize(file_path)
            
            # Validate file type
            if mime_type not in self.extractors:
                raise DocumentProcessingError(
                    f"Unsupported file type: {mime_type}",
                    details={"mime_type": mime_type, "filename": filename}
                )
            
            # Extract text content
            content = await self._extract_text(file_path, mime_type)
            
            # Generate content hash for deduplication
            content_hash = self._generate_content_hash(content)
            
            # Extract metadata
            metadata = await self._extract_metadata(file_path, filename, mime_type)
            
            # Split into chunks
            chunks = await self._create_chunks(content, document_id, metadata)
            
            # Validate chunk count
            if len(chunks) > settings.MAX_CHUNKS_PER_DOCUMENT:
                raise DocumentProcessingError(
                    f"Document too large: {len(chunks)} chunks (max: {settings.MAX_CHUNKS_PER_DOCUMENT})",
                    details={"chunk_count": len(chunks), "max_chunks": settings.MAX_CHUNKS_PER_DOCUMENT}
                )
            
            result = {
                "document_id": document_id,
                "filename": filename,
                "mime_type": mime_type,
                "file_size": file_size,
                "content": content,
                "content_hash": content_hash,
                "metadata": metadata,
                "chunks": chunks,
                "chunk_count": len(chunks),
                "tenant_id": tenant_id,
                "user_id": user_id
            }
            
            logger.info(
                "Document processing completed",
                document_id=document_id,
                chunk_count=len(chunks),
                content_length=len(content)
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Document processing failed",
                error=str(e),
                file_path=file_path,
                filename=filename
            )
            raise DocumentProcessingError(f"Failed to process document: {str(e)}")
    
    async def _detect_mime_type(self, file_path: str) -> str:
        """Detect MIME type of the file"""
        try:
            # Use python-magic for accurate detection
            mime_type = magic.from_file(file_path, mime=True)
            
            # Fallback to mimetypes module
            if not mime_type or mime_type == 'application/octet-stream':
                mime_type, _ = mimetypes.guess_type(file_path)
            
            return mime_type or 'application/octet-stream'
            
        except Exception as e:
            logger.warning(f"Failed to detect MIME type: {e}")
            # Fallback based on file extension
            mime_type, _ = mimetypes.guess_type(file_path)
            return mime_type or 'application/octet-stream'
    
    async def _extract_text(self, file_path: str, mime_type: str) -> str:
        """Extract text content from file"""
        extractor = self.extractors.get(mime_type)
        
        if not extractor:
            raise DocumentProcessingError(f"No extractor available for {mime_type}")
        
        try:
            content = await extractor.extract_text(file_path)
            
            if not content or len(content.strip()) == 0:
                raise DocumentProcessingError("No text content extracted from file")
            
            return content.strip()
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text: {str(e)}")
    
    async def _extract_metadata(self, file_path: str, filename: str, mime_type: str) -> Dict[str, Any]:
        """Extract metadata from file"""
        metadata = {
            "filename": filename,
            "mime_type": mime_type,
            "file_size": os.path.getsize(file_path),
            "file_extension": Path(filename).suffix.lower(),
        }
        
        # Try to extract additional metadata using the appropriate extractor
        extractor = self.extractors.get(mime_type)
        if extractor and hasattr(extractor, 'extract_metadata'):
            try:
                additional_metadata = await extractor.extract_metadata(file_path)
                metadata.update(additional_metadata)
            except Exception as e:
                logger.warning(f"Failed to extract additional metadata: {e}")
        
        return metadata
    
    async def _create_chunks(
        self, 
        content: str, 
        document_id: str, 
        metadata: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Split content into chunks"""
        try:
            # Create LangChain document
            doc = Document(page_content=content, metadata=metadata)
            
            # Split into chunks
            chunks = self.text_splitter.split_documents([doc])
            
            # Convert to our format with additional metadata
            processed_chunks = []
            for i, chunk in enumerate(chunks):
                chunk_data = {
                    "chunk_id": str(uuid.uuid4()),
                    "document_id": document_id,
                    "chunk_index": i,
                    "content": chunk.page_content,
                    "metadata": {
                        **chunk.metadata,
                        "chunk_index": i,
                        "chunk_length": len(chunk.page_content),
                        "chunk_word_count": len(chunk.page_content.split())
                    }
                }
                processed_chunks.append(chunk_data)
            
            return processed_chunks
            
        except Exception as e:
            logger.error(f"Chunk creation failed: {e}")
            raise DocumentProcessingError(f"Failed to create chunks: {str(e)}")
    
    def _generate_content_hash(self, content: str) -> str:
        """Generate SHA-256 hash of content for deduplication"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    async def validate_file(self, file_path: str, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Validate file before processing
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check file exists
            if not os.path.exists(file_path):
                return False, "File does not exist"
            
            # Check file size
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "File is empty"
            
            if file_size > settings.MAX_FILE_SIZE:
                return False, f"File too large: {file_size} bytes (max: {settings.MAX_FILE_SIZE})"
            
            # Check file type
            mime_type = await self._detect_mime_type(file_path)
            if mime_type not in self.extractors:
                return False, f"Unsupported file type: {mime_type}"
            
            # Check file extension
            file_ext = Path(filename).suffix.lower().lstrip('.')
            if file_ext not in settings.ALLOWED_FILE_TYPES:
                return False, f"File extension not allowed: {file_ext}"
            
            return True, None
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return {
            "supported_types": list(self.extractors.keys()),
            "chunk_size": settings.CHUNK_SIZE,
            "chunk_overlap": settings.CHUNK_OVERLAP,
            "max_chunks_per_document": settings.MAX_CHUNKS_PER_DOCUMENT,
            "max_file_size": settings.MAX_FILE_SIZE,
            "allowed_extensions": settings.ALLOWED_FILE_TYPES
        }
