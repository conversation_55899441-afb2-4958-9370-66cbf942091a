"""
Vector embedding service for generating and managing embeddings
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
import numpy as np
from abc import ABC, abstractmethod

from langchain.embeddings import OpenAIEmbeddings
from langchain.embeddings.base import Embeddings
import openai
import httpx

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import VectorStoreError

logger = get_logger(__name__)


class BaseEmbeddingProvider(ABC):
    """Base class for embedding providers"""
    
    @abstractmethod
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple documents"""
        pass
    
    @abstractmethod
    async def embed_query(self, text: str) -> List[float]:
        """Generate embedding for a single query"""
        pass
    
    @abstractmethod
    def get_dimension(self) -> int:
        """Get the dimension of embeddings"""
        pass


class OpenAIEmbeddingProvider(BaseEmbeddingProvider):
    """OpenAI embedding provider"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.model = model or settings.OPENAI_EMBEDDING_MODEL
        
        if not self.api_key:
            raise VectorStoreError("OpenAI API key not provided")
        
        self.client = openai.AsyncOpenAI(api_key=self.api_key)
        
        # Model dimensions mapping
        self.model_dimensions = {
            "text-embedding-ada-002": 1536,
            "text-embedding-3-small": 1536,
            "text-embedding-3-large": 3072,
        }
    
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple documents"""
        try:
            # OpenAI has a limit on batch size, so we process in chunks
            batch_size = 100
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=batch
                )
                
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
            
            logger.info(f"Generated embeddings for {len(texts)} documents")
            return all_embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate document embeddings: {e}")
            raise VectorStoreError(f"OpenAI embedding generation failed: {str(e)}")
    
    async def embed_query(self, text: str) -> List[float]:
        """Generate embedding for a single query"""
        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=[text]
            )
            
            embedding = response.data[0].embedding
            logger.debug(f"Generated query embedding with dimension {len(embedding)}")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise VectorStoreError(f"OpenAI query embedding failed: {str(e)}")
    
    def get_dimension(self) -> int:
        """Get the dimension of embeddings"""
        return self.model_dimensions.get(self.model, 1536)


class OllamaEmbeddingProvider(BaseEmbeddingProvider):
    """Ollama embedding provider for local models"""
    
    def __init__(self, base_url: str = None, model: str = None):
        self.base_url = base_url or settings.OLLAMA_BASE_URL
        self.model = model or settings.OLLAMA_EMBEDDING_MODEL
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple documents"""
        try:
            embeddings = []
            
            for text in texts:
                embedding = await self._generate_embedding(text)
                embeddings.append(embedding)
            
            logger.info(f"Generated Ollama embeddings for {len(texts)} documents")
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate Ollama document embeddings: {e}")
            raise VectorStoreError(f"Ollama embedding generation failed: {str(e)}")
    
    async def embed_query(self, text: str) -> List[float]:
        """Generate embedding for a single query"""
        try:
            embedding = await self._generate_embedding(text)
            logger.debug(f"Generated Ollama query embedding with dimension {len(embedding)}")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to generate Ollama query embedding: {e}")
            raise VectorStoreError(f"Ollama query embedding failed: {str(e)}")
    
    async def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using Ollama API"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/embeddings",
                json={
                    "model": self.model,
                    "prompt": text
                }
            )
            response.raise_for_status()
            
            result = response.json()
            return result["embedding"]
            
        except Exception as e:
            raise VectorStoreError(f"Ollama API call failed: {str(e)}")
    
    def get_dimension(self) -> int:
        """Get the dimension of embeddings (model-dependent)"""
        # Common Ollama embedding model dimensions
        model_dimensions = {
            "nomic-embed-text": 768,
            "all-minilm": 384,
            "sentence-transformers": 384,
        }
        return model_dimensions.get(self.model, 768)


class EmbeddingService:
    """Main embedding service that manages different providers"""
    
    def __init__(self, provider_type: str = "openai"):
        self.provider_type = provider_type
        self.provider = self._create_provider(provider_type)
    
    def _create_provider(self, provider_type: str) -> BaseEmbeddingProvider:
        """Create embedding provider based on type"""
        if provider_type == "openai":
            return OpenAIEmbeddingProvider()
        elif provider_type == "ollama":
            return OllamaEmbeddingProvider()
        else:
            raise VectorStoreError(f"Unsupported embedding provider: {provider_type}")
    
    async def embed_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate embeddings for document chunks
        
        Args:
            chunks: List of chunk dictionaries with 'content' field
            
        Returns:
            List of chunks with added 'embedding' field
        """
        try:
            # Extract text content from chunks
            texts = [chunk["content"] for chunk in chunks]
            
            # Generate embeddings
            embeddings = await self.provider.embed_documents(texts)
            
            # Add embeddings to chunks
            for chunk, embedding in zip(chunks, embeddings):
                chunk["embedding"] = embedding
                chunk["embedding_model"] = self.provider.model if hasattr(self.provider, 'model') else self.provider_type
                chunk["embedding_dimension"] = len(embedding)
            
            logger.info(f"Added embeddings to {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to embed chunks: {e}")
            raise VectorStoreError(f"Chunk embedding failed: {str(e)}")
    
    async def embed_query(self, query: str) -> List[float]:
        """Generate embedding for search query"""
        try:
            embedding = await self.provider.embed_query(query)
            logger.debug(f"Generated query embedding for: {query[:50]}...")
            return embedding
            
        except Exception as e:
            logger.error(f"Failed to embed query: {e}")
            raise VectorStoreError(f"Query embedding failed: {str(e)}")
    
    def get_embedding_info(self) -> Dict[str, Any]:
        """Get information about the embedding service"""
        return {
            "provider": self.provider_type,
            "model": getattr(self.provider, 'model', 'unknown'),
            "dimension": self.provider.get_dimension(),
            "base_url": getattr(self.provider, 'base_url', None)
        }
    
    async def test_connection(self) -> bool:
        """Test if the embedding service is working"""
        try:
            test_text = "This is a test embedding."
            embedding = await self.provider.embed_query(test_text)
            
            if not embedding or len(embedding) == 0:
                return False
            
            logger.info(f"Embedding service test successful: {len(embedding)} dimensions")
            return True
            
        except Exception as e:
            logger.error(f"Embedding service test failed: {e}")
            return False
    
    async def batch_embed_with_retry(
        self, 
        texts: List[str], 
        max_retries: int = 3,
        batch_size: int = 50
    ) -> List[List[float]]:
        """
        Generate embeddings with retry logic and batching
        """
        all_embeddings = []
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            for attempt in range(max_retries):
                try:
                    batch_embeddings = await self.provider.embed_documents(batch)
                    all_embeddings.extend(batch_embeddings)
                    break
                    
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"Failed to embed batch after {max_retries} attempts: {e}")
                        raise
                    
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Embedding attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                    await asyncio.sleep(wait_time)
        
        return all_embeddings


# Factory function for creating embedding service
def create_embedding_service(provider_type: str = None) -> EmbeddingService:
    """Create embedding service with specified provider"""
    if provider_type is None:
        # Auto-detect based on available configuration
        if settings.OPENAI_API_KEY:
            provider_type = "openai"
        elif settings.OLLAMA_BASE_URL:
            provider_type = "ollama"
        else:
            raise VectorStoreError("No embedding provider configured")
    
    return EmbeddingService(provider_type)
