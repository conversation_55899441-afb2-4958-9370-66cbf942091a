"""
Text extractors for different file formats
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import tempfile
import os

import PyPDF2
import docx
from bs4 import BeautifulSoup
import markdown

from app.core.logging import get_logger
from app.core.exceptions import DocumentProcessingError

logger = get_logger(__name__)


class BaseExtractor(ABC):
    """Base class for text extractors"""
    
    @abstractmethod
    async def extract_text(self, file_path: str) -> str:
        """Extract text content from file"""
        pass
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from file (optional)"""
        return {}


class PDFExtractor(BaseExtractor):
    """PDF text extractor using PyPDF2"""
    
    async def extract_text(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            text_content = []
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content.append(page_text)
                    except Exception as e:
                        logger.warning(f"Failed to extract text from page {page_num}: {e}")
                        continue
            
            if not text_content:
                raise DocumentProcessingError("No text content found in PDF")
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"PDF extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text from PDF: {str(e)}")
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from PDF"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                metadata = {
                    "page_count": len(pdf_reader.pages),
                    "format": "PDF"
                }
                
                # Extract PDF metadata if available
                if pdf_reader.metadata:
                    pdf_meta = pdf_reader.metadata
                    if pdf_meta.title:
                        metadata["title"] = pdf_meta.title
                    if pdf_meta.author:
                        metadata["author"] = pdf_meta.author
                    if pdf_meta.subject:
                        metadata["subject"] = pdf_meta.subject
                    if pdf_meta.creator:
                        metadata["creator"] = pdf_meta.creator
                    if pdf_meta.producer:
                        metadata["producer"] = pdf_meta.producer
                    if pdf_meta.creation_date:
                        metadata["creation_date"] = str(pdf_meta.creation_date)
                    if pdf_meta.modification_date:
                        metadata["modification_date"] = str(pdf_meta.modification_date)
                
                return metadata
                
        except Exception as e:
            logger.warning(f"Failed to extract PDF metadata: {e}")
            return {"format": "PDF"}


class DocxExtractor(BaseExtractor):
    """DOCX text extractor using python-docx"""
    
    async def extract_text(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = docx.Document(file_path)
            
            text_content = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            if not text_content:
                raise DocumentProcessingError("No text content found in DOCX")
            
            return "\n\n".join(text_content)
            
        except Exception as e:
            logger.error(f"DOCX extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text from DOCX: {str(e)}")
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from DOCX"""
        try:
            doc = docx.Document(file_path)
            
            metadata = {
                "format": "DOCX",
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables)
            }
            
            # Extract core properties
            core_props = doc.core_properties
            if core_props.title:
                metadata["title"] = core_props.title
            if core_props.author:
                metadata["author"] = core_props.author
            if core_props.subject:
                metadata["subject"] = core_props.subject
            if core_props.keywords:
                metadata["keywords"] = core_props.keywords
            if core_props.comments:
                metadata["comments"] = core_props.comments
            if core_props.created:
                metadata["created"] = str(core_props.created)
            if core_props.modified:
                metadata["modified"] = str(core_props.modified)
            if core_props.last_modified_by:
                metadata["last_modified_by"] = core_props.last_modified_by
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Failed to extract DOCX metadata: {e}")
            return {"format": "DOCX"}


class TextExtractor(BaseExtractor):
    """Plain text file extractor"""
    
    async def extract_text(self, file_path: str) -> str:
        """Extract text from plain text file"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                        if content.strip():
                            return content
                except UnicodeDecodeError:
                    continue
            
            raise DocumentProcessingError("Could not decode text file with any supported encoding")
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text: {str(e)}")
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
            return {
                "format": "Text",
                "character_count": len(content),
                "word_count": len(content.split()),
                "line_count": len(content.splitlines())
            }
            
        except Exception as e:
            logger.warning(f"Failed to extract text metadata: {e}")
            return {"format": "Text"}


class HTMLExtractor(BaseExtractor):
    """HTML text extractor using BeautifulSoup"""
    
    async def extract_text(self, file_path: str) -> str:
        """Extract text from HTML file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            
            if not text.strip():
                raise DocumentProcessingError("No text content found in HTML")
            
            return text
            
        except Exception as e:
            logger.error(f"HTML extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text from HTML: {str(e)}")
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from HTML"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            metadata = {"format": "HTML"}
            
            # Extract title
            title_tag = soup.find('title')
            if title_tag:
                metadata["title"] = title_tag.get_text().strip()
            
            # Extract meta tags
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                name = meta.get('name') or meta.get('property')
                content = meta.get('content')
                if name and content:
                    metadata[f"meta_{name}"] = content
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Failed to extract HTML metadata: {e}")
            return {"format": "HTML"}


class MarkdownExtractor(BaseExtractor):
    """Markdown text extractor"""
    
    async def extract_text(self, file_path: str) -> str:
        """Extract text from Markdown file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                md_content = file.read()
            
            # Convert markdown to HTML then extract text
            html = markdown.markdown(md_content)
            soup = BeautifulSoup(html, 'html.parser')
            text = soup.get_text()
            
            # Also include the raw markdown for better context
            combined_text = f"Raw Markdown:\n{md_content}\n\nRendered Text:\n{text}"
            
            if not text.strip():
                raise DocumentProcessingError("No text content found in Markdown")
            
            return combined_text
            
        except Exception as e:
            logger.error(f"Markdown extraction failed: {e}")
            raise DocumentProcessingError(f"Failed to extract text from Markdown: {str(e)}")
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from Markdown"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            metadata = {
                "format": "Markdown",
                "character_count": len(content),
                "word_count": len(content.split()),
                "line_count": len(content.splitlines())
            }
            
            # Check for YAML front matter
            if content.startswith('---'):
                try:
                    import yaml
                    parts = content.split('---', 2)
                    if len(parts) >= 3:
                        front_matter = yaml.safe_load(parts[1])
                        if isinstance(front_matter, dict):
                            metadata.update({f"frontmatter_{k}": v for k, v in front_matter.items()})
                except ImportError:
                    logger.warning("PyYAML not available for front matter parsing")
                except Exception as e:
                    logger.warning(f"Failed to parse YAML front matter: {e}")
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Failed to extract Markdown metadata: {e}")
            return {"format": "Markdown"}
