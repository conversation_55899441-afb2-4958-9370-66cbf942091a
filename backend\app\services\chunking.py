"""
Document chunking service with intelligent text splitting
"""

import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ChunkConfig:
    """Configuration for document chunking"""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    min_chunk_size: int = 100
    max_chunk_size: int = 2000
    preserve_sentences: bool = True
    preserve_paragraphs: bool = True


class DocumentChunker:
    """Service for intelligent document chunking"""
    
    def __init__(self, config: Optional[ChunkConfig] = None):
        self.config = config or ChunkConfig(
            chunk_size=getattr(settings, 'CHUNK_SIZE', 1000),
            chunk_overlap=getattr(settings, 'CHUNK_OVERLAP', 200)
        )
        self.logger = get_logger("chunker")
    
    def chunk_text(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Chunk text into smaller segments"""
        try:
            # Clean and normalize text
            cleaned_text = self._clean_text(text)
            
            # Choose chunking strategy based on text characteristics
            if self._is_structured_text(cleaned_text):
                chunks = self._chunk_structured_text(cleaned_text)
            else:
                chunks = self._chunk_plain_text(cleaned_text)
            
            # Add metadata to chunks
            enriched_chunks = []
            for i, chunk in enumerate(chunks):
                enriched_chunk = {
                    "content": chunk,
                    "chunk_index": i,
                    "chunk_size": len(chunk),
                    "metadata": {
                        **(metadata or {}),
                        "chunking_method": "intelligent",
                        "chunk_overlap": self.config.chunk_overlap,
                        "created_at": datetime.utcnow().isoformat()
                    }
                }
                enriched_chunks.append(enriched_chunk)
            
            self.logger.info(f"Created {len(enriched_chunks)} chunks from text of length {len(text)}")
            return enriched_chunks
            
        except Exception as e:
            self.logger.error(f"Failed to chunk text: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize line breaks
        text = re.sub(r'\r\n|\r', '\n', text)
        
        return text.strip()
    
    def _is_structured_text(self, text: str) -> bool:
        """Determine if text has structure (headers, lists, etc.)"""
        # Look for markdown-style headers
        if re.search(r'^#{1,6}\s+', text, re.MULTILINE):
            return True
        
        # Look for numbered lists
        if re.search(r'^\d+\.\s+', text, re.MULTILINE):
            return True
        
        # Look for bullet points
        if re.search(r'^[-*•]\s+', text, re.MULTILINE):
            return True
        
        # Look for multiple paragraphs
        if len(re.findall(r'\n\s*\n', text)) > 2:
            return True
        
        return False
    
    def _chunk_structured_text(self, text: str) -> List[str]:
        """Chunk structured text preserving logical boundaries"""
        chunks = []
        
        # Split by major sections (headers)
        sections = self._split_by_headers(text)
        
        for section in sections:
            if len(section) <= self.config.max_chunk_size:
                chunks.append(section)
            else:
                # Further split large sections
                sub_chunks = self._chunk_by_paragraphs(section)
                chunks.extend(sub_chunks)
        
        return self._apply_overlap(chunks)
    
    def _chunk_plain_text(self, text: str) -> List[str]:
        """Chunk plain text using sentence and paragraph boundaries"""
        # First try to split by paragraphs
        paragraphs = re.split(r'\n\s*\n', text)
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) > self.config.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                
                # If paragraph itself is too long, split by sentences
                if len(paragraph) > self.config.chunk_size:
                    sentence_chunks = self._chunk_by_sentences(paragraph)
                    chunks.extend(sentence_chunks[:-1])  # Add all but last
                    current_chunk = sentence_chunks[-1] if sentence_chunks else ""
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # Add remaining chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return self._apply_overlap(chunks)
    
    def _split_by_headers(self, text: str) -> List[str]:
        """Split text by headers"""
        # Split by markdown headers
        header_pattern = r'^(#{1,6}\s+.+)$'
        parts = re.split(header_pattern, text, flags=re.MULTILINE)
        
        sections = []
        current_section = ""
        
        for i, part in enumerate(parts):
            if re.match(header_pattern, part):
                if current_section.strip():
                    sections.append(current_section.strip())
                current_section = part
            else:
                current_section += part
        
        if current_section.strip():
            sections.append(current_section.strip())
        
        return sections
    
    def _chunk_by_paragraphs(self, text: str) -> List[str]:
        """Chunk text by paragraphs"""
        paragraphs = re.split(r'\n\s*\n', text)
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            if len(current_chunk) + len(paragraph) > self.config.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                if len(paragraph) > self.config.chunk_size:
                    # Split long paragraph by sentences
                    sentence_chunks = self._chunk_by_sentences(paragraph)
                    chunks.extend(sentence_chunks)
                    current_chunk = ""
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _chunk_by_sentences(self, text: str) -> List[str]:
        """Chunk text by sentences"""
        # Simple sentence splitting (can be improved with NLTK/spaCy)
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            if len(current_chunk) + len(sentence) > self.config.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # If single sentence is too long, split by words
                if len(sentence) > self.config.chunk_size:
                    word_chunks = self._chunk_by_words(sentence)
                    chunks.extend(word_chunks)
                    current_chunk = ""
                else:
                    current_chunk = sentence
            else:
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _chunk_by_words(self, text: str) -> List[str]:
        """Chunk text by words (last resort)"""
        words = text.split()
        chunks = []
        current_chunk = ""
        
        for word in words:
            if len(current_chunk) + len(word) + 1 > self.config.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = word
            else:
                if current_chunk:
                    current_chunk += " " + word
                else:
                    current_chunk = word
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _apply_overlap(self, chunks: List[str]) -> List[str]:
        """Apply overlap between chunks"""
        if len(chunks) <= 1 or self.config.chunk_overlap <= 0:
            return chunks
        
        overlapped_chunks = []
        
        for i, chunk in enumerate(chunks):
            if i == 0:
                overlapped_chunks.append(chunk)
            else:
                # Get overlap from previous chunk
                prev_chunk = chunks[i - 1]
                overlap_text = self._get_overlap_text(prev_chunk, self.config.chunk_overlap)
                
                if overlap_text:
                    overlapped_chunk = overlap_text + " " + chunk
                else:
                    overlapped_chunk = chunk
                
                overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks
    
    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """Get overlap text from the end of a chunk"""
        if len(text) <= overlap_size:
            return text
        
        # Try to find a good breaking point (sentence or word boundary)
        overlap_text = text[-overlap_size:]
        
        # Find the last sentence boundary
        sentence_match = re.search(r'[.!?]\s+', overlap_text)
        if sentence_match:
            return overlap_text[sentence_match.end():]
        
        # Find the last word boundary
        word_match = re.search(r'\s+', overlap_text)
        if word_match:
            return overlap_text[word_match.end():]
        
        return overlap_text
    
    def get_chunk_stats(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get statistics about chunks"""
        if not chunks:
            return {"total_chunks": 0}
        
        chunk_sizes = [len(chunk["content"]) for chunk in chunks]
        
        return {
            "total_chunks": len(chunks),
            "avg_chunk_size": sum(chunk_sizes) / len(chunk_sizes),
            "min_chunk_size": min(chunk_sizes),
            "max_chunk_size": max(chunk_sizes),
            "total_characters": sum(chunk_sizes),
            "config": {
                "chunk_size": self.config.chunk_size,
                "chunk_overlap": self.config.chunk_overlap,
                "preserve_sentences": self.config.preserve_sentences,
                "preserve_paragraphs": self.config.preserve_paragraphs
            }
        }


# Global chunker instance
document_chunker = DocumentChunker()


def get_document_chunker() -> DocumentChunker:
    """Get document chunker instance"""
    return document_chunker
