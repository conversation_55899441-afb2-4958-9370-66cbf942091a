"""
n8n workflow automation client
"""

import asyncio
from typing import Dict, Any, List, Optional
import httpx
import json

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import WorkflowError

logger = get_logger(__name__)


class N8nClient:
    """Client for interacting with n8n workflow automation"""
    
    def __init__(self, base_url: str = None, api_key: str = None):
        self.base_url = base_url or settings.N8N_BASE_URL
        self.api_key = api_key or settings.N8N_API_KEY
        
        # Setup HTTP client
        headers = {}
        if self.api_key:
            headers["X-N8N-API-KEY"] = self.api_key
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers=headers,
            timeout=30.0
        )
        
        logger.info(f"Initialized n8n client: {self.base_url}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Check n8n health status"""
        try:
            response = await self.client.get("/healthz")
            
            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "url": self.base_url,
                    "version": response.headers.get("x-n8n-version", "unknown")
                }
            else:
                return {
                    "status": "unhealthy",
                    "url": self.base_url,
                    "error": f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            logger.error(f"n8n health check failed: {e}")
            return {
                "status": "unhealthy",
                "url": self.base_url,
                "error": str(e)
            }
    
    async def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows"""
        try:
            response = await self.client.get("/api/v1/workflows")
            response.raise_for_status()
            
            workflows = response.json()
            logger.info(f"Retrieved {len(workflows)} workflows")
            return workflows
            
        except Exception as e:
            logger.error(f"Failed to list workflows: {e}")
            raise WorkflowError(f"Failed to list workflows: {str(e)}")
    
    async def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Get workflow by ID"""
        try:
            response = await self.client.get(f"/api/v1/workflows/{workflow_id}")
            response.raise_for_status()
            
            workflow = response.json()
            logger.info(f"Retrieved workflow: {workflow_id}")
            return workflow
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                raise WorkflowError(f"Workflow not found: {workflow_id}")
            raise WorkflowError(f"Failed to get workflow: {str(e)}")
        except Exception as e:
            logger.error(f"Failed to get workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to get workflow: {str(e)}")
    
    async def create_workflow(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new workflow"""
        try:
            response = await self.client.post(
                "/api/v1/workflows",
                json=workflow_data
            )
            response.raise_for_status()
            
            workflow = response.json()
            logger.info(f"Created workflow: {workflow.get('id')}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to create workflow: {e}")
            raise WorkflowError(f"Failed to create workflow: {str(e)}")
    
    async def update_workflow(self, workflow_id: str, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing workflow"""
        try:
            response = await self.client.put(
                f"/api/v1/workflows/{workflow_id}",
                json=workflow_data
            )
            response.raise_for_status()
            
            workflow = response.json()
            logger.info(f"Updated workflow: {workflow_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to update workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to update workflow: {str(e)}")
    
    async def delete_workflow(self, workflow_id: str) -> bool:
        """Delete a workflow"""
        try:
            response = await self.client.delete(f"/api/v1/workflows/{workflow_id}")
            response.raise_for_status()
            
            logger.info(f"Deleted workflow: {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to delete workflow: {str(e)}")
    
    async def activate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Activate a workflow"""
        try:
            response = await self.client.post(f"/api/v1/workflows/{workflow_id}/activate")
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Activated workflow: {workflow_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to activate workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to activate workflow: {str(e)}")
    
    async def deactivate_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """Deactivate a workflow"""
        try:
            response = await self.client.post(f"/api/v1/workflows/{workflow_id}/deactivate")
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Deactivated workflow: {workflow_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to deactivate workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to deactivate workflow: {str(e)}")
    
    async def execute_workflow(
        self, 
        workflow_id: str, 
        input_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Execute a workflow manually"""
        try:
            payload = {"workflowData": input_data or {}}
            
            response = await self.client.post(
                f"/api/v1/workflows/{workflow_id}/execute",
                json=payload
            )
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Executed workflow: {workflow_id}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to execute workflow {workflow_id}: {e}")
            raise WorkflowError(f"Failed to execute workflow: {str(e)}")
    
    async def get_executions(
        self, 
        workflow_id: str = None, 
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get workflow executions"""
        try:
            params = {"limit": limit}
            if workflow_id:
                params["workflowId"] = workflow_id
            
            response = await self.client.get("/api/v1/executions", params=params)
            response.raise_for_status()
            
            executions = response.json()
            logger.info(f"Retrieved {len(executions)} executions")
            return executions
            
        except Exception as e:
            logger.error(f"Failed to get executions: {e}")
            raise WorkflowError(f"Failed to get executions: {str(e)}")
    
    async def get_execution(self, execution_id: str) -> Dict[str, Any]:
        """Get execution details"""
        try:
            response = await self.client.get(f"/api/v1/executions/{execution_id}")
            response.raise_for_status()
            
            execution = response.json()
            logger.info(f"Retrieved execution: {execution_id}")
            return execution
            
        except Exception as e:
            logger.error(f"Failed to get execution {execution_id}: {e}")
            raise WorkflowError(f"Failed to get execution: {str(e)}")
    
    async def trigger_webhook(
        self, 
        webhook_path: str, 
        data: Dict[str, Any],
        method: str = "POST"
    ) -> Dict[str, Any]:
        """Trigger a webhook workflow"""
        try:
            webhook_url = f"/webhook/{webhook_path}"
            
            if method.upper() == "POST":
                response = await self.client.post(webhook_url, json=data)
            elif method.upper() == "GET":
                response = await self.client.get(webhook_url, params=data)
            else:
                raise WorkflowError(f"Unsupported webhook method: {method}")
            
            response.raise_for_status()
            
            try:
                result = response.json()
            except:
                result = {"status": "success", "response": response.text}
            
            logger.info(f"Triggered webhook: {webhook_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to trigger webhook {webhook_path}: {e}")
            raise WorkflowError(f"Failed to trigger webhook: {str(e)}")
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global n8n client instance
_n8n_client = None

def get_n8n_client() -> N8nClient:
    """Get or create n8n client instance"""
    global _n8n_client
    if _n8n_client is None:
        _n8n_client = N8nClient()
    return _n8n_client
