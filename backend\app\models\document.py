"""
Document model with processing status and metadata
"""

from typing import Dict, Any
from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey, JSON, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from app.db.database import Base


class Document(Base):
    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)

    # Document information
    title = Column(String, nullable=False)
    filename = Column(String, nullable=False)
    content = Column(Text, nullable=True)
    content_hash = Column(String, nullable=True, index=True)

    # File information
    file_path = Column(String, nullable=True)
    file_type = Column(String, nullable=False)
    mime_type = Column(String, nullable=True)
    file_size = Column(Integer, nullable=False)

    # Processing status
    status = Column(String, default="processing", index=True)  # processing, processed, failed
    processing_error = Column(Text, nullable=True)
    processing_time = Column(Integer, nullable=True)  # seconds

    # Chunking information
    chunk_count = Column(Integer, default=0)
    embedding_model = Column(String, nullable=True)

    # RagFlow integration
    ragflow_document_id = Column(String, nullable=True)
    ragflow_kb_id = Column(String, nullable=True)

    # Metadata
    metadata = Column(JSON, default=dict)
    tags = Column(JSON, default=list)

    # Access control
    is_public = Column(Boolean, default=False)
    shared_with = Column(JSON, default=list)  # List of user IDs

    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    tenant = relationship("Tenant", back_populates="documents")
    user = relationship("User", back_populates="documents")

    def get_metadata(self) -> Dict[str, Any]:
        """Get document metadata"""
        return self.metadata or {}

    def get_tags(self) -> list:
        """Get document tags"""
        return self.tags or []

    def add_tag(self, tag: str):
        """Add tag to document"""
        current_tags = self.get_tags()
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = current_tags

    def remove_tag(self, tag: str):
        """Remove tag from document"""
        current_tags = self.get_tags()
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = current_tags

    def is_accessible_by(self, user_id: str) -> bool:
        """Check if document is accessible by user"""
        # Owner can always access
        if str(self.user_id) == str(user_id):
            return True

        # Public documents are accessible by all tenant users
        if self.is_public:
            return True

        # Check if explicitly shared
        shared_users = self.shared_with or []
        return str(user_id) in [str(uid) for uid in shared_users]

    def share_with_user(self, user_id: str):
        """Share document with user"""
        shared_users = self.shared_with or []
        if str(user_id) not in [str(uid) for uid in shared_users]:
            shared_users.append(str(user_id))
            self.shared_with = shared_users

    def unshare_with_user(self, user_id: str):
        """Unshare document with user"""
        shared_users = self.shared_with or []
        self.shared_with = [uid for uid in shared_users if str(uid) != str(user_id)]

    def mark_as_processed(self, chunk_count: int, processing_time: int):
        """Mark document as successfully processed"""
        from datetime import datetime
        self.status = "processed"
        self.chunk_count = chunk_count
        self.processing_time = processing_time
        self.processed_at = datetime.utcnow()
        self.processing_error = None

    def mark_as_failed(self, error_message: str):
        """Mark document as failed to process"""
        self.status = "failed"
        self.processing_error = error_message
        self.processed_at = None

    def get_file_size_mb(self) -> float:
        """Get file size in MB"""
        return round(self.file_size / (1024 * 1024), 2) if self.file_size else 0

    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id),
            "user_id": str(self.user_id),
            "title": self.title,
            "filename": self.filename,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "file_size": self.file_size,
            "file_size_mb": self.get_file_size_mb(),
            "status": self.status,
            "processing_error": self.processing_error,
            "processing_time": self.processing_time,
            "chunk_count": self.chunk_count,
            "embedding_model": self.embedding_model,
            "metadata": self.get_metadata(),
            "tags": self.get_tags(),
            "is_public": self.is_public,
            "shared_with": self.shared_with or [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None
        }
