#!/bin/bash

# AIthentiqMind Health Check Script
# Comprehensive health monitoring for production deployment

set -e

# Configuration
NAMESPACE="aithentiqmind"
TIMEOUT=30

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Global health status
OVERALL_HEALTH=0

# Check Kubernetes cluster connectivity
check_cluster() {
    log_info "Checking Kubernetes cluster connectivity..."
    
    if kubectl cluster-info &> /dev/null; then
        log_success "Kubernetes cluster is accessible"
    else
        log_error "Cannot connect to Kubernetes cluster"
        OVERALL_HEALTH=1
        return 1
    fi
    
    # Check namespace
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        log_success "Namespace '$NAMESPACE' exists"
    else
        log_error "Namespace '$NAMESPACE' not found"
        OVERALL_HEALTH=1
        return 1
    fi
}

# Check pod health
check_pods() {
    log_info "Checking pod health..."
    
    # Get all pods in namespace
    PODS=$(kubectl get pods -n $NAMESPACE --no-headers 2>/dev/null || echo "")
    
    if [ -z "$PODS" ]; then
        log_error "No pods found in namespace $NAMESPACE"
        OVERALL_HEALTH=1
        return 1
    fi
    
    # Check each pod
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            POD_NAME=$(echo $line | awk '{print $1}')
            POD_STATUS=$(echo $line | awk '{print $3}')
            POD_READY=$(echo $line | awk '{print $2}')
            
            if [ "$POD_STATUS" = "Running" ]; then
                log_success "Pod $POD_NAME is running ($POD_READY)"
            else
                log_error "Pod $POD_NAME is not running (Status: $POD_STATUS)"
                OVERALL_HEALTH=1
            fi
        fi
    done <<< "$PODS"
}

# Check service endpoints
check_services() {
    log_info "Checking service endpoints..."
    
    # List of services to check
    SERVICES=("postgres-service" "redis-service" "qdrant-service" "minio-service" "n8n-service" "backend-service" "frontend-service")
    
    for SERVICE in "${SERVICES[@]}"; do
        if kubectl get service $SERVICE -n $NAMESPACE &> /dev/null; then
            ENDPOINT=$(kubectl get endpoints $SERVICE -n $NAMESPACE -o jsonpath='{.subsets[0].addresses[0].ip}' 2>/dev/null || echo "")
            if [ -n "$ENDPOINT" ]; then
                log_success "Service $SERVICE has endpoint: $ENDPOINT"
            else
                log_warning "Service $SERVICE has no endpoints"
                OVERALL_HEALTH=1
            fi
        else
            log_error "Service $SERVICE not found"
            OVERALL_HEALTH=1
        fi
    done
}

# Check application health endpoints
check_health_endpoints() {
    log_info "Checking application health endpoints..."
    
    # Get service IPs
    BACKEND_IP=$(kubectl get service backend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}' 2>/dev/null || echo "")
    FRONTEND_IP=$(kubectl get service frontend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}' 2>/dev/null || echo "")
    
    # Check backend health
    if [ -n "$BACKEND_IP" ]; then
        if kubectl run health-check-backend --rm -i --restart=Never --image=curlimages/curl --timeout=${TIMEOUT}s -- curl -f -s http://$BACKEND_IP:8000/health &> /dev/null; then
            log_success "Backend health endpoint is responding"
        else
            log_error "Backend health endpoint is not responding"
            OVERALL_HEALTH=1
        fi
    else
        log_error "Cannot get backend service IP"
        OVERALL_HEALTH=1
    fi
    
    # Check frontend health
    if [ -n "$FRONTEND_IP" ]; then
        if kubectl run health-check-frontend --rm -i --restart=Never --image=curlimages/curl --timeout=${TIMEOUT}s -- curl -f -s http://$FRONTEND_IP:3000/api/health &> /dev/null; then
            log_success "Frontend health endpoint is responding"
        else
            log_error "Frontend health endpoint is not responding"
            OVERALL_HEALTH=1
        fi
    else
        log_error "Cannot get frontend service IP"
        OVERALL_HEALTH=1
    fi
}

# Check database connectivity
check_database() {
    log_info "Checking database connectivity..."
    
    # Test PostgreSQL connection
    if kubectl exec -n $NAMESPACE deployment/postgres -- pg_isready -U postgres &> /dev/null; then
        log_success "PostgreSQL is ready"
    else
        log_error "PostgreSQL is not ready"
        OVERALL_HEALTH=1
    fi
    
    # Test Redis connection
    if kubectl exec -n $NAMESPACE deployment/redis -- redis-cli ping &> /dev/null; then
        log_success "Redis is responding"
    else
        log_error "Redis is not responding"
        OVERALL_HEALTH=1
    fi
}

# Check storage
check_storage() {
    log_info "Checking persistent storage..."
    
    # Check PVCs
    PVCS=$(kubectl get pvc -n $NAMESPACE --no-headers 2>/dev/null || echo "")
    
    if [ -z "$PVCS" ]; then
        log_warning "No PVCs found"
        return 0
    fi
    
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            PVC_NAME=$(echo $line | awk '{print $1}')
            PVC_STATUS=$(echo $line | awk '{print $2}')
            
            if [ "$PVC_STATUS" = "Bound" ]; then
                log_success "PVC $PVC_NAME is bound"
            else
                log_error "PVC $PVC_NAME is not bound (Status: $PVC_STATUS)"
                OVERALL_HEALTH=1
            fi
        fi
    done <<< "$PVCS"
}

# Check ingress
check_ingress() {
    log_info "Checking ingress configuration..."
    
    INGRESS=$(kubectl get ingress -n $NAMESPACE --no-headers 2>/dev/null || echo "")
    
    if [ -z "$INGRESS" ]; then
        log_warning "No ingress found"
        return 0
    fi
    
    while IFS= read -r line; do
        if [ -n "$line" ]; then
            INGRESS_NAME=$(echo $line | awk '{print $1}')
            INGRESS_HOSTS=$(echo $line | awk '{print $3}')
            INGRESS_ADDRESS=$(echo $line | awk '{print $4}')
            
            if [ -n "$INGRESS_ADDRESS" ] && [ "$INGRESS_ADDRESS" != "<none>" ]; then
                log_success "Ingress $INGRESS_NAME has address: $INGRESS_ADDRESS"
            else
                log_warning "Ingress $INGRESS_NAME has no external address"
            fi
        fi
    done <<< "$INGRESS"
}

# Check SSL certificates
check_ssl() {
    log_info "Checking SSL certificates..."
    
    # Check if cert-manager is installed
    if kubectl get namespace cert-manager &> /dev/null; then
        # Check certificates
        CERTS=$(kubectl get certificates -n $NAMESPACE --no-headers 2>/dev/null || echo "")
        
        if [ -z "$CERTS" ]; then
            log_warning "No SSL certificates found"
            return 0
        fi
        
        while IFS= read -r line; do
            if [ -n "$line" ]; then
                CERT_NAME=$(echo $line | awk '{print $1}')
                CERT_READY=$(echo $line | awk '{print $2}')
                
                if [ "$CERT_READY" = "True" ]; then
                    log_success "SSL certificate $CERT_NAME is ready"
                else
                    log_error "SSL certificate $CERT_NAME is not ready"
                    OVERALL_HEALTH=1
                fi
            fi
        done <<< "$CERTS"
    else
        log_warning "cert-manager not installed"
    fi
}

# Check resource usage
check_resources() {
    log_info "Checking resource usage..."
    
    # Check if metrics server is available
    if kubectl top nodes &> /dev/null; then
        log_info "Node resource usage:"
        kubectl top nodes
        echo
        
        log_info "Pod resource usage:"
        kubectl top pods -n $NAMESPACE 2>/dev/null || log_warning "Cannot get pod metrics"
    else
        log_warning "Metrics server not available"
    fi
}

# Check external dependencies
check_external_deps() {
    log_info "Checking external dependencies..."
    
    # Test external connectivity from a pod
    if kubectl run connectivity-test --rm -i --restart=Never --image=curlimages/curl --timeout=${TIMEOUT}s -- curl -f -s https://api.openai.com/v1/models &> /dev/null; then
        log_success "External connectivity (OpenAI API) is working"
    else
        log_warning "Cannot reach external services (check network/firewall)"
    fi
}

# Generate health report
generate_report() {
    log_info "Generating health report..."
    
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    REPORT_FILE="health-report-$(date '+%Y%m%d-%H%M%S').txt"
    
    {
        echo "AIthentiqMind Health Report"
        echo "Generated: $TIMESTAMP"
        echo "Namespace: $NAMESPACE"
        echo "Overall Health: $([ $OVERALL_HEALTH -eq 0 ] && echo "HEALTHY" || echo "UNHEALTHY")"
        echo
        
        echo "=== Pod Status ==="
        kubectl get pods -n $NAMESPACE -o wide
        echo
        
        echo "=== Service Status ==="
        kubectl get services -n $NAMESPACE
        echo
        
        echo "=== Ingress Status ==="
        kubectl get ingress -n $NAMESPACE
        echo
        
        echo "=== PVC Status ==="
        kubectl get pvc -n $NAMESPACE
        echo
        
        echo "=== Recent Events ==="
        kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp' | tail -20
        
    } > $REPORT_FILE
    
    log_success "Health report saved to $REPORT_FILE"
}

# Main health check function
main() {
    log_info "Starting AIthentiqMind health check..."
    echo
    
    case "${1:-all}" in
        "all")
            check_cluster
            check_pods
            check_services
            check_health_endpoints
            check_database
            check_storage
            check_ingress
            check_ssl
            check_resources
            check_external_deps
            ;;
        "quick")
            check_cluster
            check_pods
            check_health_endpoints
            ;;
        "pods")
            check_pods
            ;;
        "services")
            check_services
            ;;
        "health")
            check_health_endpoints
            ;;
        "database")
            check_database
            ;;
        "storage")
            check_storage
            ;;
        "ingress")
            check_ingress
            ;;
        "ssl")
            check_ssl
            ;;
        "resources")
            check_resources
            ;;
        "external")
            check_external_deps
            ;;
        "report")
            check_cluster
            check_pods
            check_services
            check_health_endpoints
            check_database
            check_storage
            check_ingress
            check_ssl
            check_resources
            generate_report
            ;;
        *)
            echo "Usage: $0 [all|quick|pods|services|health|database|storage|ingress|ssl|resources|external|report]"
            echo
            echo "Commands:"
            echo "  all       - Complete health check (default)"
            echo "  quick     - Quick health check (cluster, pods, health endpoints)"
            echo "  pods      - Check pod status"
            echo "  services  - Check service endpoints"
            echo "  health    - Check application health endpoints"
            echo "  database  - Check database connectivity"
            echo "  storage   - Check persistent storage"
            echo "  ingress   - Check ingress configuration"
            echo "  ssl       - Check SSL certificates"
            echo "  resources - Check resource usage"
            echo "  external  - Check external dependencies"
            echo "  report    - Generate detailed health report"
            exit 1
            ;;
    esac
    
    echo
    if [ $OVERALL_HEALTH -eq 0 ]; then
        log_success "Overall system health: HEALTHY"
        exit 0
    else
        log_error "Overall system health: UNHEALTHY"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
