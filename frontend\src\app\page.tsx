'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  EnterpriseCard, 
  EnterpriseNav, 
  EnterpriseButton,
  EnterpriseBadge 
} from '@/components/ui/enterprise-components'
import { 
  StatusIndicator, 
  LoadingSpinner, 
  MetricCard, 
  ThemeToggle 
} from '@/components/providers/theme-provider'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalDocuments: 0,
    totalQueries: 0,
    avgTrustScore: 0,
    activeUsers: 0,
    isLoading: true
  })

  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    // Update time every second for live feel
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    
    // Simulate loading enterprise stats
    const loadStats = async () => {
      await new Promise(resolve => setTimeout(resolve, 1500)) // Realistic loading
      setStats({
        totalDocuments: 2847,
        totalQueries: 18293,
        avgTrustScore: 0.94,
        activeUsers: 156,
        isLoading: false
      })
    }

    loadStats()
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 dark:from-slate-950 dark:via-blue-950 dark:to-slate-950">
      {/* Enterprise Navigation */}
      <EnterpriseNav
        brand={
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-white font-semibold text-xl">AIthentiqMind</span>
            </div>
            <StatusIndicator status="online" label="Enterprise Ready" />
          </div>
        }
        links={[
          { href: '/', label: 'Home', active: true },
          { href: '/demo', label: 'Live Demo' },
          { href: '/dashboard', label: 'Dashboard' }
        ]}
        actions={
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-4 text-xs text-blue-200">
              <StatusIndicator status="online" label="All Systems" />
              <span className="text-blue-200">|</span>
              <span>{currentTime.toLocaleTimeString()}</span>
            </div>
            <ThemeToggle />
            <Link href="/demo">
              <EnterpriseButton variant="primary" size="sm">
                Get Started
              </EnterpriseButton>
            </Link>
          </div>
        }
      />

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            {/* Status Badge */}
            <div className="inline-flex items-center space-x-2 glass rounded-full px-4 py-2 mb-8 animate-fade-in">
              <StatusIndicator status="online" />
              <span className="text-sm text-gray-300">System Status: All Services Operational</span>
              <span className="text-xs text-gray-400">
                {currentTime.toLocaleTimeString()}
              </span>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight animate-slide-up">
              Enterprise
              <span className="text-gradient">
                {' '}RAG Platform
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed animate-slide-up" style={{animationDelay: '0.2s'}}>
              Advanced Retrieval-Augmented Generation with enterprise-grade security, 
              multi-tenant architecture, and intelligent trust scoring for mission-critical applications.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16 animate-slide-up" style={{animationDelay: '0.4s'}}>
              <Link href="/demo">
                <EnterpriseButton variant="primary" size="lg" className="shadow-glow-blue">
                  Experience Live Demo
                </EnterpriseButton>
              </Link>
              <a href="http://localhost:8000/docs" target="_blank">
                <EnterpriseButton variant="secondary" size="lg">
                  View API Documentation
                </EnterpriseButton>
              </a>
            </div>

            {/* Real-time Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto animate-fade-in" style={{animationDelay: '0.6s'}}>
              <MetricCard
                title="Documents Processed"
                value={stats.isLoading ? <LoadingSpinner size="sm" /> : stats.totalDocuments.toLocaleString()}
                change="+12% this month"
                changeType="positive"
                className="glass hover:shadow-glow-blue"
              />

              <MetricCard
                title="Queries Answered"
                value={stats.isLoading ? <LoadingSpinner size="sm" /> : stats.totalQueries.toLocaleString()}
                change="+8% this week"
                changeType="positive"
                className="glass hover:shadow-glow-green"
              />

              <MetricCard
                title="Trust Score"
                value={stats.isLoading ? <LoadingSpinner size="sm" /> : `${(stats.avgTrustScore * 100).toFixed(1)}%`}
                change="+2.1% accuracy"
                changeType="positive"
                className="glass hover:shadow-glow-purple"
              />

              <MetricCard
                title="Active Users"
                value={stats.isLoading ? <LoadingSpinner size="sm" /> : stats.activeUsers.toLocaleString()}
                change="+15% growth"
                changeType="positive"
                className="glass hover:shadow-glow-blue"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Enterprise Features */}
      <div className="relative glass-dark border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-4xl font-bold text-white mb-4">
              Enterprise-Grade Architecture
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Built for scale, security, and performance with modern microservices architecture
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <EnterpriseCard 
              className="glass hover:shadow-glow-blue transition-all duration-300 animate-slide-up"
              style={{animationDelay: '0.2s'}}
            >
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">🧠</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">Advanced RAG Engine</h3>
                <p className="text-gray-300 mb-4">
                  Sophisticated retrieval-augmented generation with RagFlow integration, 
                  custom trust scoring, and intelligent document chunking.
                </p>
                <div className="space-y-2 text-sm text-gray-400">
                  <div className="flex items-center justify-between">
                    <span>Multi-modal processing</span>
                    <EnterpriseBadge variant="success" size="sm">Active</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Vector embeddings</span>
                    <EnterpriseBadge variant="info" size="sm">Ready</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Trust scoring</span>
                    <EnterpriseBadge variant="success" size="sm">94.2%</EnterpriseBadge>
                  </div>
                </div>
              </div>
            </EnterpriseCard>

            <EnterpriseCard 
              className="glass hover:shadow-glow-green transition-all duration-300 animate-slide-up"
              style={{animationDelay: '0.4s'}}
            >
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">🏢</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">Multi-Tenant Security</h3>
                <p className="text-gray-300 mb-4">
                  Complete tenant isolation with role-based access control, 
                  audit logging, and enterprise-grade security measures.
                </p>
                <div className="space-y-2 text-sm text-gray-400">
                  <div className="flex items-center justify-between">
                    <span>Zero-trust architecture</span>
                    <EnterpriseBadge variant="success" size="sm">Enabled</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>End-to-end encryption</span>
                    <EnterpriseBadge variant="success" size="sm">AES-256</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Audit trails</span>
                    <EnterpriseBadge variant="info" size="sm">Complete</EnterpriseBadge>
                  </div>
                </div>
              </div>
            </EnterpriseCard>

            <EnterpriseCard 
              className="glass hover:shadow-glow-purple transition-all duration-300 animate-slide-up"
              style={{animationDelay: '0.6s'}}
            >
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <span className="text-white text-2xl">⚡</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">Workflow Automation</h3>
                <p className="text-gray-300 mb-4">
                  Integrated n8n automation platform for document processing workflows, 
                  notifications, and business process automation.
                </p>
                <div className="space-y-2 text-sm text-gray-400">
                  <div className="flex items-center justify-between">
                    <span>Visual workflow designer</span>
                    <EnterpriseBadge variant="success" size="sm">Ready</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>200+ integrations</span>
                    <EnterpriseBadge variant="info" size="sm">Available</EnterpriseBadge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Event-driven automation</span>
                    <EnterpriseBadge variant="success" size="sm">Active</EnterpriseBadge>
                  </div>
                </div>
              </div>
            </EnterpriseCard>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative bg-black/20 border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-white font-semibold">AIthentiqMind</span>
              <span className="text-gray-400 text-sm">Enterprise RAG Platform</span>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>© 2024 AIthentiqMind</span>
              <StatusIndicator status="online" label="All systems operational" />
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
