'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'

export default function HomePage() {
  const [stats, setStats] = useState({
    totalDocuments: 0,
    totalQueries: 0,
    avgTrustScore: 0,
    activeUsers: 0,
    isLoading: true
  })

  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    // Update time every second for live feel
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)

    // Simulate loading enterprise stats
    const loadStats = async () => {
      await new Promise(resolve => setTimeout(resolve, 1500)) // Realistic loading
      setStats({
        totalDocuments: 2847,
        totalQueries: 18293,
        avgTrustScore: 0.94,
        activeUsers: 156,
        isLoading: false
      })
    }

    loadStats()
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Navigation */}
      <nav className="relative z-50 bg-white/10 backdrop-blur-md border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">AI</span>
                </div>
                <span className="text-white font-semibold text-xl">AIthentiqMind</span>
              </div>
              <div className="hidden md:flex items-center space-x-1 text-xs text-blue-200">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>Enterprise Ready</span>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-300">
                <Link href="/demo" className="hover:text-white transition-colors">
                  Live Demo
                </Link>
                <a href="http://localhost:8000/docs" target="_blank" className="hover:text-white transition-colors">
                  API Docs
                </a>
                <Link href="/dashboard" className="hover:text-white transition-colors">
                  Dashboard
                </Link>
              </div>
              <Link
                href="/demo"
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            {/* Status Badge */}
            <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-8">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">System Status: All Services Operational</span>
              <span className="text-xs text-gray-400">
                {currentTime.toLocaleTimeString()}
              </span>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              Enterprise
              <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                {' '}RAG Platform
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
              Advanced Retrieval-Augmented Generation with enterprise-grade security,
              multi-tenant architecture, and intelligent trust scoring for mission-critical applications.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Link
                href="/demo"
                className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-xl"
              >
                Experience Live Demo
              </Link>
              <a
                href="http://localhost:8000/docs"
                target="_blank"
                className="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white/20 transition-all duration-200"
              >
                View API Documentation
              </a>
            </div>

            {/* Real-time Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.isLoading ? (
                    <div className="animate-pulse bg-white/20 h-8 w-16 rounded"></div>
                  ) : (
                    stats.totalDocuments.toLocaleString()
                  )}
                </div>
                <div className="text-sm text-gray-300">Documents Processed</div>
                <div className="text-xs text-green-400 mt-1">↗ +12% this month</div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.isLoading ? (
                    <div className="animate-pulse bg-white/20 h-8 w-16 rounded"></div>
                  ) : (
                    stats.totalQueries.toLocaleString()
                  )}
                </div>
                <div className="text-sm text-gray-300">Queries Answered</div>
                <div className="text-xs text-green-400 mt-1">↗ +8% this week</div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.isLoading ? (
                    <div className="animate-pulse bg-white/20 h-8 w-16 rounded"></div>
                  ) : (
                    `${(stats.avgTrustScore * 100).toFixed(1)}%`
                  )}
                </div>
                <div className="text-sm text-gray-300">Trust Score</div>
                <div className="text-xs text-green-400 mt-1">↗ +2.1% accuracy</div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
                <div className="text-3xl font-bold text-white mb-2">
                  {stats.isLoading ? (
                    <div className="animate-pulse bg-white/20 h-8 w-16 rounded"></div>
                  ) : (
                    stats.activeUsers.toLocaleString()
                  )}
                </div>
                <div className="text-sm text-gray-300">Active Users</div>
                <div className="text-xs text-green-400 mt-1">↗ +15% growth</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="relative bg-black/20 border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-white font-semibold">AIthentiqMind</span>
              <span className="text-gray-400 text-sm">Enterprise RAG Platform</span>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>© 2024 AIthentiqMind</span>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
