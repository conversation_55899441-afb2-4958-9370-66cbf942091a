'use client'

import { useUser } from '@auth0/nextjs-auth0/client'
import { redirect } from 'next/navigation'
import { useEffect } from 'react'

import { LandingPage } from '@/components/landing/landing-page'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export default function HomePage() {
  const { user, isLoading } = useUser()

  useEffect(() => {
    if (user && !isLoading) {
      redirect('/dashboard')
    }
  }, [user, isLoading])

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (user) {
    return null // Will redirect to dashboard
  }

  return <LandingPage />
}
