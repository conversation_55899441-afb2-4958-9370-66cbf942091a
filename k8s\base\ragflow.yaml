apiVersion: apps/v1
kind: Deployment
metadata:
  name: ragflow
  namespace: aithentiqmind
  labels:
    app: ragflow
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ragflow
  template:
    metadata:
      labels:
        app: ragflow
    spec:
      containers:
      - name: ragflow
        image: infiniflow/ragflow:latest
        ports:
        - containerPort: 9380
        env:
        - name: DATABASE_URL
          value: "****************************************************************/ragflow"
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: aithentiqmind-secrets
              key: DATABASE_PASSWORD
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@redis-service:6379/1"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: aithentiqmind-secrets
              key: REDIS_PASSWORD
        - name: MINIO_ENDPOINT
          value: "minio-service:9000"
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: aithentiqmind-secrets
              key: MINIO_ACCESS_KEY
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: aithentiqmind-secrets
              key: MINIO_SECRET_KEY
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: aithentiqmind-secrets
              key: OPENAI_API_KEY
        - name: HF_ENDPOINT
          value: "https://huggingface.co"
        - name: INFINITY_ENDPOINT
          value: "http://infinity-service:7997"
        volumeMounts:
        - name: ragflow-data
          mountPath: /ragflow/data
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 9380
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 9380
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: ragflow-data
        persistentVolumeClaim:
          claimName: ragflow-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: ragflow-service
  namespace: aithentiqmind
  labels:
    app: ragflow
spec:
  selector:
    app: ragflow
  ports:
  - name: http
    port: 9380
    targetPort: 9380
  type: ClusterIP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ragflow-pvc
  namespace: aithentiqmind
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
---
# Infinity embedding service for RagFlow
apiVersion: apps/v1
kind: Deployment
metadata:
  name: infinity
  namespace: aithentiqmind
  labels:
    app: infinity
spec:
  replicas: 1
  selector:
    matchLabels:
      app: infinity
  template:
    metadata:
      labels:
        app: infinity
    spec:
      containers:
      - name: infinity
        image: michaelf34/infinity:latest
        ports:
        - containerPort: 7997
        env:
        - name: MODEL_ID
          value: "BAAI/bge-large-en-v1.5"
        - name: BATCH_SIZE
          value: "32"
        - name: REVISION
          value: "main"
        - name: TRUST_REMOTE_CODE
          value: "true"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 7997
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 7997
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: infinity-service
  namespace: aithentiqmind
  labels:
    app: infinity
spec:
  selector:
    app: infinity
  ports:
  - name: http
    port: 7997
    targetPort: 7997
  type: ClusterIP
