# Database Configuration
DATABASE_URL=postgresql://postgres:postgres_password@localhost:5432/aithentiq_db
REDIS_URL=redis://localhost:6379/0

# Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=

# Object Storage (MinIO/S3)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET_NAME=aithentiq-documents
MINIO_SECURE=false

# n8n Configuration
N8N_WEBHOOK_URL=http://localhost:5678/webhook
N8N_API_KEY=
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123

# AI/LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_EMBEDDING_MODEL=text-embedding-ada-002

# Ollama Configuration (Alternative to OpenAI)
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_EMBEDDING_MODEL=nomic-embed-text

# Authentication (Auth0)
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_CLIENT_ID=your_auth0_client_id
AUTH0_CLIENT_SECRET=your_auth0_client_secret
AUTH0_AUDIENCE=https://your-api-identifier
JWT_SECRET=your_jwt_secret_key_here

# Application Configuration
APP_NAME=AIthentiqMind
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Security
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
SESSION_TIMEOUT=3600

# Monitoring & Analytics
SENTRY_DSN=
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# File Upload Limits
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,docx,txt,md,html

# Trust Scoring Configuration
TRUST_SCORE_THRESHOLD=0.7
MIN_SOURCE_DIVERSITY=2
RECENCY_WEIGHT=0.3

# Workflow Configuration
DEFAULT_WORKFLOW_TIMEOUT=300
MAX_CONCURRENT_WORKFLOWS=10

# Development/Testing
TEST_DATABASE_URL=postgresql://postgres:postgres_password@localhost:5432/aithentiq_test_db
PYTEST_WORKERS=4
