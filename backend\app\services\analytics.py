"""
Analytics service for generating insights and reports
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_, or_
from sqlalchemy.sql import text

from app.core.logging import get_logger
from app.models.query import Query
from app.models.document import Document
from app.models.user import User
from app.models.tenant import Tenant
from app.models.trust_score import TrustScore
from app.models.audit import AuditLog

logger = get_logger(__name__)


class AnalyticsService:
    """Service for generating analytics and insights"""
    
    def __init__(self):
        self.logger = get_logger("analytics")
    
    async def get_dashboard_stats(
        self, 
        tenant_id: str, 
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get comprehensive dashboard statistics"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Basic counts
            total_documents = await self._count_documents(tenant_id, db)
            total_queries = await self._count_queries(tenant_id, db)
            total_users = await self._count_users(tenant_id, db)
            
            # Period-specific counts
            period_documents = await self._count_documents(tenant_id, db, start_date)
            period_queries = await self._count_queries(tenant_id, db, start_date)
            
            # Trust score analytics
            trust_stats = await self._get_trust_score_stats(tenant_id, db, start_date)
            
            # Performance metrics
            performance_stats = await self._get_performance_stats(tenant_id, db, start_date)
            
            # Usage trends
            usage_trends = await self._get_usage_trends(tenant_id, db, days)
            
            # Top queries and documents
            top_queries = await self._get_top_queries(tenant_id, db, start_date)
            top_documents = await self._get_top_documents(tenant_id, db, start_date)
            
            return {
                "period_days": days,
                "totals": {
                    "documents": total_documents,
                    "queries": total_queries,
                    "users": total_users
                },
                "period_stats": {
                    "documents": period_documents,
                    "queries": period_queries,
                    "avg_queries_per_day": round(period_queries / days, 1) if days > 0 else 0
                },
                "trust_analytics": trust_stats,
                "performance": performance_stats,
                "trends": usage_trends,
                "top_content": {
                    "queries": top_queries,
                    "documents": top_documents
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard stats: {e}")
            return {"error": str(e)}
    
    async def get_trust_score_analytics(
        self, 
        tenant_id: str, 
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get detailed trust score analytics"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Trust score distribution
            distribution = await self._get_trust_score_distribution(tenant_id, db, start_date)
            
            # Trust score trends over time
            trends = await self._get_trust_score_trends(tenant_id, db, days)
            
            # Factor analysis
            factor_analysis = await self._get_trust_factor_analysis(tenant_id, db, start_date)
            
            # Low trust score queries
            low_trust_queries = await self._get_low_trust_queries(tenant_id, db, start_date)
            
            return {
                "period_days": days,
                "distribution": distribution,
                "trends": trends,
                "factor_analysis": factor_analysis,
                "low_trust_queries": low_trust_queries,
                "recommendations": self._generate_trust_recommendations(factor_analysis)
            }
            
        except Exception as e:
            logger.error(f"Failed to get trust score analytics: {e}")
            return {"error": str(e)}
    
    async def get_user_analytics(
        self, 
        tenant_id: str, 
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get user behavior analytics"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Active users
            active_users = await self._get_active_users(tenant_id, db, start_date)
            
            # User query patterns
            query_patterns = await self._get_user_query_patterns(tenant_id, db, start_date)
            
            # User engagement metrics
            engagement_metrics = await self._get_user_engagement(tenant_id, db, start_date)
            
            return {
                "period_days": days,
                "active_users": active_users,
                "query_patterns": query_patterns,
                "engagement": engagement_metrics
            }
            
        except Exception as e:
            logger.error(f"Failed to get user analytics: {e}")
            return {"error": str(e)}
    
    async def get_document_analytics(
        self, 
        tenant_id: str, 
        days: int = 30,
        db: AsyncSession = None
    ) -> Dict[str, Any]:
        """Get document usage analytics"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Document usage stats
            usage_stats = await self._get_document_usage_stats(tenant_id, db, start_date)
            
            # Document performance
            performance_stats = await self._get_document_performance(tenant_id, db, start_date)
            
            # Unused documents
            unused_documents = await self._get_unused_documents(tenant_id, db, start_date)
            
            return {
                "period_days": days,
                "usage_stats": usage_stats,
                "performance": performance_stats,
                "unused_documents": unused_documents
            }
            
        except Exception as e:
            logger.error(f"Failed to get document analytics: {e}")
            return {"error": str(e)}
    
    # Helper methods
    async def _count_documents(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime = None
    ) -> int:
        """Count documents for tenant"""
        query = select(func.count(Document.id)).where(Document.tenant_id == tenant_id)
        if since:
            query = query.where(Document.created_at >= since)
        
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _count_queries(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime = None
    ) -> int:
        """Count queries for tenant"""
        query = select(func.count(Query.id)).where(Query.tenant_id == tenant_id)
        if since:
            query = query.where(Query.created_at >= since)
        
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _count_users(self, tenant_id: str, db: AsyncSession) -> int:
        """Count users for tenant"""
        query = select(func.count(User.id)).where(User.tenant_id == tenant_id)
        result = await db.execute(query)
        return result.scalar() or 0
    
    async def _get_trust_score_stats(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime
    ) -> Dict[str, Any]:
        """Get trust score statistics"""
        query = select(
            func.avg(TrustScore.overall_score),
            func.min(TrustScore.overall_score),
            func.max(TrustScore.overall_score),
            func.count(TrustScore.id)
        ).join(Query).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since
        )
        
        result = await db.execute(query)
        avg_score, min_score, max_score, count = result.first()
        
        return {
            "average": round(float(avg_score or 0), 3),
            "minimum": round(float(min_score or 0), 3),
            "maximum": round(float(max_score or 0), 3),
            "total_scored_queries": count or 0
        }
    
    async def _get_performance_stats(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime
    ) -> Dict[str, Any]:
        """Get performance statistics"""
        query = select(
            func.avg(Query.response_time),
            func.min(Query.response_time),
            func.max(Query.response_time),
            func.avg(Query.tokens_used)
        ).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since,
            Query.response_time.isnot(None)
        )
        
        result = await db.execute(query)
        avg_time, min_time, max_time, avg_tokens = result.first()
        
        return {
            "avg_response_time": round(float(avg_time or 0), 2),
            "min_response_time": round(float(min_time or 0), 2),
            "max_response_time": round(float(max_time or 0), 2),
            "avg_tokens_used": round(float(avg_tokens or 0), 0)
        }
    
    async def _get_usage_trends(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        days: int
    ) -> List[Dict[str, Any]]:
        """Get daily usage trends"""
        # Get daily query counts for the period
        query = select(
            func.date(Query.created_at).label('date'),
            func.count(Query.id).label('query_count')
        ).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= datetime.utcnow() - timedelta(days=days)
        ).group_by(
            func.date(Query.created_at)
        ).order_by(
            func.date(Query.created_at)
        )
        
        result = await db.execute(query)
        trends = []
        
        for date, count in result:
            trends.append({
                "date": date.isoformat(),
                "queries": count
            })
        
        return trends
    
    async def _get_top_queries(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get most frequent queries"""
        # This is a simplified version - in practice, you'd want to group similar queries
        query = select(
            Query.question,
            func.count(Query.id).label('count'),
            func.avg(TrustScore.overall_score).label('avg_trust')
        ).outerjoin(TrustScore).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since
        ).group_by(
            Query.question
        ).order_by(
            desc('count')
        ).limit(limit)
        
        result = await db.execute(query)
        top_queries = []
        
        for question, count, avg_trust in result:
            top_queries.append({
                "question": question,
                "count": count,
                "avg_trust_score": round(float(avg_trust or 0), 3)
            })
        
        return top_queries
    
    async def _get_top_documents(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get most referenced documents"""
        # This would require tracking document usage in queries
        # For now, return most recently created documents
        query = select(
            Document.title,
            Document.filename,
            Document.created_at,
            Document.chunk_count
        ).where(
            Document.tenant_id == tenant_id,
            Document.status == "processed"
        ).order_by(
            desc(Document.created_at)
        ).limit(limit)
        
        result = await db.execute(query)
        top_documents = []
        
        for title, filename, created_at, chunk_count in result:
            top_documents.append({
                "title": title,
                "filename": filename,
                "created_at": created_at.isoformat(),
                "chunk_count": chunk_count or 0
            })
        
        return top_documents
    
    async def _get_trust_score_distribution(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime
    ) -> Dict[str, int]:
        """Get trust score distribution"""
        query = select(TrustScore.overall_score).join(Query).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since
        )
        
        result = await db.execute(query)
        scores = [row[0] for row in result]
        
        # Create distribution buckets
        distribution = {
            "very_low": len([s for s in scores if s < 0.4]),
            "low": len([s for s in scores if 0.4 <= s < 0.6]),
            "medium": len([s for s in scores if 0.6 <= s < 0.8]),
            "high": len([s for s in scores if s >= 0.8])
        }
        
        return distribution
    
    async def _get_trust_score_trends(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        days: int
    ) -> List[Dict[str, Any]]:
        """Get trust score trends over time"""
        query = select(
            func.date(Query.created_at).label('date'),
            func.avg(TrustScore.overall_score).label('avg_trust')
        ).join(TrustScore).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= datetime.utcnow() - timedelta(days=days)
        ).group_by(
            func.date(Query.created_at)
        ).order_by(
            func.date(Query.created_at)
        )
        
        result = await db.execute(query)
        trends = []
        
        for date, avg_trust in result:
            trends.append({
                "date": date.isoformat(),
                "avg_trust_score": round(float(avg_trust or 0), 3)
            })
        
        return trends
    
    async def _get_trust_factor_analysis(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime
    ) -> Dict[str, Any]:
        """Analyze trust score factors"""
        query = select(
            func.avg(TrustScore.confidence_score),
            func.avg(TrustScore.relevance_score),
            func.avg(TrustScore.recency_score),
            func.avg(TrustScore.source_diversity_score),
            func.avg(TrustScore.answer_quality_score)
        ).join(Query).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since
        )
        
        result = await db.execute(query)
        conf, rel, rec, div, qual = result.first()
        
        return {
            "confidence": round(float(conf or 0), 3),
            "relevance": round(float(rel or 0), 3),
            "recency": round(float(rec or 0), 3),
            "source_diversity": round(float(div or 0), 3),
            "answer_quality": round(float(qual or 0), 3)
        }
    
    async def _get_low_trust_queries(
        self, 
        tenant_id: str, 
        db: AsyncSession, 
        since: datetime,
        threshold: float = 0.5,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get queries with low trust scores"""
        query = select(
            Query.question,
            TrustScore.overall_score,
            Query.created_at
        ).join(TrustScore).where(
            Query.tenant_id == tenant_id,
            Query.created_at >= since,
            TrustScore.overall_score < threshold
        ).order_by(
            TrustScore.overall_score
        ).limit(limit)
        
        result = await db.execute(query)
        low_trust_queries = []
        
        for question, score, created_at in result:
            low_trust_queries.append({
                "question": question,
                "trust_score": round(float(score), 3),
                "created_at": created_at.isoformat()
            })
        
        return low_trust_queries
    
    def _generate_trust_recommendations(
        self, 
        factor_analysis: Dict[str, float]
    ) -> List[str]:
        """Generate recommendations based on trust factor analysis"""
        recommendations = []
        
        if factor_analysis.get("confidence", 0) < 0.7:
            recommendations.append("Consider improving document quality and relevance")
        
        if factor_analysis.get("relevance", 0) < 0.7:
            recommendations.append("Review and update document content for better query matching")
        
        if factor_analysis.get("recency", 0) < 0.6:
            recommendations.append("Update outdated documents with fresh information")
        
        if factor_analysis.get("source_diversity", 0) < 0.6:
            recommendations.append("Add more diverse sources to improve answer reliability")
        
        if factor_analysis.get("answer_quality", 0) < 0.7:
            recommendations.append("Consider adjusting AI model parameters or prompts")
        
        return recommendations


# Global analytics service instance
analytics_service = AnalyticsService()


def get_analytics_service() -> AnalyticsService:
    """Get analytics service instance"""
    return analytics_service
