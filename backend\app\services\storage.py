"""
File storage service with MinIO integration
"""

import asyncio
import os
from typing import Optional, BinaryIO
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class StorageService:
    """File storage service with MinIO backend"""
    
    def __init__(self):
        self.logger = get_logger("storage")
        self.local_storage_path = Path("./storage")
        self.local_storage_path.mkdir(exist_ok=True)
    
    async def store_file(
        self,
        file_content: bytes,
        filename: str,
        tenant_id: str,
        folder: Optional[str] = None
    ) -> str:
        """Store file and return file path"""
        
        try:
            # Create tenant-specific path
            tenant_path = self.local_storage_path / tenant_id
            if folder:
                tenant_path = tenant_path / folder
            
            tenant_path.mkdir(parents=True, exist_ok=True)
            
            # Generate unique filename with timestamp
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            file_path = tenant_path / f"{timestamp}_{filename}"
            
            # Write file
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            # Return relative path
            relative_path = str(file_path.relative_to(self.local_storage_path))
            self.logger.info(f"Stored file: {relative_path}")
            
            return relative_path
            
        except Exception as e:
            self.logger.error(f"Failed to store file {filename}: {e}")
            raise
    
    async def get_file(self, file_path: str, tenant_id: str) -> bytes:
        """Retrieve file content"""
        
        try:
            full_path = self.local_storage_path / file_path
            
            # Security check - ensure path is within tenant directory
            if not str(full_path).startswith(str(self.local_storage_path / tenant_id)):
                raise ValueError("Access denied: Invalid file path")
            
            if not full_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            with open(full_path, "rb") as f:
                content = f.read()
            
            self.logger.info(f"Retrieved file: {file_path}")
            return content
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve file {file_path}: {e}")
            raise
    
    async def delete_file(self, file_path: str, tenant_id: str) -> bool:
        """Delete file"""
        
        try:
            full_path = self.local_storage_path / file_path
            
            # Security check - ensure path is within tenant directory
            if not str(full_path).startswith(str(self.local_storage_path / tenant_id)):
                raise ValueError("Access denied: Invalid file path")
            
            if full_path.exists():
                full_path.unlink()
                self.logger.info(f"Deleted file: {file_path}")
                return True
            else:
                self.logger.warning(f"File not found for deletion: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    async def file_exists(self, file_path: str, tenant_id: str) -> bool:
        """Check if file exists"""
        
        try:
            full_path = self.local_storage_path / file_path
            
            # Security check - ensure path is within tenant directory
            if not str(full_path).startswith(str(self.local_storage_path / tenant_id)):
                return False
            
            return full_path.exists()
            
        except Exception as e:
            self.logger.error(f"Failed to check file existence {file_path}: {e}")
            return False
    
    async def get_file_info(self, file_path: str, tenant_id: str) -> dict:
        """Get file information"""
        
        try:
            full_path = self.local_storage_path / file_path
            
            # Security check - ensure path is within tenant directory
            if not str(full_path).startswith(str(self.local_storage_path / tenant_id)):
                raise ValueError("Access denied: Invalid file path")
            
            if not full_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            stat = full_path.stat()
            
            return {
                "path": file_path,
                "size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "exists": True
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get file info {file_path}: {e}")
            return {
                "path": file_path,
                "exists": False,
                "error": str(e)
            }
    
    async def generate_presigned_url(
        self,
        file_path: str,
        tenant_id: str,
        expiry_hours: int = 1
    ) -> str:
        """Generate presigned URL for file access (placeholder for MinIO integration)"""
        
        # For local storage, return a simple path
        # In production with MinIO, this would generate a proper presigned URL
        return f"/api/v1/documents/download/{file_path}"
    
    async def cleanup_old_files(self, tenant_id: str, days_old: int = 30):
        """Clean up old temporary files"""
        
        try:
            tenant_path = self.local_storage_path / tenant_id
            if not tenant_path.exists():
                return
            
            cutoff_time = datetime.utcnow() - timedelta(days=days_old)
            
            for file_path in tenant_path.rglob("*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    if datetime.fromtimestamp(stat.st_mtime) < cutoff_time:
                        try:
                            file_path.unlink()
                            self.logger.info(f"Cleaned up old file: {file_path}")
                        except Exception as e:
                            self.logger.error(f"Failed to clean up file {file_path}: {e}")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old files for tenant {tenant_id}: {e}")


class MinIOStorageService(StorageService):
    """MinIO-based storage service for production"""
    
    def __init__(self):
        super().__init__()
        self.minio_client = None
        self.bucket_name = settings.MINIO_BUCKET
        
        # Initialize MinIO client if configured
        if hasattr(settings, 'MINIO_ENDPOINT') and settings.MINIO_ENDPOINT:
            self._init_minio_client()
    
    def _init_minio_client(self):
        """Initialize MinIO client"""
        try:
            from minio import Minio
            
            self.minio_client = Minio(
                settings.MINIO_ENDPOINT,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                secure=False  # Set to True for HTTPS
            )
            
            # Create bucket if it doesn't exist
            if not self.minio_client.bucket_exists(self.bucket_name):
                self.minio_client.make_bucket(self.bucket_name)
                self.logger.info(f"Created MinIO bucket: {self.bucket_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MinIO client: {e}")
            self.minio_client = None
    
    async def store_file(
        self,
        file_content: bytes,
        filename: str,
        tenant_id: str,
        folder: Optional[str] = None
    ) -> str:
        """Store file in MinIO"""
        
        if not self.minio_client:
            # Fallback to local storage
            return await super().store_file(file_content, filename, tenant_id, folder)
        
        try:
            # Create object name
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            object_name = f"{tenant_id}/"
            if folder:
                object_name += f"{folder}/"
            object_name += f"{timestamp}_{filename}"
            
            # Upload to MinIO
            from io import BytesIO
            
            self.minio_client.put_object(
                self.bucket_name,
                object_name,
                BytesIO(file_content),
                len(file_content)
            )
            
            self.logger.info(f"Stored file in MinIO: {object_name}")
            return object_name
            
        except Exception as e:
            self.logger.error(f"Failed to store file in MinIO: {e}")
            # Fallback to local storage
            return await super().store_file(file_content, filename, tenant_id, folder)


# Global storage service instance
storage_service = StorageService()


def get_storage_service() -> StorageService:
    """Get storage service instance"""
    return storage_service
