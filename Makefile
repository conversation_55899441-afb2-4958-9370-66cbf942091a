# AIthentiqMind Development Makefile

.PHONY: help setup dev prod test clean logs backup restore

# Default target
help:
	@echo "AIthentiqMind Development Commands"
	@echo "=================================="
	@echo "setup     - Set up development environment"
	@echo "dev       - Start development environment"
	@echo "prod      - Start production environment"
	@echo "test      - Run all tests"
	@echo "test-unit - Run unit tests only"
	@echo "test-int  - Run integration tests only"
	@echo "clean     - Clean up containers and volumes"
	@echo "logs      - Show logs from all services"
	@echo "backup    - Backup databases"
	@echo "restore   - Restore databases from backup"
	@echo "lint      - Run code linting"
	@echo "format    - Format code"
	@echo "docs      - Generate documentation"

# Setup development environment
setup:
	@echo "🚀 Setting up development environment..."
	@powershell -ExecutionPolicy Bypass -File scripts/setup-dev.ps1
	@echo "✅ Setup complete!"

# Start development environment
dev:
	@echo "🔧 Starting development environment..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
	@echo "✅ Development environment started!"
	@echo "Frontend: http://localhost:3000"
	@echo "Backend: http://localhost:8000"
	@echo "n8n: http://localhost:5678"
	@echo "Grafana: http://localhost:3001"

# Start production environment
prod:
	@echo "🚀 Starting production environment..."
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
	@echo "✅ Production environment started!"

# Stop all services
stop:
	@echo "🛑 Stopping all services..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml down

# Run all tests
test:
	@echo "🧪 Running all tests..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend pytest tests/ -v
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm test

# Run unit tests only
test-unit:
	@echo "🧪 Running unit tests..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend pytest tests/unit/ -v
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm run test:unit

# Run integration tests only
test-int:
	@echo "🧪 Running integration tests..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend pytest tests/integration/ -v
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm run test:integration

# Clean up containers and volumes
clean:
	@echo "🧹 Cleaning up..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v
	docker-compose -f docker-compose.yml -f docker-compose.prod.yml down -v
	docker system prune -f
	@echo "✅ Cleanup complete!"

# Show logs from all services
logs:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f

# Show logs from specific service
logs-%:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f $*

# Backup databases
backup:
	@echo "💾 Creating database backups..."
	@mkdir -p backups/postgres backups/qdrant
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec postgres pg_dump -U postgres aithentiq_db > backups/postgres/aithentiq_db_$(shell date +%Y%m%d_%H%M%S).sql
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec qdrant curl -X POST "http://localhost:6333/collections/backup" > backups/qdrant/backup_$(shell date +%Y%m%d_%H%M%S).json
	@echo "✅ Backup complete!"

# Restore databases from backup
restore:
	@echo "🔄 Restoring databases..."
	@echo "Please specify backup file: make restore-postgres FILE=backup.sql"

restore-postgres:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec -T postgres psql -U postgres aithentiq_db < $(FILE)

# Run code linting
lint:
	@echo "🔍 Running code linting..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend flake8 .
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend black --check .
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm run lint

# Format code
format:
	@echo "✨ Formatting code..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend black .
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend isort .
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm run format

# Generate documentation
docs:
	@echo "📚 Generating documentation..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend python -m sphinx.cmd.build -b html docs/ docs/_build/
	@echo "✅ Documentation generated in docs/_build/"

# Database migrations
migrate:
	@echo "🔄 Running database migrations..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend alembic upgrade head

# Create new migration
migration:
	@echo "📝 Creating new migration..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend alembic revision --autogenerate -m "$(MSG)"

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend pip install -r requirements.txt
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm install

# Update dependencies
update:
	@echo "🔄 Updating dependencies..."
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend pip install -r requirements.txt --upgrade
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend npm update

# Shell access
shell-backend:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec backend bash

shell-frontend:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend sh

shell-postgres:
	docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec postgres psql -U postgres aithentiq_db

# Health check
health:
	@echo "🏥 Checking service health..."
	@curl -f http://localhost:8000/health || echo "❌ Backend unhealthy"
	@curl -f http://localhost:3000/api/health || echo "❌ Frontend unhealthy"
	@curl -f http://localhost:6333/health || echo "❌ Qdrant unhealthy"
	@echo "✅ Health check complete!"
