{"name": "aithentiqmind-frontend", "version": "1.0.0", "description": "AIthentiqMind Frontend - Enterprise RAG SaaS Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true next build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "@next/font": "14.0.4", "@auth0/nextjs-auth0": "^3.5.0", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "axios": "^1.6.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.294.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-input": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-sheet": "^1.0.4", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-table": "^1.0.4", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-textarea": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-highlight": "^7.0.0", "recharts": "^2.8.0", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7", "immer": "^10.0.3", "socket.io-client": "^4.7.4", "react-use": "^17.4.2", "react-intersection-observer": "^9.5.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8"}, "devDependencies": {"@types/node": "20.10.4", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "@types/react-window": "^1.8.8", "typescript": "5.3.3", "eslint": "8.56.0", "eslint-config-next": "14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "@tailwindcss/prettier-plugin": "^0.0.0-insiders.ff95ac2", "jest": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest-environment-jsdom": "^29.7.0", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.0.4", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}