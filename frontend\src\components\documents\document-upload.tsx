'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, FileText, X, CheckCircle, AlertCircle, Loader2, File } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'

interface UploadFile {
  id: string
  file: File
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error'
  progress: number
  error?: string
  documentId?: string
}

interface DocumentUploadProps {
  onUploadComplete?: (documentId: string) => void
  onUploadError?: (error: string) => void
  maxFiles?: number
  maxFileSize?: number // in bytes
  acceptedFileTypes?: string[]
  className?: string
}

const DEFAULT_ACCEPTED_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/markdown',
  'text/html'
]

const DEFAULT_MAX_SIZE = 50 * 1024 * 1024 // 50MB

export function DocumentUpload({
  onUploadComplete,
  onUploadError,
  maxFiles = 10,
  maxFileSize = DEFAULT_MAX_SIZE,
  acceptedFileTypes = DEFAULT_ACCEPTED_TYPES,
  className
}: DocumentUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [isDragActive, setIsDragActive] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
      )
      onUploadError?.(errors.join('\n'))
    }

    // Add accepted files to upload queue
    const newFiles: UploadFile[] = acceptedFiles.map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      status: 'pending',
      progress: 0
    }))

    setUploadFiles(prev => [...prev, ...newFiles])

    // Start uploading files
    newFiles.forEach(uploadFile => {
      startUpload(uploadFile)
    })
  }, [onUploadError])

  const { getRootProps, getInputProps, isDragActive: dropzoneActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxSize: maxFileSize,
    maxFiles: maxFiles - uploadFiles.length,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
  })

  const startUpload = async (uploadFile: UploadFile) => {
    try {
      // Update status to uploading
      updateFileStatus(uploadFile.id, 'uploading', 0)

      // Create FormData
      const formData = new FormData()
      formData.append('file', uploadFile.file)

      // Upload with progress tracking
      const xhr = new XMLHttpRequest()

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          updateFileStatus(uploadFile.id, 'uploading', progress)
        }
      })

      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText)
            updateFileStatus(uploadFile.id, 'processing', 100)
            
            // Poll for processing completion
            pollProcessingStatus(uploadFile.id, response.document_id)
          } catch (error) {
            updateFileStatus(uploadFile.id, 'error', 0, 'Invalid response from server')
          }
        } else {
          updateFileStatus(uploadFile.id, 'error', 0, `Upload failed: ${xhr.statusText}`)
        }
      })

      xhr.addEventListener('error', () => {
        updateFileStatus(uploadFile.id, 'error', 0, 'Network error during upload')
      })

      xhr.open('POST', '/api/proxy/documents/upload')
      xhr.send(formData)

    } catch (error) {
      updateFileStatus(uploadFile.id, 'error', 0, error instanceof Error ? error.message : 'Upload failed')
    }
  }

  const pollProcessingStatus = async (fileId: string, documentId: string) => {
    try {
      const response = await fetch(`/api/proxy/documents/${documentId}/status`)
      
      if (!response.ok) {
        throw new Error(`Status check failed: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.status === 'processed') {
        updateFileStatus(fileId, 'completed', 100, undefined, documentId)
        onUploadComplete?.(documentId)
      } else if (data.status === 'failed') {
        updateFileStatus(fileId, 'error', 0, data.error || 'Processing failed')
      } else {
        // Still processing, poll again
        setTimeout(() => pollProcessingStatus(fileId, documentId), 2000)
      }
    } catch (error) {
      updateFileStatus(fileId, 'error', 0, error instanceof Error ? error.message : 'Status check failed')
    }
  }

  const updateFileStatus = (
    id: string, 
    status: UploadFile['status'], 
    progress: number, 
    error?: string,
    documentId?: string
  ) => {
    setUploadFiles(prev => prev.map(file => 
      file.id === id 
        ? { ...file, status, progress, error, documentId }
        : file
    ))
  }

  const removeFile = (id: string) => {
    setUploadFiles(prev => prev.filter(file => file.id !== id))
  }

  const clearCompleted = () => {
    setUploadFiles(prev => prev.filter(file => file.status !== 'completed'))
  }

  const getFileIcon = (file: File) => {
    if (file.type.includes('pdf')) return '📄'
    if (file.type.includes('word')) return '📝'
    if (file.type.includes('text')) return '📃'
    return '📄'
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusColor = (status: UploadFile['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'uploading': case 'processing': return 'text-blue-600'
      default: return 'text-muted-foreground'
    }
  }

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error': return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'uploading': case 'processing': return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
      default: return <File className="h-4 w-4 text-muted-foreground" />
    }
  }

  const completedCount = uploadFiles.filter(f => f.status === 'completed').length
  const errorCount = uploadFiles.filter(f => f.status === 'error').length
  const processingCount = uploadFiles.filter(f => f.status === 'uploading' || f.status === 'processing').length

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
              isDragActive || dropzoneActive
                ? "border-primary bg-primary/5"
                : "border-muted-foreground/25 hover:border-muted-foreground/50"
            )}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <div className="space-y-2">
              <p className="text-lg font-medium">
                {isDragActive ? "Drop files here" : "Drag & drop files here"}
              </p>
              <p className="text-sm text-muted-foreground">
                or click to browse files
              </p>
              <p className="text-xs text-muted-foreground">
                Supports PDF, DOCX, TXT, MD, HTML • Max {formatFileSize(maxFileSize)} per file
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Status Summary */}
      {uploadFiles.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Badge variant="outline">
              {uploadFiles.length} file{uploadFiles.length !== 1 ? 's' : ''}
            </Badge>
            {completedCount > 0 && (
              <Badge variant="outline" className="text-green-600 border-green-200">
                {completedCount} completed
              </Badge>
            )}
            {processingCount > 0 && (
              <Badge variant="outline" className="text-blue-600 border-blue-200">
                {processingCount} processing
              </Badge>
            )}
            {errorCount > 0 && (
              <Badge variant="outline" className="text-red-600 border-red-200">
                {errorCount} failed
              </Badge>
            )}
          </div>
          {completedCount > 0 && (
            <Button variant="outline" size="sm" onClick={clearCompleted}>
              Clear Completed
            </Button>
          )}
        </div>
      )}

      {/* File List */}
      {uploadFiles.length > 0 && (
        <Card>
          <CardContent className="p-0">
            <div className="divide-y">
              {uploadFiles.map((uploadFile) => (
                <div key={uploadFile.id} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="text-2xl">{getFileIcon(uploadFile.file)}</div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {uploadFile.file.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatFileSize(uploadFile.file.size)}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(uploadFile.status)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => removeFile(uploadFile.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      {(uploadFile.status === 'uploading' || uploadFile.status === 'processing') && (
                        <div className="mt-2">
                          <Progress value={uploadFile.progress} className="h-2" />
                          <p className="text-xs text-muted-foreground mt-1">
                            {uploadFile.status === 'uploading' ? 'Uploading...' : 'Processing...'}
                            {uploadFile.progress > 0 && ` ${uploadFile.progress}%`}
                          </p>
                        </div>
                      )}
                      
                      {uploadFile.status === 'error' && uploadFile.error && (
                        <Alert className="mt-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="text-xs">
                            {uploadFile.error}
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      {uploadFile.status === 'completed' && (
                        <p className="text-xs text-green-600 mt-1">
                          ✓ Successfully processed
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
