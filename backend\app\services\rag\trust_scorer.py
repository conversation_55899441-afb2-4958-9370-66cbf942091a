"""
Trust scoring system for RAG responses
"""

import asyncio
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import math

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class TrustScorer:
    """Trust scoring system for evaluating RAG response quality"""
    
    def __init__(self):
        self.confidence_weight = settings.CONFIDENCE_WEIGHT
        self.relevance_weight = settings.RELEVANCE_WEIGHT
        self.recency_weight = settings.RECENCY_WEIGHT
        self.min_source_diversity = settings.MIN_SOURCE_DIVERSITY
    
    async def calculate_trust_score(
        self,
        question: str,
        answer: str,
        context_chunks: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive trust score for a RAG response
        
        Args:
            question: Original user question
            answer: Generated answer
            context_chunks: Retrieved context chunks with scores
            
        Returns:
            Dictionary with overall score and component scores
        """
        try:
            # Calculate individual score components
            confidence_score = await self._calculate_confidence_score(context_chunks)
            relevance_score = await self._calculate_relevance_score(question, context_chunks)
            recency_score = await self._calculate_recency_score(context_chunks)
            source_diversity_score = await self._calculate_source_diversity_score(context_chunks)
            answer_quality_score = await self._calculate_answer_quality_score(answer, context_chunks)
            
            # Calculate weighted overall score
            overall_score = (
                confidence_score * self.confidence_weight +
                relevance_score * self.relevance_weight +
                recency_score * self.recency_weight +
                source_diversity_score * 0.15 +  # Fixed weight for diversity
                answer_quality_score * 0.15      # Fixed weight for answer quality
            )
            
            # Ensure score is between 0 and 1
            overall_score = max(0.0, min(1.0, overall_score))
            
            trust_factors = {
                "overall_score": round(overall_score, 3),
                "confidence_score": round(confidence_score, 3),
                "relevance_score": round(relevance_score, 3),
                "recency_score": round(recency_score, 3),
                "source_diversity_score": round(source_diversity_score, 3),
                "answer_quality_score": round(answer_quality_score, 3),
                "source_count": len(context_chunks),
                "unique_documents": len(set(chunk.get("document_id") for chunk in context_chunks)),
                "weights": {
                    "confidence": self.confidence_weight,
                    "relevance": self.relevance_weight,
                    "recency": self.recency_weight,
                    "diversity": 0.15,
                    "quality": 0.15
                }
            }
            
            logger.debug(f"Calculated trust score: {overall_score:.3f}")
            return trust_factors
            
        except Exception as e:
            logger.error(f"Trust score calculation failed: {e}")
            return {
                "overall_score": 0.0,
                "error": str(e),
                "source_count": len(context_chunks) if context_chunks else 0
            }
    
    async def _calculate_confidence_score(self, context_chunks: List[Dict[str, Any]]) -> float:
        """Calculate confidence score based on retrieval scores"""
        if not context_chunks:
            return 0.0
        
        # Use the similarity scores from vector search
        scores = [chunk.get("score", 0.0) for chunk in context_chunks]
        
        if not scores:
            return 0.0
        
        # Calculate weighted average with emphasis on top results
        weights = [1.0 / (i + 1) for i in range(len(scores))]  # 1, 0.5, 0.33, 0.25, ...
        weighted_sum = sum(score * weight for score, weight in zip(scores, weights))
        weight_sum = sum(weights)
        
        confidence = weighted_sum / weight_sum if weight_sum > 0 else 0.0
        
        # Normalize to 0-1 range (assuming similarity scores are typically 0-1)
        return min(1.0, max(0.0, confidence))
    
    async def _calculate_relevance_score(
        self, 
        question: str, 
        context_chunks: List[Dict[str, Any]]
    ) -> float:
        """Calculate relevance score based on keyword overlap and semantic similarity"""
        if not context_chunks:
            return 0.0
        
        # Extract keywords from question
        question_keywords = self._extract_keywords(question.lower())
        
        if not question_keywords:
            return 0.5  # Neutral score if no keywords
        
        relevance_scores = []
        
        for chunk in context_chunks:
            content = chunk.get("content", "").lower()
            
            # Calculate keyword overlap
            content_keywords = self._extract_keywords(content)
            
            if not content_keywords:
                relevance_scores.append(0.0)
                continue
            
            # Jaccard similarity for keyword overlap
            intersection = len(question_keywords.intersection(content_keywords))
            union = len(question_keywords.union(content_keywords))
            keyword_similarity = intersection / union if union > 0 else 0.0
            
            # Boost score if chunk contains exact phrases from question
            phrase_boost = self._calculate_phrase_overlap(question, content)
            
            # Combine keyword similarity and phrase boost
            chunk_relevance = min(1.0, keyword_similarity + phrase_boost * 0.3)
            relevance_scores.append(chunk_relevance)
        
        # Return average relevance score
        return sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0
    
    async def _calculate_recency_score(self, context_chunks: List[Dict[str, Any]]) -> float:
        """Calculate recency score based on document creation/modification dates"""
        if not context_chunks:
            return 0.0
        
        current_time = datetime.now()
        recency_scores = []
        
        for chunk in context_chunks:
            metadata = chunk.get("metadata", {})
            
            # Try to find date information in metadata
            date_fields = ["created", "modified", "creation_date", "modification_date"]
            chunk_date = None
            
            for field in date_fields:
                if field in metadata:
                    try:
                        if isinstance(metadata[field], str):
                            # Try to parse date string
                            chunk_date = datetime.fromisoformat(metadata[field].replace('Z', '+00:00'))
                        elif isinstance(metadata[field], datetime):
                            chunk_date = metadata[field]
                        break
                    except (ValueError, TypeError):
                        continue
            
            if chunk_date is None:
                # No date information, assume neutral recency
                recency_scores.append(0.5)
                continue
            
            # Calculate days since document creation/modification
            days_old = (current_time - chunk_date).days
            
            # Recency score decreases exponentially with age
            # Documents less than 30 days old get high scores
            # Documents older than 365 days get low scores
            if days_old <= 30:
                score = 1.0
            elif days_old <= 90:
                score = 0.8
            elif days_old <= 180:
                score = 0.6
            elif days_old <= 365:
                score = 0.4
            else:
                score = 0.2
            
            recency_scores.append(score)
        
        return sum(recency_scores) / len(recency_scores) if recency_scores else 0.5
    
    async def _calculate_source_diversity_score(self, context_chunks: List[Dict[str, Any]]) -> float:
        """Calculate source diversity score"""
        if not context_chunks:
            return 0.0
        
        # Count unique documents
        unique_documents = set(chunk.get("document_id") for chunk in context_chunks)
        unique_count = len(unique_documents)
        
        # Score based on diversity
        if unique_count >= self.min_source_diversity:
            # High score for good diversity
            diversity_ratio = min(1.0, unique_count / len(context_chunks))
            return 0.5 + (diversity_ratio * 0.5)  # Score between 0.5 and 1.0
        else:
            # Lower score for poor diversity
            return unique_count / self.min_source_diversity * 0.5
    
    async def _calculate_answer_quality_score(
        self, 
        answer: str, 
        context_chunks: List[Dict[str, Any]]
    ) -> float:
        """Calculate answer quality score based on various heuristics"""
        if not answer or not context_chunks:
            return 0.0
        
        quality_factors = []
        
        # 1. Answer length appropriateness
        answer_length = len(answer.split())
        if 20 <= answer_length <= 200:
            length_score = 1.0
        elif 10 <= answer_length < 20 or 200 < answer_length <= 300:
            length_score = 0.7
        elif answer_length < 10:
            length_score = 0.3
        else:
            length_score = 0.5
        
        quality_factors.append(length_score)
        
        # 2. Check for uncertainty indicators
        uncertainty_phrases = [
            "i don't know", "i'm not sure", "i cannot", "i can't",
            "unclear", "uncertain", "maybe", "perhaps", "possibly"
        ]
        
        answer_lower = answer.lower()
        uncertainty_count = sum(1 for phrase in uncertainty_phrases if phrase in answer_lower)
        uncertainty_score = max(0.0, 1.0 - (uncertainty_count * 0.3))
        quality_factors.append(uncertainty_score)
        
        # 3. Check for specific information vs. generic responses
        specific_indicators = [
            "according to", "based on", "the document states",
            "specifically", "in particular", "for example"
        ]
        
        specificity_count = sum(1 for indicator in specific_indicators if indicator in answer_lower)
        specificity_score = min(1.0, specificity_count * 0.3 + 0.4)
        quality_factors.append(specificity_score)
        
        # 4. Check if answer uses information from context
        context_text = " ".join(chunk.get("content", "") for chunk in context_chunks).lower()
        context_keywords = self._extract_keywords(context_text)
        answer_keywords = self._extract_keywords(answer_lower)
        
        if context_keywords and answer_keywords:
            overlap = len(context_keywords.intersection(answer_keywords))
            context_usage_score = min(1.0, overlap / len(answer_keywords) * 2)
        else:
            context_usage_score = 0.5
        
        quality_factors.append(context_usage_score)
        
        # Return average of all quality factors
        return sum(quality_factors) / len(quality_factors)
    
    def _extract_keywords(self, text: str) -> set:
        """Extract meaningful keywords from text"""
        # Remove punctuation and split into words
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Common stop words to exclude
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before',
            'after', 'above', 'below', 'between', 'among', 'this', 'that', 'these',
            'those', 'what', 'which', 'who', 'when', 'where', 'why', 'how', 'all',
            'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such',
            'only', 'own', 'same', 'than', 'too', 'very', 'can', 'will', 'just',
            'should', 'now', 'are', 'was', 'were', 'been', 'have', 'has', 'had',
            'does', 'did', 'would', 'could', 'may', 'might', 'must', 'shall'
        }
        
        # Filter out stop words and short words
        keywords = {word for word in words if word not in stop_words and len(word) >= 3}
        
        return keywords
    
    def _calculate_phrase_overlap(self, question: str, content: str) -> float:
        """Calculate overlap of phrases between question and content"""
        question_lower = question.lower()
        content_lower = content.lower()
        
        # Extract phrases (2-4 words)
        question_words = question_lower.split()
        overlap_count = 0
        total_phrases = 0
        
        for i in range(len(question_words) - 1):
            for phrase_len in [2, 3, 4]:
                if i + phrase_len <= len(question_words):
                    phrase = " ".join(question_words[i:i + phrase_len])
                    total_phrases += 1
                    if phrase in content_lower:
                        overlap_count += 1
        
        return overlap_count / total_phrases if total_phrases > 0 else 0.0
    
    def get_trust_level(self, trust_score: float) -> str:
        """Convert trust score to human-readable level"""
        if trust_score >= 0.8:
            return "High"
        elif trust_score >= 0.6:
            return "Medium"
        elif trust_score >= 0.4:
            return "Low"
        else:
            return "Very Low"
